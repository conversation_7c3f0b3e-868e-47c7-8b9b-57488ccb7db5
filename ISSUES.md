# 🐛 Issues Tracker - GoodTenant Web Client

## 📋 Resumen de Issues

| Severidad | Total | Abiertos | En Progreso | Resueltos |
|-----------|-------|----------|-------------|-----------|
| 🔴 Critical | 0 | 0 | 0 | 0 |
| 🟠 High | 0 | 0 | 0 | 0 |
| 🟡 Medium | 0 | 0 | 0 | 0 |
| 🟢 Low | 0 | 0 | 0 | 0 |
| **Total** | **0** | **0** | **0** | **0** |

---

## 🔴 Critical Issues
> Issues que bloquean funcionalidades principales o causan pérdida de datos

### Plantilla para Critical Issues
```markdown
### [CRIT-001] Título del Issue
- **Estado**: 🔴 Abierto / 🟡 En Progreso / ✅ Resuelto
- **Fecha Reportado**: YYYY-MM-DD
- **Componente**: Nombre del componente/página
- **Descripción**: Descripción detallada del problema
- **Pasos para Reproducir**:
  1. Paso 1
  2. Paso 2
  3. Paso 3
- **Resultado Esperado**: Lo que debería pasar
- **Resultado Actual**: Lo que realmente pasa
- **Impacto**: Descripción del impacto en el usuario
- **Navegador/Dispositivo**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [Si aplica]
- **Logs de Error**: [Si aplica]
- **Prioridad**: P0 (Crítica)
- **Asignado a**: [Nombre]
- **Fecha Resolución**: [Cuando se resuelva]
```

---

## 🟠 High Issues
> Issues que afectan funcionalidades importantes pero tienen workarounds

### Plantilla para High Issues
```markdown
### [HIGH-001] Título del Issue
- **Estado**: 🔴 Abierto / 🟡 En Progreso / ✅ Resuelto
- **Fecha Reportado**: YYYY-MM-DD
- **Componente**: Nombre del componente/página
- **Descripción**: Descripción detallada del problema
- **Pasos para Reproducir**:
  1. Paso 1
  2. Paso 2
  3. Paso 3
- **Resultado Esperado**: Lo que debería pasar
- **Resultado Actual**: Lo que realmente pasa
- **Workaround**: [Si existe una solución temporal]
- **Navegador/Dispositivo**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [Si aplica]
- **Prioridad**: P1 (Alta)
- **Asignado a**: [Nombre]
- **Fecha Resolución**: [Cuando se resuelva]
```

---

## 🟡 Medium Issues
> Issues que afectan la experiencia del usuario pero no bloquean funcionalidades

### Plantilla para Medium Issues
```markdown
### [MED-001] Título del Issue
- **Estado**: 🔴 Abierto / 🟡 En Progreso / ✅ Resuelto
- **Fecha Reportado**: YYYY-MM-DD
- **Componente**: Nombre del componente/página
- **Descripción**: Descripción detallada del problema
- **Pasos para Reproducir**:
  1. Paso 1
  2. Paso 2
  3. Paso 3
- **Resultado Esperado**: Lo que debería pasar
- **Resultado Actual**: Lo que realmente pasa
- **Navegador/Dispositivo**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [Si aplica]
- **Prioridad**: P2 (Media)
- **Asignado a**: [Nombre]
- **Fecha Resolución**: [Cuando se resuelva]
```

---

## 🟢 Low Issues
> Issues menores, mejoras de UI/UX, optimizaciones

### Plantilla para Low Issues
```markdown
### [LOW-001] Título del Issue
- **Estado**: 🔴 Abierto / 🟡 En Progreso / ✅ Resuelto
- **Fecha Reportado**: YYYY-MM-DD
- **Componente**: Nombre del componente/página
- **Descripción**: Descripción detallada del problema
- **Sugerencia de Mejora**: [Si aplica]
- **Navegador/Dispositivo**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [Si aplica]
- **Prioridad**: P3 (Baja)
- **Asignado a**: [Nombre]
- **Fecha Resolución**: [Cuando se resuelva]
```

---

## 📊 Issues por Componente

### 🔐 Autenticación
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 🏠 Dashboard
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 🏢 Propiedades
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 👥 Usuarios/Inquilinos
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 📄 Contratos/Leases
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 💰 Pagos
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 🔧 Mantenimiento
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 📁 Archivos/Documentos
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 🔔 Notificaciones
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 📱 Responsive/Mobile
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

### 🎨 UI/UX General
- Total: 0
- Críticos: 0 | Altos: 0 | Medios: 0 | Bajos: 0

---

## 📝 Notas de Testing

### Navegadores Probados
- [ ] Chrome (Desktop)
- [ ] Firefox (Desktop)
- [ ] Safari (Desktop)
- [ ] Chrome (Mobile)
- [ ] Safari (Mobile)

### Resoluciones Probadas
- [ ] 1920x1080 (Desktop)
- [ ] 1366x768 (Laptop)
- [ ] 768x1024 (Tablet)
- [ ] 375x667 (Mobile)

### Funcionalidades Principales Probadas
- [ ] Login/Logout
- [ ] Dashboard
- [ ] Gestión de Propiedades
- [ ] Gestión de Inquilinos
- [ ] Contratos de Arrendamiento
- [ ] Sistema de Pagos
- [ ] Subida de Archivos
- [ ] Notificaciones
- [ ] Configuraciones
- [ ] Responsive Design

---

## 🔄 Changelog de Issues

### [Fecha] - Versión X.X.X
- ✅ Resuelto: [Issue ID] - Descripción breve
- 🟡 En progreso: [Issue ID] - Descripción breve
- 🔴 Nuevo: [Issue ID] - Descripción breve

---

## 📞 Contacto para Reportar Issues

**Proceso de Reporte:**
1. Identifica la severidad del issue
2. Usa la plantilla correspondiente
3. Incluye toda la información solicitada
4. Agrega screenshots si es necesario
5. Actualiza el contador en el resumen

**Información Requerida:**
- Descripción clara y concisa
- Pasos exactos para reproducir
- Navegador y dispositivo usado
- Screenshots o videos si aplica
- Logs de error de la consola del navegador

---

*Última actualización: [Fecha]*
*Versión del documento: 1.0*

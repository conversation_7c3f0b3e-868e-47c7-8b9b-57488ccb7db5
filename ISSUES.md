# 🐛 Issues Tracker - GoodTenant Web Client

## 📋 Issues Summary

| Severity | Total | Open | In Progress | Resolved |
|----------|-------|------|-------------|----------|
| 🔴 Critical | 0 | 0 | 0 | 0 |
| 🟠 High | 9 | 9 | 0 | 0 |
| 🟡 Medium | 0 | 0 | 0 | 0 |
| 🟢 Low | 0 | 0 | 0 | 0 |
| **Total** | **9** | **9** | **0** | **0** |

---

## 🔴 Critical Issues
> Issues that block main functionalities or cause data loss

### Template for Critical Issues
```markdown
### [CRIT-001] Issue Title
- **Status**: 🔴 Open / 🟡 In Progress / ✅ Resolved
- **Date Reported**: YYYY-MM-DD
- **Component**: Component/page name
- **Description**: Detailed description of the problem
- **Steps to Reproduce**:
  1. Step 1
  2. Step 2
  3. Step 3
- **Expected Result**: What should happen
- **Actual Result**: What actually happens
- **Impact**: Description of user impact
- **Browser/Device**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [If applicable]
- **Error Logs**: [If applicable]
- **Priority**: P0 (Critical)
- **Assigned to**: [Name]
- **Resolution Date**: [When resolved]
```

---

## 🟠 High Issues

### Registration Form Issues (Safari Mobile)

1. **Generic validation messages without field identification**
   - Shows "This field is required" without specifying which field
   - <Picture>

2. **Missing password requirements on register**
   - Shows "Password must contain:" without listing what's missing
   - <Picture>

3. **Password error persists after fixing**
   - Error message stays visible even when password is valid
   - <Picture>

4. **Button not displayed correctly**
   - "Create Account" button overflows screen on mobile
   - <Picture>

### Logout Issues (Safari Mobile)

5. **Logout redirects to 404 page**
   - When logged in, clicking Logout redirects to https://www.goodtenant.us/login (404 error)
   - Should redirect to https://www.goodtenant.us/en/auth/login
   - <Picture>

6. **Logo redirects to 404 page**
   - When clicking GoodTenant logo, redirects to https://www.goodtenant.us/dashboard (404 error)
   - Should redirect to https://www.goodtenant.us/en/dashboard
   - <Picture>

### Property Search Issues (Safari Mobile)

7. **Search field hides keyboard after 1 character**
   - In Properties page, "Search properties..." field searches and hides keyboard after typing only 1 character
   - Should allow typing multiple characters before searching
   - Missing "Done" button on keyboard or search button for better accessibility
   - <Picture>

8. **Missing translations in Add Property form**
   - Multiple untranslated text keys showing instead of proper text
   - Keys showing: properties.addProperty, properties.addPropertyDescripcion, properties.from.selectHOA, selectPropertyType, maxFiles
   - Should display proper translated text
   - <Picture>

9. **State/Province and Country fields missing dropdowns**
   - State/Province field is text input instead of dropdown with US states
   - Country shows "USA" but no dropdown to select other countries
   - Should have proper dropdowns with states related to selected country
   - Need to verify requirements: US-only or multi-country support
   - <Picture>

10. **Incorrect image sections in Add Property form**
    - Shows both "Property Images" and "Upload New Images" sections when creating new property
    - "Property Images" section should not exist when creating new property (no existing images)
    - Should only show "Upload New Images" section for new property creation
    - "Property Images" section should appear only when editing existing property
    - <Picture>

### Template for High Issues
```markdown
### [HIGH-001] Issue Title
- **Status**: 🔴 Open / 🟡 In Progress / ✅ Resolved
- **Date Reported**: YYYY-MM-DD
- **Component**: Component/page name
- **Description**: Detailed description of the problem
- **Steps to Reproduce**:
  1. Step 1
  2. Step 2
  3. Step 3
- **Expected Result**: What should happen
- **Actual Result**: What actually happens
- **Workaround**: [If temporary solution exists]
- **Browser/Device**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [If applicable]
- **Priority**: P1 (High)
- **Assigned to**: [Name]
- **Resolution Date**: [When resolved]
```

---

## 🟡 Medium Issues
> Issues that affect user experience but don't block functionalities

### Template for Medium Issues
```markdown
### [MED-001] Issue Title
- **Status**: 🔴 Open / 🟡 In Progress / ✅ Resolved
- **Date Reported**: YYYY-MM-DD
- **Component**: Component/page name
- **Description**: Detailed description of the problem
- **Steps to Reproduce**:
  1. Step 1
  2. Step 2
  3. Step 3
- **Expected Result**: What should happen
- **Actual Result**: What actually happens
- **Browser/Device**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [If applicable]
- **Priority**: P2 (Medium)
- **Assigned to**: [Name]
- **Resolution Date**: [When resolved]
```

---

## 🟢 Low Issues
> Minor issues, UI/UX improvements, optimizations

### Template for Low Issues
```markdown
### [LOW-001] Issue Title
- **Status**: 🔴 Open / 🟡 In Progress / ✅ Resolved
- **Date Reported**: YYYY-MM-DD
- **Component**: Component/page name
- **Description**: Detailed description of the problem
- **Improvement Suggestion**: [If applicable]
- **Browser/Device**: Chrome/Firefox/Safari - Desktop/Mobile
- **Screenshots**: [If applicable]
- **Priority**: P3 (Low)
- **Assigned to**: [Name]
- **Resolution Date**: [When resolved]
```

---

## 📊 Issues by Component

### 🔐 Authentication
- Total: 6
- Critical: 0 | High: 6 | Medium: 0 | Low: 0

### 🏠 Dashboard
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 🏢 Properties
- Total: 3
- Critical: 0 | High: 3 | Medium: 0 | Low: 0

### 👥 Users/Tenants
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 📄 Leases/Contracts
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 💰 Payments
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 🔧 Maintenance
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 📁 Files/Documents
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 🔔 Notifications
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

### 📱 Responsive/Mobile
- Total: 1
- Critical: 0 | High: 1 | Medium: 0 | Low: 0

### 🎨 UI/UX General
- Total: 0
- Critical: 0 | High: 0 | Medium: 0 | Low: 0

---

## 📝 Testing Notes

### Browsers Tested
- [ ] Chrome (Desktop)
- [ ] Firefox (Desktop)
- [ ] Safari (Desktop)
- [ ] Chrome (Mobile)
- [x] Safari (Mobile)

### Resolutions Tested
- [ ] 1920x1080 (Desktop)
- [ ] 1366x768 (Laptop)
- [ ] 768x1024 (Tablet)
- [ ] 375x667 (Mobile)

### Main Features Tested
- [ ] Login/Logout
- [ ] Dashboard
- [ ] Property Management
- [ ] Tenant Management
- [ ] Lease Contracts
- [ ] Payment System
- [ ] File Upload
- [ ] Notifications
- [ ] Settings
- [x] Responsive Design (Registration Form)

---

## 🔄 Issues Changelog

### [Date] - Version X.X.X
- ✅ Resolved: [Issue ID] - Brief description
- 🟡 In progress: [Issue ID] - Brief description
- 🔴 New: [Issue ID] - Brief description

---

## 📞 Contact for Issue Reporting

**Reporting Process:**
1. Identify issue severity
2. Use corresponding template
3. Include all requested information
4. Add screenshots if necessary
5. Update counter in summary

**Required Information:**
- Clear and concise description
- Exact steps to reproduce
- Browser and device used
- Screenshots or videos if applicable
- Error logs from browser console

---

*Last updated: [Date]*
*Document version: 1.0*

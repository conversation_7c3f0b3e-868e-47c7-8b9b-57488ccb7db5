// @ts-check
const withNextIntl = require('next-intl/plugin')('./i18n.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Skip type checking during build
  typescript: {
    ignoreBuildErrors: true,
  },
  output: 'standalone', // Enable standalone output for Docker deployment
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'goodtenant.72b01fb60837afcad807e9ac33b29580.r2.cloudflarestorage.com',
        port: '',
        pathname: '/**',
      },
    ],
    // Optional: Configure image quality (1-100, where 100 is best quality)
    // quality: 80,
    
    // Optional: Configure image formats to optimize for
    // formats: ['image/avif', 'image/webp'],
    
    // Optional: Configure device sizes for responsive images
    // deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    
    // Optional: Configure image sizes for layout shifts
    // imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // Enable React Strict Mode
  reactStrictMode: true,
  // Enable server components (if using App Router)
  
  // Configure server actions body size limit for file uploads
  experimental: {
    serverActions: {
      bodySizeLimit: '20mb'
    },
  },
  // Add redirects
  async redirects() {
    return [
      {
        source: '/',
        destination: '/en',  // Change to your default locale
        permanent: true,
        basePath: false
      },
    ];
  },
};

module.exports = withNextIntl(nextConfig);
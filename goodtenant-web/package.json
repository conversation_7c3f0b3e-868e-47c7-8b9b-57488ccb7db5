{"name": "goodtenant-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:standalone": "next build && cp -r .next/standalone/ . && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/", "start": "next start", "start:standalone": "node server.js", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.3.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.80.10", "@tanstack/react-table": "^8.21.3", "@tiptap/core": "^2.23.1", "@tiptap/extension-bold": "^2.23.1", "@tiptap/extension-bullet-list": "^2.23.1", "@tiptap/extension-italic": "^2.23.1", "@tiptap/extension-ordered-list": "^2.23.1", "@tiptap/extension-placeholder": "^2.23.1", "@tiptap/extension-text-align": "^2.23.1", "@tiptap/extension-underline": "^2.14.0", "@tiptap/react": "^2.23.1", "@tiptap/starter-kit": "^2.23.1", "@types/html2canvas": "^0.5.35", "@types/jspdf": "^1.3.3", "@types/pdfmake": "^0.2.11", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "mapbox-gl": "^3.12.0", "next": "15.3.2", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "pdfmake": "^0.2.20", "react": "^19.0.0", "react-country-flag": "^3.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-international-phone": "^4.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-map-gl": "^8.0.4", "react-signature-canvas": "^1.1.0-alpha.2", "react-toastify": "^11.0.5", "react-viewer": "^3.2.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tiptap": "^0.15.0", "use-debounce": "^10.0.5", "zod": "^3.25.34"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.4", "@sentry/nextjs": "^9.31.0", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.18", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20.17.52", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-map-gl": "^6.1.7", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}
version: '3.8'

services:
  web-development:
    build:
      context: .
      target: development
      dockerfile: Dockerfile
    container_name: goodtenant-web-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.development
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - goodtenant-network

  web-production:
    build:
      context: .
      target: production
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    container_name: goodtenant-web-prod
    restart: unless-stopped
    ports:
      - "3001:3000"
    env_file:
      - .env.production
    networks:
      - goodtenant-network
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  goodtenant-network:
    driver: bridge

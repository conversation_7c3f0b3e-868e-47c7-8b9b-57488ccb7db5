export type NotificationType =
  | 'SYSTEM'
  | 'MAINTENANCE'
  | 'PAYMENT'
  | 'LEASE'
  | 'DOCUMENT'
  | 'MESSAGE'
  | 'ALERT';

export type NotificationEntity =
  | 'USER'
  | 'PROPERTY'
  | 'UNIT'
  | 'LEASE'
  | 'MAINTENANCE_REQUEST'
  | 'PAYMENT';

export interface NotificationUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatarUrl?: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  entityType?: NotificationEntity;
  entityId?: string;
  actionUrl?: string;
  createdAt: string;
  updatedAt: string;
  user?: NotificationUser;
  createdBy?: NotificationUser;
}

export interface NotificationPagination {
  items: Notification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

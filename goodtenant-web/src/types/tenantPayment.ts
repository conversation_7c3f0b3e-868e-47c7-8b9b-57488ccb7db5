export interface TenantPaymentStatusResponse {
  success: boolean;
  data: TenantPaymentStatus;
}

export interface TenantPaymentStatus {
  tenantId: string;
  asOfDate: string;
  overallStatus: {
    totalMonthlyRent: number;
    totalPaid: number;
    totalBalance: number;
    allPaid: boolean;
    anyLate: boolean;
  };
  leases: TenantLeasePaymentStatus[];
}

export interface TenantLeasePaymentStatus {
  leaseId: string;
  property: {
    id: string;
    name: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    zipCode: string;
  };
  monthlyRent: number;
  paymentDueDate: number;
  currentPeriod: {
    start: string;
    end: string;
  };
  paymentStatus: {
    isPaid: boolean;
    totalPaid: number;
    balance: number;
    dueDate: string;
    isLate: boolean;
  };
  paymentHistory: PaymentHistoryItem[];
}

export interface PaymentHistoryItem {
  id: string;
  amount: number;
  paymentDate: string;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  paymentMethod: string;
}

export type TaxStatus = 'unpaid' | 'partial_paid' | 'paid' | 'overdue' | 'cancelled';
export type TaxType = 'Property' | 'Income' | 'Sales' | 'Business' | 'Others' | 'all';

export interface Tax {
  id: string;
  taxType: TaxType;
  description: string;
  website?: string;
  accountNumber: string;
  taxId: string;
  amount: string | number;
  dueDate: string;
  paymentFrequency: string;
  status: TaxStatus;
  paymentMethod?: string;
  paymentDate?: string | null;
  notes?: string;
  isActive: boolean;
  propertyId: string;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  property?: {
    id: string;
    name: string;
    addressLine1: string;
    city: string;
    state: string;
  };
  creator?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updater?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateTaxDto {
  taxType: TaxType;
  description: string;
  website?: string;
  accountNumber: string;
  taxId: string;
  amount: number;
  dueDate: string;
  paymentFrequency: string;
  status: TaxStatus;
  paymentMethod?: string;
  paymentDate?: string;
  notes?: string;
  propertyId: string;
}

export interface UpdateTaxDto extends Partial<CreateTaxDto> {}

export interface TaxResponse {
  success: boolean;
  data: Tax;
}

export interface TaxesResponse {
  success: boolean;
  data: Tax[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface DeleteTaxResponse {
  success: boolean;
  message: string;
}

export interface GetTaxesQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: TaxStatus;
  taxType?: TaxType;
  propertyId?: string;
}

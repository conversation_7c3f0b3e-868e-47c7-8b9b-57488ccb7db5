export interface HOA {
  id: string;
  name: string;
  fee: number | string;
  website: string | null;
  phone: string | null;
  email: string | null;
  managerName: string | null;
  managerEmail: string | null;
  managerPhone: string | null;
  gateCode: string | null;
  comments: string | null;
  isActive: boolean;
  addressLine1: string;
  addressLine2: string | null;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  properties?: Property[];
}

export interface CreateHOAPayload {
  name: string;
  fee: number;
  website?: string;
  phone?: string;
  email?: string;
  managerName?: string;
  managerEmail?: string;
  managerPhone?: string;
  gateCode?: string;
  comments?: string;
  isActive: boolean;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface UpdateHOAPayload extends Partial<CreateHOAPayload> {}

export interface HOAResponse extends HOA {
  success: boolean;
}

export interface HOAListResponse {
  success: boolean;
  data: HOA[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

// Re-exporting Property type if needed
import { Property } from './property';

export type { Property };

export type UtilityType = 'electricity' | 'water' | 'gas' | 'internet' | 'trash' | 'sewer' | 'other';

export interface Utility {
  id: string;
  propertyId: string;
  utilityType: UtilityType;
  providerName: string;
  accountNumber: string;
  amount?: number; // Added amount field as optional
  billingCycle: string;
  dueDate: number;
  isIncludedInRent: boolean;
  isActive: boolean;
  notes?: string;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  property: {
    id: string;
    name: string;
    addressLine1: string;
    city: string;
    state: string;
  };
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updater: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateUtilityDto {
  propertyId: string;
  utilityType: UtilityType;
  providerName: string;
  accountNumber: string;
  billingCycle: string;
  dueDate: number;
  isIncludedInRent: boolean;
  isActive: boolean;
  notes?: string;
}

export interface UpdateUtilityDto extends Partial<Omit<CreateUtilityDto, 'propertyId'>> {}

export interface UtilityResponse {
  success: boolean;
  data: Utility;
}

export interface UtilitiesResponse {
  success: boolean;
  data: Utility[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

export interface DeleteUtilityResponse {
  success: boolean;
  message: string;
}

export interface GetUtilitiesQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  propertyId?: string;
  utilityType?: UtilityType;
  isActive?: boolean;
  billingCycle?: string;
}

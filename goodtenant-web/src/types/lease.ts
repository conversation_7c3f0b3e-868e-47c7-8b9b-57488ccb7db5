// Base lease interface
export interface Lease {
  id: string;
  startDate: string;
  endDate: string;
  monthlyRent: number | string;
  securityDeposit: number | string;
  status: 'draft' | 'active' | 'terminated' | 'expired';
  leaseType: 'fixed' | 'month-to-month' | 'week-to-week' | 'other';
  paymentDueDate: number;
  lateFee: number | string;
  nsfFee: number | string;
  earlyTerminationFee: number | string;
  numberOfPeople: number;
  notes?: string;
  isActive: boolean;
  propertyId: string;
  landlordId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  property?: {
    id: string;
    name: string;
    addressLine1: string;
    city: string;
    state: string;
    postalCode: string;
  };
  landlord?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  tenants?: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  }>;
}

// DTOs
export interface CreateLeaseDto {
  startDate: string;
  endDate: string;
  monthlyRent: number;
  securityDeposit: number;
  status: 'draft' | 'active' | 'terminated' | 'expired';
  leaseType: 'fixed' | 'month-to-month' | 'week-to-week' | 'other';
  paymentDueDate: number;
  lateFee: number;
  nsfFee: number;
  earlyTerminationFee: number;
  numberOfPeople: number;
  notes?: string;
  propertyId: string;
  tenantIds?: string[];
}

export interface UpdateLeaseDto extends Partial<CreateLeaseDto> {
  tenantIds?: string[];
}

// Query parameters for getting leases
export interface GetLeasesQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  propertyId?: string;
  landlordId?: string;
  tenantId?: string;
}

// Response types
export interface LeaseResponse {
  success: boolean;
  data: Lease;
  message?: string;
}

export interface LeasesResponse {
  success: boolean;
  data: Lease[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface DeleteLeaseResponse {
  success: boolean;
  message: string;
}

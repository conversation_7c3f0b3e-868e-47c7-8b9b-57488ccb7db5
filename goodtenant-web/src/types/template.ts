export interface Template {
  id: string;
  name: string;
  description: string;
  type: TemplateType;
  content: TemplateContent;
  isActive: boolean;
  isDefault: boolean;
  usedVariables: string[];
  createdById: string;
  updatedById: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updatedBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export type TemplateType = 'LEASE' | 'PET' | 'INVENTORY' | 'HOA';

export interface TemplateContent {
  version: string;
  content: TemplateContentItem[];
}

// In src/types/template.ts
export interface TemplateContentItem {
  type: string;
  text: string;
  variables: string[];
  textAlign?: 'left' | 'center' | 'right';
  hasFormatting?: boolean;
  formattedContent?: string;
  // Add this new property for list items
  content?: Array<{
    text: string;
    variables: string[];
  }>;
}

export interface CreateTemplatePayload {
  name: string;
  description: string;
  type: TemplateType;
  content: TemplateContent;
  isActive: boolean;
}

export interface UpdateTemplatePayload {
  name?: string;
  description?: string;
  type?: TemplateType;
  content?: TemplateContent;
  isActive?: boolean;
}

export interface TemplateResponse {
  success: boolean;
  data: Template;
}

export interface TemplateListResponse {
  success: boolean;
  data: Template[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

export interface SystemVariable {
  description: string;
  required: boolean;
  type: string;
}

export interface SystemVariablesResponse {
  success: boolean;
  data: {
    [key: string]: SystemVariable;
  };
}

export interface DeleteTemplateResponse {
  success: boolean;
  message: string;
}

// User role types
export type UserRole = 'account_owner' | 'property_manager' | 'leasing_agent' | 'maintenance_staff' | 'tenant';
export type RoleFilter = UserRole | 'all';

// Base user interface
export interface BaseUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  avatar?: string | null;
  isActive?: boolean;
  addressLine1: string;
  addressLine2: string | null;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isEmailVerified: boolean;
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Account user relationship
export interface AccountUser {
  id: string;
  primaryRole: UserRole;
  additionalRoles: UserRole[];
  isDefault: boolean;
  accountId: string;
}

// Account interface for user accounts
export interface UserAccount {
  id: string;
  status: string;
  plan: string;
  currentPeriodStart: string | null;
  currentPeriodEnd: string | null;
  cancelAtPeriodEnd: boolean;
  stripeCustomerId: string | null;
  stripeSubscriptionId: string | null;
  paymentMethodId: string | null;
  trialEnd: string | null;
  createdAt: string;
  updatedAt: string;
  accountUser: {
    additionalRoles: UserRole[];
    primaryRole: UserRole;
    isDefault: boolean;
  };
}

// Full user interface including account relationships
export interface User extends BaseUser {
  accountUsers: AccountUser[];
}

// Create user DTO
export interface CreateUserDto {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  primaryRole: UserRole;
  additionalRoles?: UserRole[];
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

// Update user DTO
export interface UpdateUserDto extends Partial<Omit<CreateUserDto, 'email' | 'primaryRole'>> {
  email?: string;
  primaryRole?: UserRole;
}

// API Response types
export interface UserResponse {
  success: boolean;
  data: User;
  message?: string;
}

export interface UsersResponse {
  success: boolean;
  data: Array<User & { accounts?: UserAccount[] }>;
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface CurrentUserResponse {
  success: boolean;
  data: User;
}

export interface DeleteUserResponse {
  success: boolean;
  message: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data: User;
}

// Query parameters for getting users
export interface GetUsersQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: RoleFilter;
}

// Update profile DTO
export interface UpdateProfileDto {
  firstName?: string;
  lastName?: string;
  phone?: string;
  addressLine1?: string;
  addressLine2?: string | null;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

// Change password DTO
export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

// Change password response
export interface ChangePasswordResponse {
  success: boolean;
  message: string;
}

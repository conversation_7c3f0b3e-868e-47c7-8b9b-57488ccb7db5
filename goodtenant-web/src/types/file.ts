export type EntityType = 'Property' | 'HOA' | 'Insurance' | 'Loan' | 'Tax' | 'Maintenance' | 'Utility' | 'Inventory' | 'Lease';

export interface FileMetadata {
  id: string;
  fileName: string;
  fileKey: string;
  fileType: string;
  fileSize: number;
  description: string;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
  propertyId: string; // Kept for backward compatibility
  entityId?: string; // New field for generic entity reference
  entityType?: EntityType; // New field for entity type
  url?: string; // Populated with the signed URL
}

// Base API response structure
export interface BaseApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

export interface FileUploadResponse extends BaseApiResponse<FileMetadata> {}

export interface FileListResponse extends BaseApiResponse<FileMetadata[]> {}

export interface FileSignedUrlResponse extends BaseApiResponse<{ url: string }> {}

// Wrapped API responses
export interface ApiResponse<T> extends BaseApiResponse<T> {}

export interface UploadFileParams<T extends EntityType> {
  file: File;
  entityId: string;
  entityType?: T;
  description?: string;
  onUploadProgress?: (progress: number) => void;
}

// For backward compatibility
export interface LegacyUploadFileParams extends Omit<UploadFileParams<'Property'>, 'entityType' | 'entityId'> {
  propertyId: string;
}

export interface FileWithPreview {
  file: File;
  preview: string;
}
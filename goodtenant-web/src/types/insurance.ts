import { User } from '../app/services/authService';
import { InsuranceStatus } from './insurance-status';
import { FileMetadata, EntityType } from './file';

export interface InsuranceProperty {
  id: string;
  name: string;
  addressLine1: string;
  city: string;
  state: string;
  address?: string; // For backward compatibility
}

export interface InsuranceCreator extends Pick<User, 'id' | 'firstName' | 'lastName' | 'email'> {}

export interface Insurance {
  id: string;
  policyNumber: string;
  provider: string;
  website?: string | null; // Optional and can be null
  insuranceType: string;
  effectiveStartDate?: string; // Made optional to match form schema
  effectiveEndDate?: string; // Made optional to match form schema
  premiumAmount: string | number;
  coverageAmount: string | number;
  deductible: string | number;
  paymentFrequency: string;
  currency: string;
  agentName?: string | null; // Made optional and nullable
  agentPhone?: string | null; // Made optional and nullable
  agentEmail?: string | null; // Made optional and nullable
  propertyId: string;
  notes?: string | null;
  isActive?: boolean;
  
  // Backend-managed fields (optional)
  createdBy?: string;
  updatedBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
  
  // Optional fields
  status?: InsuranceStatus;
  property?: InsuranceProperty;
  creator?: InsuranceCreator;
  updater?: InsuranceCreator;
  documents?: Array<FileMetadata & { entityType: EntityType }>;
}

export interface CreateInsuranceDto {
  policyNumber: string;
  provider: string;
  website?: string | null;
  insuranceType: string;
  effectiveStartDate: string;
  effectiveEndDate: string;
  premiumAmount: number;
  coverageAmount: number;
  deductible: number;
  paymentFrequency: string;
  currency?: string;
  agentName?: string | null;
  agentPhone?: string | null;
  agentEmail?: string | null;
  notes?: string | null;
  isActive?: boolean;
  propertyId: string;
}

export type UpdateInsuranceDto = Partial<CreateInsuranceDto>;

export interface InsuranceResponse {
  success: boolean;
  data: Insurance;
}

export interface InsurancesResponse {
  success: boolean;
  data: Insurance[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface DeleteInsuranceResponse {
  success: boolean;
  message: string;
}

export interface GetInsurancesQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  propertyId?: string;
  isActive?: boolean;
}

export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'overdue' | 'cancelled';
export type TaskPriority = 'low' | 'medium' | 'high' | 'critical';
export type TaskType = 'utility' | 'insurance' | 'loan' | 'lease' | 'maintenance' | 'other';

export interface TaskAssignee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface TaskProperty {
  id: string;
  name: string;
  addressLine1: string;
  city?: string;
  state?: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  status: TaskStatus;
  priority: TaskPriority;
  type: TaskType;
  taskableId: string | null;
  taskableType: string | null;
  isRecurring: boolean;
  recurrencePattern: string | null;
  nextRecurrenceDate: string | null;
  completedAt: string | null;
  completedById: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  accountId: string;
  createdById: string;
  assignedToId: string;
  propertyId: string;
  creator: TaskAssignee;
  assignee: TaskAssignee;
  property: TaskProperty;
}

export interface CreateTaskPayload {
  title: string;
  description: string;
  dueDate: string;
  status: TaskStatus;
  priority: TaskPriority;
  type: TaskType;
  assignedToId: string;
  propertyId: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  taskableId?: string;
  taskableType?: string;
}

export type UpdateTaskPayload = Partial<CreateTaskPayload>;

export interface TaskResponse {
  success: boolean;
  data: Task;
}

export interface TaskListResponse {
  success: boolean;
  data: Task[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface TaskQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  assignedTo?: string;
  propertyId?: string;
}

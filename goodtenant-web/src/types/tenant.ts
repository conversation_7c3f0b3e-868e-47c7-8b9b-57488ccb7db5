// Common types for tenant-related functionality

export interface TenantInvitation {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  status: TenantInvitationStatus;
  expiresAt: string;
  token: string;
  property: {
    id: string;
    name: string;
    addressLine1: string;
    city: string;
    state: string;
    postalCode: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateTenantInvitationDto {
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface PaginatedTenantInvitations {
  data: TenantInvitation[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface TenantInvitationResponse {
  success: boolean;
  data: TenantInvitation;
  message?: string;
}

export interface ResendInvitationResponse {
  success: boolean;
  message: string;
}

export interface CancelInvitationResponse {
  success: boolean;
  message: string;
}

export interface FinalizeOnboardingResponse {
  success: boolean;
  message: string;
  data: {
    invitationId: string;
    status: string;
  };
}

export interface VerifyInvitationResponse {
  success: boolean;
  data: {
    invitation: TenantInvitation;
    property: {
      propertyType: string | undefined;
      bathrooms: number | undefined;
      bedrooms: number | undefined;
      size: number | undefined;
      type: string | undefined;
      id: string;
      name: string;
      addressLine1: string;
      city: string;
      state: string;
      postalCode: string;
    };
  };
  message?: string;
}

export interface CompleteOnboardingRequest {
  userData: {
    firstName: string;
    lastName: string;
    password: string;
    phoneNumber: string;
  };
  additionalData: {
    vehicles?: Array<{
      make: string;
      model: string;
      year: number;
      color: string;
      licensePlate: string;
    }>;
    pets?: Array<{
      name: string;
      type: string;
      breed: string;
      weight: number;
      age: number;
      sex: string;
      registration?: string;
      notes?: string;
    }>;
    occupants?: Array<{
      firstName: string;
      lastName: string;
      age: number;
      relationship: string;
      notes?: string;
    }>;
  };
}

export interface CompleteOnboardingResponse {
  success: boolean;
  message: string;
  data: {
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
    };
    property: {
      id: string;
      name: string;
    };
    lease: {
      leaseId: string;
      hasHoa: boolean;
      vehicles: number;
      pets: number;
      occupants: number;
      adultOccupants: Array<{
        firstName: string;
        lastName: string;
      }>;
    };
    message: string;
  };
}

export enum TenantInvitationStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

export interface LeaseDocumentResponse {
  success: boolean;
  data: {
    documentContent: any; // Adjust this type based on your document structure
    documentName: string;
    templateId: string;
    leaseId: string;
  };
  message?: string;
}

export interface PetDocumentResponse {
  success: boolean;
  data: {
    documentContent: any; // Adjust this type based on your document structure
    documentName: string;
    templateId: string;
    leaseId: string;
  };
  message?: string;
}

export interface HoaDocumentResponse {
  success: boolean;
  data: {
    documentContent: any; // Adjust this type based on your document structure
    documentName: string;
    templateId: string;
    leaseId: string;
  };
  message?: string;
}

export interface FileUploadParams {
  file: File;
  modelName: string;
  modelId: string;
  description?: string;
}

export interface FileUploadResponse {
  success: boolean;
  data: {
    id: string;
    isActive: boolean;
    leaseId: string;
    fileName: string;
    fileKey: string;
    fileType: string;
    fileSize: number;
    description: string;
    uploadedBy: string | null;
    updatedAt: string;
    createdAt: string;
  };
  message?: string;
}

// src/types/property.ts

export const PROPERTY_TYPES = {
  APARTMENT: 'apartment',
  HOUSE: 'house',
  CONDO: 'condo',
  TOWNHOUSE: 'townhouse',
  COMMERCIAL: 'commercial',
  OTHER: 'other'
} as const;

export type PropertyType = typeof PROPERTY_TYPES[keyof typeof PROPERTY_TYPES];

export const PROPERTY_STATUS = {
  VACANT: 'vacant',
  OCCUPIED: 'occupied',
  UNDER_MAINTENANCE: 'under_maintenance',
  RENOVATING: 'renovating',
  OFF_MARKET: 'off_market'
} as const;

export type PropertyStatus = typeof PROPERTY_STATUS[keyof typeof PROPERTY_STATUS];

export const PRICE_INTERVALS = {
  DAY: 'day',
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year'
} as const;

export type PriceInterval = typeof PRICE_INTERVALS[keyof typeof PRICE_INTERVALS];

export interface PropertyUser {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string | null;
  isPrimary: boolean;
  startDate: string | null;
  endDate: string | null;
  notes: string | null;
  addedAt: string;
}

export interface AddUserToPropertyDto {
  userId: string;
  startDate: string;
  endDate: string;
}

export interface PropertyUserResponse {
  success: boolean;
  message: string;
  data?: PropertyUser;
}

export interface PropertyUsersResponse {
  success: boolean;
  data: PropertyUser[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

export interface Property {
  id: string;
  name: string;
  description: string;
  propertyType: PropertyType;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  hoaId: string;
  latitude: number;
  longitude: number;
  yearBuilt: number;
  sizeSquareFeet: number;
  depositAmount: number;
  bedrooms: number;
  bathrooms: number;
  isActive: boolean;
  status: PropertyStatus;
  priceAmount: number;
  priceCurrency: string;
  priceInterval: PriceInterval;
  deletedAt: string | null;
  users: PropertyUser[];
  createdAt: string;
  updatedAt: string;
}

export interface CreatePropertyDto {
  name: string;
  description: string;
  propertyType: PropertyType;
  status: PropertyStatus;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  hoaId?: string;
  latitude: number;
  longitude: number;
  priceAmount: number;
  priceCurrency: string;
  priceInterval: PriceInterval;
  sizeSquareFeet: number;
  yearBuilt: number;
  depositAmount: number;
  bedrooms: number;
  bathrooms: number;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface UpdatePropertyDto extends Partial<CreatePropertyDto> {}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
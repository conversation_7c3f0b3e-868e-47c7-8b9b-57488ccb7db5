import { Property } from './property';
import { User } from './user';

export type MaintenanceStatus = 'open' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled';
export type MaintenancePriority = 'low' | 'medium' | 'high' | 'critical';

export interface MaintenanceFile {
  id: string;
  fileName: string;
  fileKey: string;
  fileType: string;
  fileSize: number;
  description?: string;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
  ticketId: string;
  uploader?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface MaintenanceTicket {
  id: string;
  title: string;
  description: string;
  status: MaintenanceStatus;
  priority: MaintenancePriority;
  reportedBy: string;
  assignedTo?: string;
  propertyId: string;
  accountId: string;
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;
  inventoryId: string | null;
  reporter?: User;
  assignee?: User;
  property?: Property;
  files?: MaintenanceFile[];
}

export interface CreateMaintenanceTicketPayload {
  title: string;
  description: string;
  priority: MaintenancePriority;
}

export interface UpdateMaintenanceTicketPayload {
  title?: string;
  description?: string;
  priority?: MaintenancePriority;
  propertyId?: string;
  assigneeId?: string | null;
}

export interface AssignMaintenanceTicketPayload {
  assigneeId: string;
}

export interface UpdateMaintenanceStatusPayload {
  status: MaintenanceStatus;
}

export interface MaintenanceTicketResponse {
  success: boolean;
  data: MaintenanceTicket;
  message?: string;
}

export interface MaintenanceTicketListResponse {
  success: boolean;
  data: MaintenanceTicket[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

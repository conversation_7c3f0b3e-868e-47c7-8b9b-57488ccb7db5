export enum LoanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  PAID = 'paid'
}

export interface LoanAddress {
  line1: string;
  line2?: string | null;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface LoanProperty {
  id: string;
  name: string;
  addressLine1: string;
  city: string;
  state: string;
}

export interface Loan {
  id: string;
  propertyId: string;
  
  // Basic loan details
  loanName: string;
  loanType: string;
  loanTerm: number | string;
  loanNumber: string;
  loanAmount: number | string;
  interestRate: number | string;
  
  // Lender information
  lenderName: string;
  lenderContactName: string;
  lenderEmail: string;
  lenderPhone: string;
  lenderWebsite: string;
  lenderCountry: string;
  
  // Legacy lender address fields
  lenderAddressLine1: string;
  lenderAddressLine2: string | null;
  lenderCity: string;
  lenderState: string;
  lenderPostalCode: string;
  
  // Payment details
  paymentDay: number | string;
  monthlyPayment: number | string;
  
  // Legacy fields
  customerServicePhone: string;
  principal: number | string;
  escrow: number | string;
  
  // Notes/comments
  notes: string | null;
  comments: string | null;
  
  // System fields
  accountId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  property?: LoanProperty;
}

export interface CreateLoanDto {
  propertyId: string;
  lenderName: string;
  lenderWebsite: string;
  loanNumber: string;
  lenderAddressLine1: string;
  lenderAddressLine2?: string;
  lenderCity: string;
  lenderState: string;
  lenderPostalCode: string;
  lenderCountry: string;
  customerServicePhone: string;
  principal: number;
  interestRate: number;
  escrow: number;
  loanAmount: number;
  comments?: string;
}

export interface UpdateLoanDto extends Partial<CreateLoanDto> {}

export interface LoansResponse {
  success: boolean;
  data: Loan[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface GetLoansQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  propertyId?: string;
}

export interface LoanResponse {
  success: boolean;
  data: Loan;
  message?: string;
}

export interface DeleteLoanResponse {
  success: boolean;
  message: string;
}

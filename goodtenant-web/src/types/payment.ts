// src/types/payment.ts
export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'partially_refunded' | 'cancelled';
  paymentMethod: 'stripe' | 'cash' | 'bank_transfer' | 'check' | 'other';
  paymentDate: string;
  dueDate?: string;
  referenceNumber?: string;
  notes?: string;
  payerId: string;
  payer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  receiverId: string;
  receiver?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  accountId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  property?: {
    id: string;
    name: string;
    address?: string;
  };
  receiptUrl?: string;
}

export interface CreatePaymentDto {
  amount: number;
  currency?: string;
  paymentMethod: 'stripe' | 'cash' | 'bank_transfer' | 'check' | 'other';
  paymentDate?: string;
  dueDate?: string;
  referenceNumber?: string;
  notes?: string;
  metadata?: Record<string, any>;
  payerId: string;
}
  
export interface UpdatePaymentDto {
  status?: 'pending' | 'completed' | 'failed' | 'refunded' | 'partially_refunded' | 'cancelled';
  notes?: string;
  referenceNumber?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  success: boolean;
  data: Payment;
  message?: string;
}

export interface PaymentsResponse {
  success: boolean;
  data: Payment[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface DeletePaymentResponse {
  success: boolean;
  message: string;
}

export interface GetPaymentsQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  paymentMethod?: string;
  startDate?: string;
  endDate?: string;
  payerId?: string;
  receiverId?: string;
  accountId?: string;
}

export interface StripeLineItem {
  price_data: {
    currency: string;
    product_data: {
      name: string;
      description?: string;
    };
    unit_amount: number;
  };
  quantity: number;
}

export type CheckoutSessionParams = {
  amount: number;
  currency?: string;
  description: string;
  metadata?: Record<string, any>;
  payment_method_types?: string[];
  mode: 'payment' | 'subscription' | 'setup';
  success_url: string;
  cancel_url: string;
  line_items?: StripeLineItem[];
}

export interface StripePriceData {
  currency: string;
  product_data: {
    name: string;
    description: string;
  };
  unit_amount: number;
}

export interface CheckoutSessionResponse {
  success: boolean;
  url: string;
  sessionId: string;
  paymentId: string;
}

export interface CheckoutSuccessResponse {
    success: boolean;
    data: {
      payment: Payment;
      session: {
        id: string;
        payment_intent: string;
        payment_status: string;
        status: string;
      };
    };
  }
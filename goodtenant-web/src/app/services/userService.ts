import { api } from '@/lib/api';
import {
  User,
  CreateUserDto,
  UpdateUserDto,
  UserResponse,
  UsersResponse,
  DeleteUserResponse,
  GetUsersQueryParams,
  UserRole,
  CurrentUserResponse,
  UpdateProfileDto,
  UpdateProfileResponse,
  UserAccount,
  ChangePasswordDto,
  ChangePasswordResponse
} from '@/types/user';

export const userService = {
  // Get all users with optional filtering and pagination
  async getUsers(params: GetUsersQueryParams = {}): Promise<UsersResponse> {
    const { page = 1, limit = 10, search, role } = params;
    const response = await api.get('/users', {
      params: {
        page,
        limit,
        ...(search && { search }),
        ...(role && role !== 'all' && { role })
      }
    });
    return response.data;
  },

  // Get single user by ID
  async getUserById(id: string): Promise<UserResponse> {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // Create a new user
  async createUser(userData: CreateUserDto): Promise<UserResponse> {
    try {
      console.log('Creating user with data:', JSON.stringify(userData, null, 2));
      const response = await api.post<UserResponse>('/users', userData);
      console.log('User created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      // Re-throw the error to be handled by the calling component
      throw error;
    }
  },

  // Update an existing user
  async updateUser(
    id: string,
    userData: UpdateUserDto
  ): Promise<UserResponse> {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  // Delete a user
  async deleteUser(id: string): Promise<DeleteUserResponse> {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  },

  // Get available user roles
  getAvailableRoles(): { value: UserRole; label: string }[] {
    return [
      { value: 'account_owner', label: 'Account Owner' },
      { value: 'property_manager', label: 'Property Manager' },
      { value: 'leasing_agent', label: 'Leasing Agent' },
      { value: 'maintenance_staff', label: 'Maintenance Staff' },
      { value: 'tenant', label: 'Tenant' },
    ];
  },

  // Get current user profile
  async getCurrentUser(): Promise<CurrentUserResponse> {
    const response = await api.get('/users/me');
    return response.data;
  },

  // Update current user profile
  async updateProfile(profileData: UpdateProfileDto): Promise<UpdateProfileResponse> {
    const response = await api.put('/users/me', profileData);
    return response.data;
  },

  // Change user password
  async changePassword(data: ChangePasswordDto): Promise<ChangePasswordResponse> {
    const response = await api.post('/users/me/change-password', data);
    return response.data;
  },

  // Format user name
  formatUserName(user: { firstName: string; lastName: string }): string {
    return `${user.firstName} ${user.lastName}`.trim();
  },

  // Get user's full name with optional format (default: 'First Last')
  getUserFullName(
    user: { firstName: string; lastName: string },
    format: 'first-last' | 'last-first' = 'first-last'
  ): string {
    const { firstName, lastName } = user;
    return format === 'first-last'
      ? `${firstName} ${lastName}`.trim()
      : `${lastName}, ${firstName}`.trim();
  },

  // Check if user has a specific role
  hasRole(user: { accountUsers: { primaryRole: UserRole; additionalRoles: UserRole[] }[] }, role: UserRole): boolean {
    return user.accountUsers.some(
      (accountUser) =>
        accountUser.primaryRole === role || accountUser.additionalRoles.includes(role)
    );
  },

  // Get user's primary account (if any)
  getPrimaryAccount(user: { accountUsers: { isDefault: boolean; accountId: string }[] }): string | null {
    const primaryAccount = user.accountUsers.find(account => account.isDefault);
    return primaryAccount ? primaryAccount.accountId : null;
  },
};

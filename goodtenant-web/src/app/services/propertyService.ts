// src/app/services/propertyService.ts

import { api } from '@/lib/api';
import { 
  Property, 
  CreatePropertyDto, 
  UpdatePropertyDto, 
  PaginatedResponse,
  AddUserToPropertyDto,
  PropertyUserResponse,
  PropertyUsersResponse
} from '@/types/property';

export const propertyService = {
  // Get all properties
  async getProperties(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Property>> {
    const response = await api.get('/properties/account', {
      params: { page, limit }
    });
    return response.data;
  },

  // Get single property by ID
  async getPropertyById(id: string): Promise<{ success: boolean; data: Property }> {
    const response = await api.get(`/properties/${id}`);
    return response.data;
  },

  // Create a new property
  async createProperty(propertyData: CreatePropertyDto): Promise<{ success: boolean; data: Property }> {
    const response = await api.post('/properties', propertyData);
    return response.data;
  },

  // Update an existing property
  async updateProperty(
    id: string,
    propertyData: UpdatePropertyDto
  ): Promise<{ success: boolean; data: Property }> {
    const response = await api.put(`/properties/${id}`, propertyData);
    return response.data;
  },

  // Delete a property
  async deleteProperty(id: string): Promise<{ success: boolean; message: string }> {
    const response = await api.delete(`/properties/${id}`);
    return response.data;
  },

  // Add user to property
  async addUserToProperty(
    propertyId: string,
    payload: AddUserToPropertyDto
  ): Promise<PropertyUserResponse> {
    const response = await api.post(`/properties/${propertyId}/users`, payload);
    return response.data;
  },

  // Remove user from property
  async removeUserFromProperty(
    propertyId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    const response = await api.delete(`/properties/${propertyId}/users/${userId}`);
    return response.data;
  },

  // Get all users for a property
  async getPropertyUsers(
    propertyId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<PropertyUsersResponse> {
    const response = await api.get(`/properties/${propertyId}/users`, {
      params: { page, limit }
    });
    return response.data;
  },
};
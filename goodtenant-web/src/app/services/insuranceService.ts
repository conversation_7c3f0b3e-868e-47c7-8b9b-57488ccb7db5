import { api } from '@/lib/api';
import type { 
  Insurance, 
  CreateInsuranceDto, 
  UpdateInsuranceDto, 
  InsuranceResponse, 
  InsurancesResponse, 
  DeleteInsuranceResponse,
  GetInsurancesQueryParams 
} from '@/types/insurance';

export type {
  Insurance,
  CreateInsuranceDto,
  UpdateInsuranceDto,
  InsuranceResponse,
  InsurancesResponse,
  DeleteInsuranceResponse,
  GetInsurancesQueryParams
};

export const insuranceService = {
  // Get all insurances with optional filtering and pagination
  async getAllInsurances(params: GetInsurancesQueryParams = {}): Promise<InsurancesResponse> {
    const { page = 1, limit = 10, search, propertyId, isActive } = params;
    const response = await api.get('/insurances', {
      params: {
        page,
        limit,
        ...(search && { search }),
        ...(propertyId && { propertyId }),
        ...(isActive !== undefined && { isActive })
      }
    });
    return response.data;
  },

  // Get insurances by property ID with pagination
  async getInsurancesByPropertyId(
    propertyId: string, 
    page: number = 1, 
    limit: number = 10,
    isActive?: boolean
  ): Promise<InsurancesResponse> {
    const response = await api.get(`/insurances/properties/${propertyId}`, {
      params: { 
        page, 
        limit,
        ...(isActive !== undefined && { isActive })
      }
    });
    return response.data;
  },

  // Get single insurance by ID
  async getInsuranceById(id: string): Promise<InsuranceResponse> {
    const response = await api.get(`/insurances/${id}`);
    return response.data;
  },

  // Create a new insurance
  async createInsurance(insuranceData: CreateInsuranceDto): Promise<InsuranceResponse> {
    const response = await api.post('/insurances', insuranceData);
    return response.data;
  },

  // Update an existing insurance
  async updateInsurance(
    id: string,
    insuranceData: UpdateInsuranceDto
  ): Promise<InsuranceResponse> {
    const response = await api.put(`/insurances/${id}`, insuranceData);
    return response.data;
  },

  // Delete an insurance
  async deleteInsurance(id: string): Promise<DeleteInsuranceResponse> {
    const response = await api.delete(`/insurances/${id}`);
    return response.data;
  },
};

import { 
  MaintenanceTicket, 
  CreateMaintenanceTicketPayload, 
  UpdateMaintenanceTicketPayload, 
  MaintenanceTicketResponse, 
  MaintenanceTicketListResponse, 
  AssignMaintenanceTicketPayload,
  UpdateMaintenanceStatusPayload,
  MaintenanceStatus,
  MaintenancePriority
} from '@/types/maintenance';
import { api } from '@/lib/api';

const BASE_PATH = '/maintenance';

export const maintenanceService = {
  /**
   * Get all maintenance tickets with pagination and filtering
   * @param page - Page number (1-based)
   * @param limit - Number of items per page
   * @param status - Filter by ticket status
   * @param priority - Filter by ticket priority
   * @param assignedTo - Filter by assigned user ID
   * @param propertyId - Filter by property ID
   * @param search - Search term to filter by title or description
   */
  async getAllMaintenanceTickets(
    page: number = 1,
    limit: number = 10,
    status?: MaintenanceStatus,
    priority?: MaintenancePriority,
    assignedTo?: string,
    propertyId?: string,
    search?: string
  ): Promise<{
    success: boolean;
    data: MaintenanceTicket[];
    pagination: {
      total: number;
      page: number;
      totalPages: number;
      limit: number;
    };
  }> {
    const response = await api.get<MaintenanceTicketListResponse>(BASE_PATH, {
      params: {
        page,
        limit,
        status: status || undefined,
        priority: priority || undefined,
        assignedTo: assignedTo || undefined,
        propertyId: propertyId || undefined,
        search: search || undefined,
      },
    });
    return response.data;
  },

  /**
   * Create a new maintenance ticket for a property
   * @param propertyId - ID of the property for which to create the ticket
   * @param data - Ticket creation payload
   */
  async createMaintenanceTicket(
    propertyId: string, 
    data: CreateMaintenanceTicketPayload
  ): Promise<MaintenanceTicketResponse> {
    const response = await api.post<MaintenanceTicketResponse>(
      `${BASE_PATH}/property/${propertyId}`, 
      data
    );
    return response.data;
  },

  /**
   * Get maintenance ticket by ID
   * @param id - Ticket ID
   */
  async getMaintenanceTicketById(id: string): Promise<MaintenanceTicketResponse> {
    const response = await api.get<MaintenanceTicketResponse>(`${BASE_PATH}/${id}`);
    return response.data;
  },

  /**
   * Assign maintenance ticket to a user
   * @param id - Ticket ID
   * @param data - Assignment payload containing assigneeId
   */
  async assignMaintenanceTicket(
    id: string, 
    data: AssignMaintenanceTicketPayload
  ): Promise<MaintenanceTicketResponse> {
    const response = await api.patch<MaintenanceTicketResponse>(
      `${BASE_PATH}/${id}/assign`, 
      data
    );
    return response.data;
  },

  /**
   * Update maintenance ticket status
   * @param id - Ticket ID
   * @param data - Status update payload
   */
  async updateMaintenanceTicketStatus(
    id: string, 
    data: UpdateMaintenanceStatusPayload
  ): Promise<MaintenanceTicketResponse> {
    const response = await api.patch<MaintenanceTicketResponse>(
      `${BASE_PATH}/${id}/status`, 
      data
    );
    return response.data;
  },

  /**
   * Update maintenance ticket details
   * @param id - Ticket ID
   * @param data - Ticket update payload
   */
  async updateMaintenanceTicket(
    id: string, 
    data: UpdateMaintenanceTicketPayload
  ): Promise<MaintenanceTicketResponse> {
    const response = await api.put<MaintenanceTicketResponse>(
      `${BASE_PATH}/${id}`, 
      data
    );
    return response.data;
  }
};

import axios from 'axios';

// Create a separate API client without auth interceptors for public endpoints
export const publicApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Don't send cookies
});

// Add response interceptor to handle errors
publicApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Public API request failed:', error);
    return Promise.reject(error);
  }
);

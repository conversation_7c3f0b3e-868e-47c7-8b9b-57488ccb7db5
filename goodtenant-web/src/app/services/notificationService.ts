import { api } from '../../lib/api';
import { NotificationType, NotificationEntity } from '../../types/notification';

interface NotificationQueryParams {
  page?: number;
  limit?: number;
  isRead?: boolean;
  type?: NotificationType;
  entityType?: NotificationEntity;
  entityId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: unknown; // For any additional query params
}

export const getNotifications = async (params: NotificationQueryParams = {}) => {
  console.log('Fetching notifications with params:', params);
  try {
    const response = await api.get('/notifications', { 
      params: {
        page: 1,
        limit: 10,
        ...params
      } 
    });
    console.log('Notifications API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
};

export const getNotificationById = async (id: string) => {
  const response = await api.get(`/notifications/${id}`);
  return response.data;
};

export const createNotification = async (data: {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  entityType?: NotificationEntity;
  entityId?: string;
  actionUrl?: string;
}) => {
  const response = await api.post('/notifications', data);
  return response.data;
};

export const markAsRead = async (id: string) => {
  const response = await api.patch(`/notifications/${id}/read`);
  return response.data;
};

export const markAllAsRead = async () => {
  const response = await api.patch('/notifications/read-all');
  return response.data;
};

export const deleteNotification = async (id: string) => {
  const response = await api.delete(`/notifications/${id}`);
  return response.data;
};

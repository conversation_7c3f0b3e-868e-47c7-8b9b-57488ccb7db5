import { api } from '@/lib/api';
import { tokenManager } from '@/lib/tokenManager';

export interface RegisterPayload {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface Tokens {
  accessToken: string;
  refreshToken: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  addressLine1?: string;
  addressLine2?: string | null;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isEmailVerified?: boolean;
  lastLogin?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  // Support both old and new API response formats
  accounts?: Account[];
  // New API structure for account users
  accountUsers?: AccountUser[];
  roles?: {
    name: string;
    isPrimary: boolean;
  }[];
}

export interface Account {
  id: string;
  status: string;
  plan: string;
  isDefault: boolean;
  currentPeriodStart?: string | null;
  currentPeriodEnd?: string | null;
  cancelAtPeriodEnd?: boolean;
  stripeCustomerId?: string | null;
  stripeSubscriptionId?: string | null;
  paymentMethodId?: string | null;
  trialEnd?: string | null;
  createdAt?: string;
  updatedAt?: string;
  roles?: {
    primaryRole: string;
    additionalRoles: string[];
  };
}

// New interface for account user relationship from updated API
export interface AccountUser {
  id: string;
  accountId: string;
  isDefault: boolean;
  primaryRole: string;
  additionalRoles: string[];
}


export interface RegisterResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    tokens: Tokens;
  };
}

export interface LoginPayload {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    tokens: Tokens;
  };
}

interface LogoutResponse {
  success: boolean;
  message: string;
}

interface CurrentUserResponse {
  success: boolean;
  data: User;
}

interface RequestPasswordResetResponse {
  success: boolean;
  message: string;
}


export interface ResetPasswordResponse {
  success: boolean;
  message: string;
}

export interface VerifyEmailResponse {
  success: boolean;
  message: string;
  code: string;
  data?: {
    user: User;
  };
}

export const authService = {
  /**
   * Login user with email and password
   */
  async login(credentials: LoginPayload): Promise<LoginResponse> {
    try {
      const response = await api.post<LoginResponse>('/auth/login', credentials);
      
      // Store tokens using token manager
      if (response.data?.data?.tokens) {
        tokenManager.storeTokens(response.data.data.tokens);
      }
      
      return response.data;
    } catch (error) {
      console.error('Login failed:', error);
      tokenManager.clearTokens();
      throw error;
    }
  },

  /**
   * Register a new user
   */
  async register(payload: RegisterPayload): Promise<RegisterResponse> {
    try {
      const response = await api.post<RegisterResponse>('/auth/register', payload);
      
      // Store tokens using token manager
      if (response.data?.data?.tokens) {
        tokenManager.storeTokens(response.data.data.tokens);
      }
      
      return response.data;
    } catch (error) {
      console.error('Registration failed:', error);
      tokenManager.clearTokens();
      throw error;
    }
  },

  /**
   * Logout the current user
   */
  async logout(): Promise<LogoutResponse> {
    try {
      const response = await api.post<LogoutResponse>('/auth/logout', {});
      return response.data;
    } catch (error) {
      console.error('Logout API call failed:', error);
      throw error;
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return tokenManager.isAuthenticated();
  },

  /**
   * Get stored access token
   */
  getAccessToken(): string | null {
    return tokenManager.getToken('access');
  },

  /**
   * Get current user data using the stored access token
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await api.get<CurrentUserResponse>('/users/me');
      
      if (!response.data.success || !response.data.data) {
        throw new Error('Failed to get current user data');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('Get current user failed:', error);
      throw error;
    }
  },

  /**
   * Request a password reset email
   * @param email User's email address
   */
  async requestPasswordReset(email: string): Promise<RequestPasswordResetResponse> {
    try {
      const response = await api.post<RequestPasswordResetResponse>(
        '/auth/request-password-reset',
        { email }
      );
      return response.data;
    } catch (error) {
      console.error('Request password reset failed:', error);
      throw error;
    }
  },

  /**
   * Reset user's password using a reset token
   * @param token Password reset token from email
   * @param newPassword New password to set
   */
  async resetPassword(token: string, newPassword: string): Promise<ResetPasswordResponse> {
    return api
      .post<ResetPasswordResponse>('/auth/reset-password', {
        token,
        newPassword,
      })
      .then((response) => response.data);
  },

  /**
   * Verify user's email using a verification token
   * @param token Email verification token from the verification link
   */
  async verifyEmail(token: string): Promise<VerifyEmailResponse> {
    return api
      .get<VerifyEmailResponse>('/auth/verify-email', {
        params: { token }
      })
      .then((response) => response.data);
  },
};

export default authService;
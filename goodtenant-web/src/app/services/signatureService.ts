// goodtenant-web/src/services/signatureService.ts
import { ApiError } from '@/lib/errors';
import { tenantService } from './tenantService';
import { publicApi } from './publicApi';

export type SignatureData = {   
  id: string;
  url: string;
  signedUrl?: string;  // Added to match API response
  expiresAt?: string;  // Added to match API response
  createdAt: string;
  updatedAt: string;
};

/**
 * Upload a new signature for the current user
 * @param file - The signature file to upload
 * @returns Promise with the uploaded signature data
 */
export const uploadSignature = async (file: File): Promise<SignatureData> => {
  try {
    const formData = new FormData();
    formData.append('signature', file);

    const response = await publicApi.post('/signature/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw ApiError.fromAxiosError(error, 'Failed to upload signature');
  }
};

/**
 * Get the current user's signature
 * @returns Promise with the user's signature data or null if not found
 */
export const getSignature = async (): Promise<SignatureData | null> => {
  try {
    const response = await publicApi.get('/signature');
    return response.data || null;
  } catch (error: unknown) {
    // Return null if no signature exists (404) or propagate other errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { status?: number; data?: { message?: string } } };
      if (axiosError.response?.status === 404) {
        return null;
      }
      throw new ApiError(
        axiosError.response?.data?.message || 'Failed to get signature',
        axiosError.response?.status || 500
      );
    }
    throw new ApiError('An unexpected error occurred while getting signature', 500);
  }
};

/**
 * Delete the current user's signature
 * @returns Promise that resolves when the signature is deleted
 */
export const deleteSignature = async (): Promise<void> => {
  try {
    await publicApi.delete('/signature');
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as {
        response?: {
          data?: { message?: string };
          status?: number;
        };
      };
      throw new ApiError(
        apiError.response?.data?.message || 'Failed to delete signature',
        apiError.response?.status || 500
      );
    }
    throw new ApiError('Failed to delete signature', 500);
  }
};

/**
 * Convert a base64 string to a File object
 * @param base64 - The base64 string of the signature
 * @param filename - The name of the file
 * @returns A File object
 */
export const base64ToFile = (base64: string, filename: string): File => {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime || 'image/png' });
};

/**
 * Convert a signature to a base64 string
 * @param signature - The signature data URL or File object
 * @returns A promise that resolves to a base64 string
 */
export const getBase64 = async (signature: string | File): Promise<string> => {
  if (typeof signature === 'string') {
    return signature;
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(signature);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

interface LeaseDocumentResponse {
  success: boolean;
  data: {
    documentContent: any;
    documentName: string;
    templateId: string;
    leaseId: string;
    landlordSignature?: string; // Optional field for backward compatibility
  };
  message?: string;
}

/**
 * Get the account owner's (landlord) signature by lease ID
 * @param leaseId - The ID of the lease
 * @param token - Tenant invitation token for authentication
 * @returns Promise with the signature URL or null if not found
 */
export const getLandlordSignatureByLease = async (leaseId: string, token: string): Promise<string | null> => {
    // If we have a token, try to fetch using the public endpoint first
    if (token) {
      try {
        const url = `/public/leases/${leaseId}/landlord-signature/${encodeURIComponent(token)}`;
        console.log('Fetching landlord signature from public endpoint:', url);
        
        const response = await publicApi.get(url, {
          validateStatus: (status) => status < 500,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        
        console.log('Landlord signature response status:', response.status, 'Data:', response.data);
        
        if (response.status === 200 && response.data?.data?.signedUrl) {
          return response.data.data.signedUrl;
        }
        console.log('No signature found via public endpoint, trying regular endpoint');
      } catch (error) {
        console.warn('Error fetching from public endpoint, falling back to regular endpoint:', error);
      }
    }
    
    // Fall back to regular endpoint (for authenticated users)
    try {
      console.log('Attempting to fetch lease document for leaseId:', leaseId);
      const leaseDoc = await tenantService.getLeaseDocument(leaseId, token) as LeaseDocumentResponse;
      
      if (leaseDoc?.data?.landlordSignature) {
        console.log('Found landlord signature in lease document');
        return leaseDoc.data.landlordSignature;
      }
      
      console.log('No landlord signature found in lease document');
      return null;
    } catch (error) {
      console.warn('Error fetching lease document:', error);
      
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number } };
        if (axiosError.response?.status === 404) {
          return null; // No signature exists for this landlord
        }
      }
      
      // Only throw if it's not a 404
      throw ApiError.fromAxiosError(error, 'Failed to fetch landlord signature');
    }
  };

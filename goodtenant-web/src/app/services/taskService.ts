import { 
  Task, 
  CreateTaskPayload, 
  UpdateTaskPayload, 
  TaskResponse, 
  TaskListResponse,
  TaskQueryParams
} from '@/types/task';
import { api } from '@/lib/api';

const BASE_PATH = '/tasks';

export const taskService = {
  /**
   * Get all tasks with pagination and filters
   * @param params - Query parameters for filtering, sorting, and pagination
   */
  async getAllTasks(params: TaskQueryParams = {}): Promise<TaskListResponse> {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      priority,
      type,
      assignedTo,
      propertyId,
    } = params;

    const response = await api.get<TaskListResponse>(BASE_PATH, {
      params: {
        page,
        limit,
        search: search || undefined,
        status: status || undefined,
        priority: priority || undefined,
        type: type || undefined,
        assignedTo: assignedTo || undefined,
        propertyId: propertyId || undefined,
      },
    });

    return response.data;
  },

  /**
   * Create a new task
   */
  async createTask(data: CreateTaskPayload): Promise<TaskResponse> {
    const response = await api.post<TaskResponse>(BASE_PATH, data);
    return response.data;
  },

  /**
   * Get task by ID
   */
  async getTaskById(id: string): Promise<TaskResponse> {
    const response = await api.get<TaskResponse>(`${BASE_PATH}/${id}`);
    return response.data;
  },

  /**
   * Update an existing task
   */
  async updateTask(id: string, data: UpdateTaskPayload): Promise<TaskResponse> {
    const response = await api.put<TaskResponse>(`${BASE_PATH}/${id}`, data);
    return response.data;
  },

  /**
   * Delete a task
   */
  async deleteTask(id: string): Promise<{ success: boolean; message: string }> {
    const response = await api.delete<{ success: boolean; message: string }>(
      `${BASE_PATH}/${id}`
    );
    return response.data;
  },


};

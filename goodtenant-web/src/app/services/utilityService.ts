import { api } from '@/lib/api';
import {
  Utility,
  CreateUtilityDto,
  UpdateUtilityDto,
  UtilityResponse,
  UtilitiesResponse,
  DeleteUtilityResponse,
  GetUtilitiesQueryParams,
  UtilityType
} from '@/types/utility';

export const utilityService = {
  // Get all utilities with optional filtering and pagination
  async getAllUtilities(params: GetUtilitiesQueryParams = {}): Promise<UtilitiesResponse> {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      propertyId, 
      utilityType, 
      isActive 
    } = params;
    
    const response = await api.get('/utilities', {
      params: {
        page,
        limit,
        ...(search && { search }),
        ...(propertyId && { propertyId }),
        ...(utilityType && { utilityType }),
        ...(isActive !== undefined && { isActive: isActive.toString() })
      }
    });
    return response.data;
  },

  // Get utilities by property ID with pagination
  async getUtilitiesByPropertyId(
    propertyId: string, 
    page: number = 1, 
    limit: number = 10
  ): Promise<UtilitiesResponse> {
    const response = await api.get(`/utilities/properties/${propertyId}`, {
      params: { page, limit }
    });
    return response.data;
  },

  // Get single utility by ID
  async getUtilityById(id: string): Promise<UtilityResponse> {
    const response = await api.get(`/utilities/${id}`);
    return response.data;
  },

  // Create a new utility
  async createUtility(utilityData: CreateUtilityDto): Promise<UtilityResponse> {
    const response = await api.post('/utilities', utilityData);
    return response.data;
  },

  // Update an existing utility
  async updateUtility(
    id: string,
    utilityData: UpdateUtilityDto
  ): Promise<UtilityResponse> {
    const response = await api.put(`/utilities/${id}`, utilityData);
    return response.data;
  },

  // Delete a utility
  async deleteUtility(id: string): Promise<DeleteUtilityResponse> {
    const response = await api.delete(`/utilities/${id}`);
    return response.data;
  },

  // Get all utility types
  getUtilityTypes(): { value: UtilityType; label: string }[] {
    return [
      { value: 'electricity', label: 'Electricity' },
      { value: 'water', label: 'Water' },
      { value: 'gas', label: 'Gas' },
      { value: 'internet', label: 'Internet' },
      { value: 'trash', label: 'Trash' },
      { value: 'sewer', label: 'Sewer' },
      { value: 'other', label: 'Other' },
    ];
  },
};

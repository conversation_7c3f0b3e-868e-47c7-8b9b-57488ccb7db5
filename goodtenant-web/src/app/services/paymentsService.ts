import { api, stripeApi } from '@/lib/api';
import {
  Payment,
  CreatePaymentDto,
  UpdatePaymentDto,
  PaymentResponse,
  PaymentsResponse,
  DeletePaymentResponse,
  GetPaymentsQueryParams,
  CheckoutSessionResponse,
  CheckoutSessionParams,
  CheckoutSuccessResponse,
} from '@/types/payment';

export const paymentService = {

  // Get all payments with optional filtering and pagination
  async getPayments(params: GetPaymentsQueryParams = {}): Promise<PaymentsResponse> {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      status,
      paymentMethod,
      startDate,
      endDate,
      payerId,
      receiverId,
      accountId
    } = params;
    
    const response = await api.get('/payments', {
      params: {
        page,
        limit,
        ...(search && { search }),
        ...(status && { status }),
        ...(paymentMethod && { paymentMethod }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
        ...(payerId && { payerId }),
        ...(receiverId && { receiverId }),
        ...(accountId && { accountId }),
      }
    });
    return response.data;
  },

  // Get single payment by ID
  async getPaymentById(id: string): Promise<PaymentResponse> {
    const response = await api.get(`/payments/${id}`);
    return response.data;
  },

  /**
   * Create a new payment record
   */
  async createPayment(paymentData: CreatePaymentDto): Promise<PaymentResponse> {
    const response = await api.post('/payments', {
      ...paymentData,
      metadata: {
        ...paymentData.metadata,
        createdVia: 'manual',
      },
    });
    return response.data;
  },

  /**
   * Update an existing payment
   */
  async updatePayment(
    id: string,
    paymentData: UpdatePaymentDto
  ): Promise<PaymentResponse> {
    const response = await api.put(`/payments/${id}`, {
      ...paymentData,
      metadata: {
        ...paymentData.metadata,
        updatedAt: new Date().toISOString(),
      },
    });
    return response.data;
  },

  /**
   * Delete a payment (soft delete)
   */
  async deletePayment(id: string): Promise<DeletePaymentResponse> {
    const response = await api.delete(`/payments/${id}`);
    return response.data;
  },

  /**
   * Get payments by account ID
   */
  async getPaymentsByAccountId(accountId: string, params: Omit<GetPaymentsQueryParams, 'accountId'> = {}): Promise<PaymentsResponse> {
    return this.getPayments({ ...params, accountId });
  },

  /**
   * Get payments by user ID (as payer)
   */
  async getPaymentsByUserId(userId: string, params: Omit<GetPaymentsQueryParams, 'payerId'> = {}): Promise<PaymentsResponse> {
    return this.getPayments({ ...params, payerId: userId });
  },

  /**
   * Create a new Stripe Checkout session
   * @param params Checkout session parameters
   */
  async createCheckoutSession(params: CheckoutSessionParams): Promise<CheckoutSessionResponse> {
    const successUrl = new URL(params.success_url);
    successUrl.pathname = successUrl.pathname.replace(/\/success$/, '');
    
    // Ensure cancel_url is properly formatted
    const cancelUrl = new URL(params.cancel_url);
    
    const response = await api.post('/payments/checkout', {
      amount: Math.round(Number(params.amount) * 100), // Convert to cents
      currency: params.currency || 'usd',
      description: params.description,
      metadata: {
        ...params.metadata,
        success_url: successUrl.toString(),
        cancel_url: cancelUrl.toString(), // Add cancel_url to metadata as well
      },
      success_url: successUrl.toString(),
      cancel_url: cancelUrl.toString(),
      payment_method_types: params.payment_method_types || ['card'],
      mode: 'payment'
    });
    
    console.log('Stripe session created with URLs:', {
      success_url: successUrl.toString(),
      cancel_url: cancelUrl.toString(),
      request_payload: {
        success_url: successUrl.toString(),
        cancel_url: cancelUrl.toString(),
        metadata: {
          ...params.metadata,
          success_url: successUrl.toString(),
          cancel_url: cancelUrl.toString()
        }
      }
    });
    
    return response.data;
  },

  /**
   * Get checkout session details
   */
  async getCheckoutSession(sessionId: string): Promise<CheckoutSessionResponse> {
    const response = await api.get(`/payments/checkout/session/${sessionId}`);
    return response.data;
  },

  /**
   * Handle successful payment redirect
   */
  async handleSuccessRedirect(sessionId: string): Promise<CheckoutSuccessResponse> {
    const response = await api.get(`/payments/checkout/success?session_id=${sessionId}`);
    return response.data;
  }
};
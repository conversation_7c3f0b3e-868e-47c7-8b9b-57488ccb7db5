import { UploadFileParams, FileMetadata } from '@/types/file';

interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}
import { api } from '@/lib/api';

type EntityType = 'Property' | 'HOA' | 'Insurance' | 'Loan' | 'Tax' | 'Maintenance' | 'Utility' | 'Inventory' | 'Lease';

interface FileServiceEntityMethods<T extends EntityType> {
  uploadFile(params: Omit<UploadFileParams<T>, 'entityType'>): Promise<FileMetadata>;
  getFiles(entityId: string): Promise<FileMetadata[]>;
  getSignedUrl(fileKey: string): Promise<string>;
  deleteFile(fileId: string): Promise<void>;
}

class FileService<T extends EntityType> implements FileServiceEntityMethods<T> {
  constructor(private entityType: T) {}

  private handleApiResponse<T>(response: {
    data: ApiResponse<T>;
  }): T {
    // Extract the data from the API response
    if (response.data && response.data.success === true && response.data.data !== undefined) {
      return response.data.data;
    }
    throw new Error(response.data?.message || 'Invalid API response format');
  }

  async uploadFile({
    file,
    entityId,
    description = '',
    onUploadProgress,
  }: Omit<UploadFileParams<T>, 'entityType'>): Promise<FileMetadata> {
    const formData = new FormData();
    formData.append('file', file);
    if (description) {
      formData.append('description', description);
    }

    try {
      const response = await api.post<ApiResponse<FileMetadata>>(
        `/files/${this.entityType}/${entityId}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (onUploadProgress && progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onUploadProgress(progress);
            }
          },
        }
      );

      return this.handleApiResponse<FileMetadata>(response);
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getFiles(entityId: string): Promise<FileMetadata[]> {
    try {
      console.log(`Fetching files for ${this.entityType} with ID:`, entityId);
      const response = await api.get(`/files/${this.entityType}/${entityId}`);
      
      console.log('Raw API response:', response);
      
      // Handle different possible response structures
      let files: FileMetadata[] = [];
      
      // Case 1: Direct array response
      if (Array.isArray(response.data)) {
        files = response.data;
      } 
      // Case 2: Standard success/data structure
      else if (response.data?.success && Array.isArray(response.data.data)) {
        files = response.data.data;
      }
      // Case 3: Nested success/data structure (legacy)
      else if (response.data?.data?.success && Array.isArray(response.data.data.data)) {
        files = response.data.data.data;
      }
      
      console.log('Extracted files:', files);
      
      if (!files || files.length === 0) {
        console.log('No files found for this entity');
        return [];
      }
      
      // Get signed URLs for all files if not already provided
      const filesWithUrls = await Promise.all(
        files.map(async (file) => {
          try {
            if (!file.url) {
              const signedUrl = await this.getSignedUrl(file.fileKey);
              return { ...file, url: signedUrl };
            }
            return file;
          } catch (error) {
            console.error('Error getting signed URL for file:', file.fileKey, error);
            return file;
          }
        })
      );

      return filesWithUrls;
    } catch (error) {
      console.error('Error fetching files:', error);
      throw new Error(`Failed to fetch files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getSignedUrl(fileKey: string): Promise<string> {
    try {
      const encodedFileKey = encodeURIComponent(fileKey);
      const response = await api.get<ApiResponse<{ url: string }>>(
        `/files/download/${this.entityType}/${encodedFileKey}`
      );
      
      const result = this.handleApiResponse<{ url: string }>(response);
      if (!result?.url) {
        throw new Error('No URL returned from server');
      }
      
      return result.url;
    } catch (error) {
      console.error('Error getting signed URL:', error);
      throw new Error(`Failed to get signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      const response = await api.delete<ApiResponse<Record<string, unknown>>>(
        `/files/${this.entityType}/${fileId}`
      );
      
      // The handleApiResponse will throw if the request was not successful
      await this.handleApiResponse<Record<string, unknown>>(response);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Create pre-configured instances for each entity type
export const propertyFiles = new FileService('Property');
export const hoaFiles = new FileService('HOA');
export const insuranceFiles = new FileService('Insurance');
export const loanFiles = new FileService('Loan');
export const taxFiles = new FileService('Tax');
export const maintenanceFiles = new FileService('Maintenance');
export const utilityFiles = new FileService('Utility');
export const inventoryFiles = new FileService('Inventory');
export const leaseFiles = new FileService('Lease');

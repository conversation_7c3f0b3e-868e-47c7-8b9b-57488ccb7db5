// src/app/services/tenantService.ts

import { api } from '@/lib/api';
import { publicApi } from './publicApi';
import {
  TenantInvitation,
  CreateTenantInvitationDto,
  PaginatedTenantInvitations,
  TenantInvitationResponse,
  ResendInvitationResponse,
  CancelInvitationResponse,
  VerifyInvitationResponse,
  CompleteOnboardingRequest,
  CompleteOnboardingResponse,
  TenantInvitationStatus,
  LeaseDocumentResponse,
  PetDocumentResponse,
  HoaDocumentResponse,
  FileUploadResponse,
  FinalizeOnboardingResponse,
} from '@/types/tenant';

export type FileUploadParams = {
  file: File;
  modelName: 'Property' | 'HOA' | 'Lease' | 'Inventory';
  modelId: string;
  description?: string;
};

export const tenantService = {
  // Create a tenant invitation for a property
  async createTenantInvitation(
    propertyId: string,
    invitationData: CreateTenantInvitationDto
  ): Promise<TenantInvitationResponse> {
    const response = await api.post(
      `/tenants/properties/invitations/${propertyId}`,
      invitationData
    );
    return response.data;
  },

  // Get all tenant invitations for a property
  async getPropertyTenantInvitations(
    propertyId: string,
    page: number = 1,
    limit: number = 10,
    status?: TenantInvitationStatus
  ): Promise<PaginatedTenantInvitations> {
    const response = await api.get(`/tenants/properties/invitations/${propertyId}`, {
      params: { page, limit, status }
    });
    return response.data;
  },

  // Resend a tenant invitation
  async resendTenantInvitation(
    invitationId: string
  ): Promise<ResendInvitationResponse> {
    const response = await api.post(
      `/tenants/invitations/${invitationId}/resend`,
      {}
    );
    return response.data;
  },

  // Cancel a tenant invitation
  async cancelTenantInvitation(
    invitationId: string
  ): Promise<CancelInvitationResponse> {
    const response = await api.delete(`/tenants/invitations/${invitationId}`);
    return response.data;
  },

  // Verify an invitation token
  async verifyInvitationToken(
    token: string
  ): Promise<VerifyInvitationResponse> {
    const response = await api.get(`/public/invitations/${token}/verify`);
    return response.data;
  },

  // Complete tenant onboarding
  async completeTenantOnboarding(
    token: string,
    requestData: CompleteOnboardingRequest
  ): Promise<CompleteOnboardingResponse> {
    const response = await api.post(
      `/public/invitations/${token}/complete`,
      requestData
    );
    
    // Store the successful response in localStorage for later use
    if (response.data && response.data.success && typeof window !== 'undefined') {
      try {
        localStorage.setItem(`onboarding-completion-${token}`, JSON.stringify(response.data));
        console.log('Stored onboarding completion data in localStorage');
      } catch (err) {
        console.error('Failed to store onboarding completion data:', err);
      }
    }
    
    return response.data;
  },

  // Get lease document content
  async getLeaseDocument(
    leaseId: string,
    invitationToken: string
  ): Promise<LeaseDocumentResponse> {
    const response = await publicApi.get(
      `/public/leases/${leaseId}/document/${invitationToken}`
    );
    return response.data;
  },

  // Get pet document content
  async getPetDocument(
    leaseId: string,
    invitationToken: string
  ): Promise<PetDocumentResponse> {
    const response = await publicApi.get(
      `/public/leases/${leaseId}/pet-document/${invitationToken}`
    );
    return response.data;
  },

  // Get HOA document content
  async getHoaDocument(
    leaseId: string,
    invitationToken: string
  ): Promise<HoaDocumentResponse> {
    const response = await publicApi.get(
      `/public/leases/${leaseId}/hoa-document/${invitationToken}`
    );
    return response.data;
  },

  // Finalize tenant onboarding
  async finalizeTenantOnboarding(
    invitationToken: string
  ): Promise<FinalizeOnboardingResponse> {
    const response = await api.patch(
      `/public/invitations/${invitationToken}/finalize`
    );
    return response.data;
  },

  // Upload file using token invitation
  async uploadFile(
    token: string,
    params: FileUploadParams
  ): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', params.file);
    formData.append('modelName', params.modelName);
    formData.append('modelId', params.modelId);
    if (params.description) {
      formData.append('description', params.description);
    }

    const response = await api.post(
      `/public/upload/${token}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data;
  },

  // Get onboarding status
  async getOnboardingStatus(
    token: string
  ): Promise<{
    success: boolean;
    data?: {
      lease: {
        adultOccupants: Array<{ firstName: string; lastName: string }>;
      };
    };
    message?: string;
  }> {
    try {
      const response = await api.get(`/public/invitations/${token}/status`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching onboarding status:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to fetch onboarding status';
      return { 
        success: false, 
        message: errorMessage
      };
    }
  },
};

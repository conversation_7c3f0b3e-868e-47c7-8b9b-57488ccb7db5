import { StripeConfigResponse, UpdateStripeConfigPayload, UpdateStripeConfigResponse } from '@/types/stripe';
import { api } from '@/lib/api';

const BASE_PATH = '/stripe';

export const stripeService = {
  /**
   * Get Stripe publishable key for the current account
   * @param accountId - The ID of the account to get the publishable key for
   */
  async getPublishableKey(accountId: string): Promise<StripeConfigResponse> {
    try {
      const response = await api.get<StripeConfigResponse>(`${BASE_PATH}/config`, {
        params: { accountId },
      });
      return response.data;
    } catch (error) {
      console.error('Error getting Stripe publishable key:', error);
      throw error;
    }
  },

  /**
   * Update Stripe configuration for an account
   * @param accountId - The ID of the account to update
   * @param config - The Stripe configuration including secret and publishable keys
   */
  async updateStripeConfig(
    accountId: string,
    config: UpdateStripeConfigPayload
  ): Promise<UpdateStripeConfigResponse> {
    try {
      const response = await api.put<UpdateStripeConfigResponse>(
        `${BASE_PATH}/config/${accountId}`,
        config
      );
      return response.data;
    } catch (error) {
      console.error('Error updating Stripe config:', error);
      throw error;
    }
  },
};

import { api } from '@/lib/api';
import { TenantPaymentStatusResponse } from '@/types/tenantPayment';

export const tenantPaymentService = {
  /**
   * Get the current payment status for the authenticated tenant
   * @returns {Promise<TenantPaymentStatusResponse>} The payment status response
   */
  async getPaymentStatus(): Promise<TenantPaymentStatusResponse> {
    const response = await api.get('/tenant-payment/status');
    return response.data;
  },

  // Additional payment-related methods can be added here in the future
  // For example:
  // - makePayment
  // - getPaymentHistory
  // - updatePaymentMethod
  // etc.
};

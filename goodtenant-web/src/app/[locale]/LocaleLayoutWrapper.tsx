// Client component wrapper for handling locale
'use client';

import React from 'react';
import { RootProviders } from '@/components/providers/RootProviders';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import { cn } from '@/lib/utils';

const geistSans = Geist({ variable: '--font-geist-sans', subsets: ['latin'] });
const geistMono = Geist_Mono({ variable: '--font-geist-mono', subsets: ['latin'] });

type LocaleLayoutWrapperProps = {
  children: React.ReactNode;
  locale: string;
};

export default function LocaleLayoutWrapper({ children, locale }: LocaleLayoutWrapperProps) {
  return (
    <html lang={locale} suppressHydrationWarning>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          geistSans.variable,
          geistMono.variable,
        )}
      >
        <RootProviders locale={locale}>
          {children}
        </RootProviders>
      </body>
    </html>
  );
}

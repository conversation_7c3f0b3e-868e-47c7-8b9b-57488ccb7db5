'use client';

import { Badge } from '@/components/ui/badge';
import { Mail, Phone, Eye, Pencil, Trash2, MoreHorizontal, X } from 'lucide-react';
import Link from 'next/link';
import { User } from '@/types/user';
import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

type UserMobileCardProps = {
  user: User;
  locale: string;
  onRowClick?: (user: User) => void;
};

export function UserMobileCard({ user, locale, onRowClick }: UserMobileCardProps) {
  const name = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unnamed User';
  const initials = (user.firstName?.[0] || '') + (user.lastName?.[0] || '');
  
  // Handle click on the card
  const handleClick = () => {
    if (onRowClick) {
      onRowClick(user);
    }
  };
  
  // Get roles from the first account (or use empty arrays if no accounts)
  const accountRoles = (user as any).accounts?.[0]?.accountUser;
  const primaryRole = accountRoles?.primaryRole;
  const additionalRoles = accountRoles?.additionalRoles || [];
  
  const roleVariant = (role: string) => ({
    account_owner: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    property_manager: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    leasing_agent: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200',
    maintenance_staff: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200',
    tenant: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  }[role] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200');
  
  const formatRole = (role: string) => {
    return role
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const allRoles = [primaryRole, ...additionalRoles].filter(Boolean) as string[];

  const [isOpen, setIsOpen] = useState(false);
  const [startX, setStartX] = useState(0);
  const [currentX, setCurrentX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const actionsRef = useRef<HTMLDivElement>(null);
  const actionsWidth = 240; // Total width of all action buttons

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
    setCurrentX(isOpen ? -actionsWidth : 0);
    setIsDragging(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    const touch = e.touches[0];
    const diff = startX - touch.clientX;
    
    // Only allow swiping left (negative diff)
    if (diff > 0) {
      setCurrentX(Math.max(-actionsWidth, -diff));
    } else {
      setCurrentX(Math.min(0, -diff));
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    
    // If swiped more than half of actions width, open the actions
    if (currentX < -actionsWidth / 2) {
      setCurrentX(-actionsWidth);
      setIsOpen(true);
    } else {
      setCurrentX(0);
      setIsOpen(false);
    }
  };

  const closeActions = () => {
    setCurrentX(0);
    setIsOpen(false);
  };

  // Close actions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && cardRef.current && !cardRef.current.contains(event.target as Node)) {
        closeActions();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative overflow-visible px-2 py-1.5">
      <div 
        ref={cardRef}
        className="relative transition-transform duration-300 ease-out"
        style={{ transform: `translateX(${currentX}px)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Main Card Content */}
        <div 
          className="flex items-start justify-between bg-card rounded-xl border border-border/40 transition-all duration-300 relative overflow-hidden hover:-translate-y-0.5 shadow-lg hover:shadow-xl [box-shadow:0_4px_12px_rgba(0,0,0,0.08)] hover:[box-shadow:0_6px_24px_rgba(0,0,0,0.12)]"
          onClick={(e) => {
            if (!isDragging) {
              handleClick();
            }
          }}
        >
          {/* Gradient left border with rounded corners */}
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary to-primary/70 rounded-l-xl" />
          <div className="flex items-start p-4 w-full pl-5">
            <div className="flex-shrink-0 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
              {initials || '👤'}
            </div>
            <div className="space-y-1">
              <h3 className="font-medium">{name}</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              
              <div className="flex items-center space-x-2 mt-1">
                {user.phone && (
                  <a 
                    href={`tel:${user.phone}`}
                    className="text-xs text-blue-600 hover:underline flex items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Phone className="h-3 w-3 mr-1" />
                    {user.phone}
                  </a>
                )}
                {user.email && (
                  <a 
                    href={`mailto:${user.email}`}
                    className="text-xs text-blue-600 hover:underline flex items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Mail className="h-3 w-3 mr-1" />
                    Email
                  </a>
                )}
              </div>
              
              <div className="flex flex-wrap gap-1 mt-1">
                {allRoles.slice(0, 2).map((role, index) => (
                  <Badge 
                    key={index} 
                    variant="outline"
                    className={cn('text-xs whitespace-nowrap', roleVariant(role))}
                  >
                    {formatRole(role)}
                  </Badge>
                ))}
                {allRoles.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{allRoles.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <button 
            className="p-4 text-muted-foreground hover:text-foreground self-center"
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(!isOpen);
              setCurrentX(isOpen ? 0 : -actionsWidth);
            }}
          >
            {isOpen ? <X className="h-4 w-4" /> : <MoreHorizontal className="h-4 w-4" />}
          </button>
        </div>
        
        {/* Actions Panel - Only render when needed */}
        {(isOpen || Math.abs(currentX) > 10) && (
          <div 
            ref={actionsRef}
            className="absolute top-0 right-0 bottom-0 flex items-center"
            style={{ 
              width: `${actionsWidth}px`,
              transform: `translateX(${actionsWidth}px)`,
              background: 'transparent',
              pointerEvents: 'auto' // Ensure the panel is clickable when visible
            }}
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking actions
          >
            <div className="flex h-full items-center space-x-1.5 px-1.5">
              <Link 
                href={`/${locale}/users/${user.id}?view=true`}
                className="flex items-center justify-center h-9 w-9 bg-white/80 hover:bg-white text-gray-700 hover:text-blue-600 dark:bg-gray-800/90 dark:text-gray-300 dark:hover:text-blue-400 rounded-lg shadow-sm hover:shadow transition-all duration-200"
                onClick={(e) => e.stopPropagation()}
                title="View details"
              >
                <Eye className="h-4 w-4" />
              </Link>
              <Link 
                href={`/${locale}/users/${user.id}?edit=true`}
                className="flex items-center justify-center h-9 w-9 bg-white/80 hover:bg-white text-gray-700 hover:text-amber-600 dark:bg-gray-800/90 dark:text-gray-300 dark:hover:text-amber-400 rounded-lg shadow-sm hover:shadow transition-all duration-200"
                onClick={(e) => e.stopPropagation()}
                title="Edit user"
              >
                <Pencil className="h-4 w-4" />
              </Link>
              {user.email && (
                <a 
                  href={`mailto:${user.email}`}
                  className="flex items-center justify-center h-9 w-9 bg-white/80 hover:bg-white text-gray-700 hover:text-green-600 dark:bg-gray-800/90 dark:text-gray-300 dark:hover:text-green-400 rounded-lg shadow-sm hover:shadow transition-all duration-200"
                  onClick={(e) => e.stopPropagation()}
                  title="Send email"
                >
                  <Mail className="h-4 w-4" />
                </a>
              )}
              {user.phone && (
                <a 
                  href={`tel:${user.phone}`}
                  className="flex items-center justify-center h-9 w-9 bg-white/80 hover:bg-white text-gray-700 hover:text-purple-600 dark:bg-gray-800/90 dark:text-gray-300 dark:hover:text-purple-400 rounded-lg shadow-sm hover:shadow transition-all duration-200"
                  onClick={(e) => e.stopPropagation()}
                  title="Call"
                >
                  <Phone className="h-4 w-4" />
                </a>
              )}
              <Link 
                href={`/${locale}/users/${user.id}?delete=true`}
                className="flex items-center justify-center h-9 w-9 bg-white/80 hover:bg-white text-gray-700 hover:text-red-600 dark:bg-gray-800/90 dark:text-gray-300 dark:hover:text-red-400 rounded-lg shadow-sm hover:shadow transition-all duration-200"
                onClick={(e) => e.stopPropagation()}
                title="Delete user"
              >
                <Trash2 className="h-4 w-4" />
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

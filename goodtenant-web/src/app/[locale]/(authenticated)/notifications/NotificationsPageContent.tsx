"use client";

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Client component that handles the dynamic import
const NotificationsClient = dynamic(
  () => import('./NotificationsClient').then(mod => mod.default),
  { 
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }
);

export default function NotificationsPageContent() {
  return <NotificationsClient />;
}

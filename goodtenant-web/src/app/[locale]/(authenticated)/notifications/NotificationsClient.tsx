"use client";

import { useTranslations } from 'next-intl';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2, Bell } from 'lucide-react';
import { useEffect, useState } from 'react';
import NotificationsListWrapper from '@/components/notifications/NotificationsListWrapper';

function ErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      event.preventDefault();
      console.error('Error in ErrorBoundary:', event.error);
      setHasError(true);
      setError(event.error);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error?.message || 'Something went wrong loading notifications'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
}

function NotificationsClient() {
  const t = useTranslations();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 py-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8 pb-6 border-b border-gray-200 dark:border-gray-800">
          <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-3">
                <div className="p-2.5 rounded-lg bg-primary/10 text-primary shadow-sm">
                  <Bell className="h-5 w-5" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold tracking-tight text-foreground">
                    {t('notificationTitle')}
                  </h1>
                  <p className="text-muted-foreground text-sm mt-1">
                    {t('notificationDescription')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-card rounded-lg border">
          <ErrorBoundary>
            <NotificationsListWrapper />
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
}

export default NotificationsClient;

'use client';

import { useState, useEffect } from 'react';
import { useRouter, use<PERSON>arams } from 'next/navigation';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { toast } from 'sonner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, SubmitHandler } from 'react-hook-form';
import * as z from 'zod';
import { 
  Template,
  TemplateType, 
  TemplateContent, 
  CreateTemplatePayload, 
  SystemVariable 
} from '@/types/template';
import { templateService } from '@/app/services/templateService';
import RichTextEditor from './rich-text-editor';
import { FileText, FileText as LeaseIcon, PawPrint as PetIcon, Home as HoaIcon, ClipboardList as InventoryIcon } from 'lucide-react';


const templateFormSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }).max(100),
  description: z.string().min(1, { message: 'Description is required' }).max(500),
  type: z.enum(['LEASE', 'PET', 'HOA', 'INVENTORY']),
  isActive: z.boolean().default(true),  // Removed .optional() as default() already handles it
  content: z.any(), // Will be handled separately in the component
});

// Define our form values type based on the Zod schema
type TemplateFormValues = z.infer<typeof templateFormSchema>;
// Make sure this type is explicitly compatible with what we use in the component
interface SubmitFormValues {
  name: string;
  description: string;
  type: TemplateType;
  isActive: boolean;
  content?: any;
}

interface TemplateEditorProps {
  template?: Template;
  mode: 'create' | 'edit';
}

export default function TemplateEditor({ template, mode }: TemplateEditorProps) {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(false);
  const [editorContent, setEditorContent] = useState<TemplateContent>(
    template?.content || {
      version: '1.0',
      content: [
        {
          type: 'paragraph',
          text: '',
          variables: [],
        },
      ],
    }
  );
  
  const handleEditorChange = (content: TemplateContent) => {
    setEditorContent(content);
    form.setValue('content', content, { shouldDirty: true });
  };
  const [systemVariables, setSystemVariables] = useState<Record<string, SystemVariable>>({});

  // Use the explicit type for the form
  const form = useForm<SubmitFormValues>({
    resolver: zodResolver(templateFormSchema) as any, // Cast to any to avoid complex type issues
    defaultValues: {
      name: template?.name || '',
      description: template?.description || '',
      type: (template?.type as TemplateType) || 'LEASE',
      isActive: template?.isActive ?? true,
      content: editorContent,
    },
  });

  useEffect(() => {
    const fetchSystemVariables = async () => {
      try {
        const response = await templateService.getSystemVariables();
        if (response.success) {
          setSystemVariables(response.data);
        }
      } catch (error) {
        toast.error('Failed to load system variables', {
          position: 'top-right',
          duration: 5000,
        });
        console.error('Failed to load system variables:', error);
      }
    };

    fetchSystemVariables();
  }, []);

  // Make sure the onSubmit handler uses the proper type
  const onSubmit: SubmitHandler<SubmitFormValues> = async (values) => {
    try {
      setLoading(true);

      // Prepare the payload with editor content
      const payload: CreateTemplatePayload = {
        name: values.name,
        description: values.description,
        type: values.type,
        isActive: values.isActive,
        content: editorContent,
      };

      let response;
      if (mode === 'create') {
        response = await templateService.createTemplate(payload);
      } else if (mode === 'edit' && template?.id) {
        response = await templateService.updateTemplate(template.id, payload);
      }

      if (response?.success) {
        toast.success(mode === 'create' ? 'Template created successfully' : 'Template updated successfully', {
          position: 'top-right',
          duration: 5000,
        });
        
        // Redirect back to templates list
        // Get current locale from URL path
        const pathname = window.location.pathname;
        const localeMatch = pathname.match(/^\/([^/]+)\/.+/);
        const currentLocale = localeMatch ? localeMatch[1] : '';
        
        router.push(`/${currentLocale}/templates`);
      }
    } catch (error) {
      toast.error(mode === 'create' 
        ? 'Failed to create template' 
        : 'Failed to update template', {
          position: 'top-right',
          duration: 5000,
        });
      console.error('Template form error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleContentChange = (newContent: TemplateContent) => {
    setEditorContent(newContent);
  };

  const handleInsertVariable = (variable: string) => {
    // The rich text editor component will handle this insertion
    console.log('Inserting variable:', variable);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card className="overflow-hidden border border-border/50 shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-primary/10">
                <FileText className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle>Template Information</CardTitle>
                <CardDescription>
                  Basic details about your template
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <span>Template Name</span>
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="e.g., Standard Lease Agreement" 
                        className="h-10"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for your template
                    </FormDescription>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <span>Template Type</span>
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="Select template type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="LEASE" className="py-2">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 rounded-md bg-blue-100 dark:bg-blue-900/30">
                              <LeaseIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                            </div>
                            <span>Lease Agreement</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="PET" className="py-2">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 rounded-md bg-purple-100 dark:bg-purple-900/30">
                              <PetIcon className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                            </div>
                            <span>Pet Addendum</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="HOA" className="py-2">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 rounded-md bg-green-100 dark:bg-green-900/30">
                              <HoaIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                            </div>
                            <span>HOA Agreement</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="INVENTORY" className="py-2">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 rounded-md bg-amber-100 dark:bg-amber-900/30">
                              <InventoryIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                            </div>
                            <span>Inventory Checklist</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The type of document this template is for
                    </FormDescription>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="mt-4">
                    <FormLabel className="flex items-center gap-2">
                      <span>Description</span>
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a brief description of what this template is used for..."
                        className="min-h-[100px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A clear description helps identify the template's purpose
                    </FormDescription>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="mt-6 flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 bg-muted/20">
                    <div className="space-y-0.5 pr-4">
                      <FormLabel className="text-sm font-medium flex items-center gap-2">
                        <span>Template Status</span>
                      </FormLabel>
                      <FormDescription className="text-xs">
                        {field.value ? 
                          'This template is active and available for use' : 
                          'This template is inactive and will not be available for selection'}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <div className="flex items-center gap-3">
                        <span className={`text-sm font-medium ${field.value ? 'text-green-600' : 'text-muted-foreground'}`}>
                          {field.value ? 'Active' : 'Inactive'}
                        </span>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-green-600"
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Template Content</h3>
            </div>

            <RichTextEditor
              initialContent={editorContent}
              onChange={handleEditorChange}
              systemVariables={systemVariables}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.push(`/${params.locale}/templates`)}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Saving...' : mode === 'create' ? 'Create Template' : 'Update Template'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

import React, { useRef, ForwardedRef } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/app/context/AuthContext';
import { MaintenancePhotos } from './MaintenancePhotos';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent,  CardHeader, CardTitle } from '@/components/ui/card';
import { Property } from '@/types/property';
import { User } from '@/types/user';
import { MaintenanceTicket } from '@/types/maintenance';
import { MaintenanceStatusBadge } from './maintenance-status-filter';
import { MaintenancePriorityBadge } from './maintenance-priority-filter';
import { Wrench } from 'lucide-react';
import { PropertySelect } from '@/components/ui/property-select';
import { UserSelect } from '@/components/ui/user-select';
import { 
  maintenanceSchema, 
  maintenanceFormSchema,
  MaintenanceFormValues,
  MAINTENANCE_STATUS,
  MAINTENANCE_PRIORITY,
  MaintenanceStatus,
  MaintenancePriority
} from '../_schemas/maintenance.schema';

// Define the type for the MaintenancePhotos ref
export type MaintenancePhotosRefType = {
  uploadPendingFilesWithTicketId: (ticketId: string) => Promise<void>;
};

interface MaintenanceFormProps {
  ticket?: MaintenanceTicket;
  properties: Property[];
  users?: User[];
  onSuccess?: (ticketId: string) => void;
  locale: string;
  form: UseFormReturn<MaintenanceFormValues>;
  onSubmit: (values: MaintenanceFormValues) => Promise<void>;
  isSubmitting: boolean;
  onPropertyChange?: (value: string | undefined) => void;
  readOnly?: boolean;
  maintenancePhotosRef?: React.RefObject<MaintenancePhotosRefType | null>;
}

export function MaintenanceForm({ 
  ticket, 
  form, 
  onSubmit, 
  isSubmitting, 
  onPropertyChange, 
  readOnly = false, 
  maintenancePhotosRef 
}: MaintenanceFormProps) {
  const t = useTranslations();
  const { hasRole } = useAuth();
  const isEditing = !!ticket;
  const isAccountOwner = hasRole('account_owner');

  // Use the form passed as prop or create a new one
  const rhfForm = form || useForm<MaintenanceFormValues>({
    resolver: zodResolver(maintenanceFormSchema),
    defaultValues: {
      title: ticket?.title || '',
      description: ticket?.description || '',
      propertyId: ticket?.propertyId || '',
      priority: (ticket?.priority as MaintenancePriority) || MAINTENANCE_PRIORITY.MEDIUM,
      status: (ticket?.status as MaintenanceStatus) || MAINTENANCE_STATUS.OPEN,
      assigneeId: ticket?.assignee?.id || undefined,
    },
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
  });

  // Use the provided ref or create one if not provided
  const internalRef = useRef<MaintenancePhotosRefType | null>(null);
  
  // Track pending photos
  const [pendingPhotos, setPendingPhotos] = React.useState<File[]>([]);
  
  // Handle form submission with validation
  const handleSubmit = async (data: MaintenanceFormValues) => {
    try {
      // Validate against the submission schema
      const validatedData = await maintenanceSchema.parseAsync(data);
      await onSubmit(validatedData);
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...rhfForm}>
        <form 
          onSubmit={(e) => {
            e.preventDefault();
            rhfForm.handleSubmit(handleSubmit)(e);
          }} 
          className="space-y-6"
          noValidate
        >
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center space-x-2">
                <div className="p-2 rounded-lg bg-primary/10 text-primary shadow-sm">
                  <Wrench className="h-5 w-5" />
                </div>
                <CardTitle className="text-lg">
                  {isEditing ? t('editTicket') : t('createTicket')}
                </CardTitle>
              </div>
              {!readOnly && (
                <p className="text-sm text-muted-foreground mt-1">
                  {isEditing ? t('editDescription') : t('createDescription')}
                </p>
              )}
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Title */}
                <FormField
                  control={rhfForm.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('maintenanceTitle')}</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder={t('maintenanceTitlePlaceholder')}
                          disabled={isSubmitting || readOnly}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Property */}
                <FormField
                  control={rhfForm.control}
                  name="propertyId"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>{t('property')}</FormLabel>
                      <FormControl>
                        <PropertySelect
                          value={field.value}
                          onChange={(value) => {
                            if (onPropertyChange) {
                              onPropertyChange(value);
                            } else {
                              field.onChange(value);
                            }
                          }}
                          placeholder={t('selectProperty')}
                          disabled={isSubmitting || readOnly}
                          showAddress
                          showStatus
                          variant="compact"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Priority */}
                <FormField
                  control={rhfForm.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('priority')}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isSubmitting || readOnly}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectPriority')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {['low', 'medium', 'high', 'critical'].map((priority) => (
                            <SelectItem key={priority} value={priority}>
                              <MaintenancePriorityBadge priority={priority as MaintenancePriority} />
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status (only for editing) */}
                {isEditing && (
                  <FormField
                    control={rhfForm.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('status')}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={isSubmitting || readOnly}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('selectStatus')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {['open', 'in_progress', 'on_hold', 'completed', 'cancelled'].map((status) => (
                              <SelectItem key={status} value={status}>
                                <MaintenanceStatusBadge status={status as MaintenanceStatus} />
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Assignee - Only visible to account owners */}
                {isAccountOwner && (
                  <FormField
                    control={rhfForm.control}
                    name="assigneeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('assignee')}</FormLabel>
                        <FormControl>
                          <UserSelect
                            value={field.value}
                            onChange={(value) => field.onChange(value || null)}
                            placeholder={t('selectAssignee')}
                            disabled={isSubmitting || readOnly}
                            showEmail
                            showRole
                            variant="compact"
                            roles={['account_owner', 'property_manager', 'maintenance_staff']}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              {/* Description */}
              <FormField
                control={rhfForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('description')}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder={t('descriptionPlaceholder')}
                        disabled={isSubmitting || readOnly}
                        className="min-h-[120px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </form>
      </Form>
      
      {/* Photos upload component - allow for both new and existing tickets */}
      <MaintenancePhotos 
        ref={maintenancePhotosRef ? (maintenancePhotosRef as unknown as ForwardedRef<MaintenancePhotosRefType>) : internalRef}
        maintenanceId={ticket?.id}
        readOnly={readOnly}
        onPendingPhotosChange={setPendingPhotos}
      />
    </div>
  );
}

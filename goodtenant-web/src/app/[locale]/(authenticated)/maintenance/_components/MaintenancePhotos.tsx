'use client';

import React, { useState, useEffect, useCallback } from 'react';


import { useDropzone } from 'react-dropzone';
import { useTranslations } from 'next-intl';
import { Image as ImageIcon, Upload, Trash2, Loader2, X, AlertCircle, ZoomIn } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatFileSize } from '@/lib/fileUtils';

// Helper function to format date
const formatDate = (dateString?: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString();
};

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { maintenanceFiles } from '@/app/services/fileService';
import { FileMetadata } from '@/types/file';
import { maintenanceService } from '@/app/services/maintenanceService';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';


// Extend the FileMetadata type to include our additional fields
type FileMetadataExtended = FileMetadata & {
  previewUrl?: string;
  _file?: File;  // Reference to the original File object for pending files
};

interface MaintenancePhotosProps {
  maintenanceId?: string;
  className?: string;
  readOnly?: boolean;
  onPendingPhotosChange?: (files: File[]) => void;
}

export const MaintenancePhotos = React.forwardRef<{
  uploadPendingFilesWithTicketId: (ticketId: string) => Promise<void>
}, MaintenancePhotosProps>(({ maintenanceId, className = '', readOnly = false, onPendingPhotosChange }, ref) => {
  const t = useTranslations();
  const [files, setFiles] = useState<FileMetadataExtended[]>([]);
  const [pendingFiles, setPendingFiles] = useState<File[]>([]);
  const [isLoading, setIsLoading] = useState(maintenanceId ? true : false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  useEffect(() => {
    if (maintenanceId) {
      fetchFiles();
    }
  }, [maintenanceId]);
  
  // When pendingFiles change, notify parent component
  useEffect(() => {
    if (onPendingPhotosChange) {
      onPendingPhotosChange(pendingFiles);
    }
  }, [pendingFiles, onPendingPhotosChange]);

  const fetchFiles = async () => {
    if (!maintenanceId) return;
    
    try {
      setIsLoading(true);
      
      // Get files from the maintenance ticket response
      const response = await maintenanceService.getMaintenanceTicketById(maintenanceId);
      
      if (response.success && response.data.files && response.data.files.length > 0) {
        // Process image files
        const imageFiles: FileMetadataExtended[] = [];
        
        for (const file of response.data.files) {
          // Skip files without fileKey or that aren't images
          if (file.fileType?.startsWith('image/') && file.fileKey) {
            try {
              // Get signed URL for each file
              const signedUrl = await maintenanceFiles.getSignedUrl(file.fileKey);
              
              // Convert the maintenance file to FileMetadataExtended type with proper type safety
              const fileMetadata: FileMetadataExtended = {
                id: file.id || '',
                fileName: file.fileName || 'Unnamed File',
                fileKey: file.fileKey, // This is already checked in the if condition above
                fileType: file.fileType || 'image/jpeg',
                fileSize: file.fileSize || 0,
                description: file.description || '',
                entityType: 'Maintenance' as 'Maintenance', // Type assertion to match EntityType
                entityId: maintenanceId,
                propertyId: response.data.propertyId || '', // Required for backward compatibility
                uploadedBy: file.uploadedBy || '',
                createdAt: file.createdAt || new Date().toISOString(),
                updatedAt: file.updatedAt || new Date().toISOString(),
                url: signedUrl,
                previewUrl: signedUrl
              };
              
              imageFiles.push(fileMetadata);
            } catch (urlError) {
              console.error('Error getting signed URL for file:', file.fileKey, urlError);
            }
          }
        }
        
        setFiles(imageFiles);
      } else {
        setFiles([]);
      }
    } catch (error) {
      console.error('Error fetching maintenance images:', error);
      toast.error(t('fetchFailed'), {
        position: 'top-right',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateFile = (file: File) => {
    // Only allow image files
    if (!file.type.startsWith('image/')) {
      return {
        valid: false,
        error: t('invalidFileType')
      };
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      return {
        valid: false,
        error: t('errors.fileTooLarge', { maxSize: '10MB' })
      };
    }

    return { valid: true, error: null };
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      Array.from(e.target.files).forEach(file => {
        const validation = validateFile(file);
        if (validation.valid) {
          if (maintenanceId) {
            // If we have a maintenance ID, upload immediately
            handleFileUpload(file);
          } else {
            // Otherwise, add to pending files with preview
            addToPendingFiles(file);
          }
        } else if (validation.error) {
          setUploadError(validation.error);
          toast.error(validation.error, {
            position: 'top-right',
            duration: 5000,
          });
        }
      });
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploadError(null);
    acceptedFiles.forEach(file => {
      const validation = validateFile(file);
      if (validation.valid) {
        if (maintenanceId) {
          // If we have a maintenance ID, upload immediately
          handleFileUpload(file);
        } else {
          // Otherwise, add to pending files with preview
          addToPendingFiles(file);
        }
      } else if (validation.error) {
        setUploadError(validation.error);
        toast.error(validation.error, {
          position: 'top-right',
          duration: 5000,
        });
      }
    });
  }, [maintenanceId]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': []
    },
    disabled: isUploading || readOnly
  });

  // Add file to pending list with preview
  const addToPendingFiles = (file: File) => {
    // Create a preview for the file
    const previewUrl = URL.createObjectURL(file);
    
    // Add a temporary entry for display
    const tempFileId = `temp-${Date.now()}`;
    const tempFile: FileMetadataExtended = {
      id: tempFileId,
      fileName: file.name,
      fileKey: '',
      fileSize: file.size,
      fileType: file.type,
      description: '',
      uploadedBy: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      propertyId: '',
      url: '',
      previewUrl,
      // Store original file reference for later removal
      _file: file
    };
    
    // Add to visual files list
    setFiles(prev => [tempFile, ...prev]);
    
    // Add to pending files for actual upload later
    setPendingFiles(prev => [...prev, file]);
  };
  
  // Upload pending files when maintenance ID is available
  const uploadPendingFiles = async (ticketId: string) => {
    if (pendingFiles.length > 0) {
      // Upload each pending file
      for (const file of pendingFiles) {
        try {
          await handleFileUpload(file, ticketId);
        } catch (error) {
          console.error('Error uploading pending file:', error);
        }
      }
      // Clear pending files after upload attempts
      setPendingFiles([]);
    }
  };
  
  // Public method to upload pending files
  const uploadPendingFilesWithTicketId = async (ticketId: string) => {
    await uploadPendingFiles(ticketId);
  };
  
  // Make the method accessible via ref
  // eslint-disable-next-line react-hooks/rules-of-hooks
  React.useImperativeHandle(
    ref,
    () => ({ 
      uploadPendingFilesWithTicketId 
    }),
    [pendingFiles]
  );

  const handleFileUpload = async (file: File, targetTicketId?: string) => {
    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);
    
    try {
      const onUploadProgress = (progress: number) => {
        setUploadProgress(Math.round(progress * 100));
      };

      // Create a preview for the file being uploaded
      const previewUrl = URL.createObjectURL(file);
      
      // Add a temporary entry for the uploading file
      const tempFileId = `temp-${Date.now()}`;
      const tempFile: FileMetadataExtended = {
        id: tempFileId,
        fileName: file.name,
        fileKey: '',
        fileSize: file.size,
        fileType: file.type,
        description: '',
        uploadedBy: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        propertyId: '',
        url: '',
        previewUrl
      };
      
      setFiles(prev => [tempFile, ...prev]);
      
      // Use provided ticket ID or the component's maintenanceId
      const ticketId = targetTicketId || maintenanceId;
      if (!ticketId) {
        throw new Error('No maintenance ticket ID available for upload');
      }
      
      // Upload the file
      const response = await maintenanceFiles.uploadFile({
        entityId: ticketId,
        file,
        onUploadProgress
      });
      
      // Replace the temp file with the actual uploaded file data
      // Make sure to maintain all required fields with proper values
      setFiles(prev => prev.map(f => {
        if (f.id === tempFileId) {
          // Ensure all required fields have valid values to prevent NaN/undefined
          return {
            ...response,
            id: response.id || tempFileId,
            fileName: response.fileName || file.name,
            fileSize: response.fileSize || file.size,
            fileType: response.fileType || file.type,
            description: response.description || '',
            uploadedBy: response.uploadedBy || '',
            createdAt: response.createdAt || new Date().toISOString(),
            updatedAt: response.updatedAt || new Date().toISOString(),
            propertyId: response.propertyId || '',
            url: response.url || '',
            previewUrl: response.url || previewUrl // Use the URL from response or keep the preview
          };
        }
        return f;
      }));
      
      toast.success(t('fileUploaded'), {
        position: 'top-right',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      // Remove the temp file on error
      setFiles(prev => prev.filter(f => !f.id.startsWith('temp-')));
      setUploadError(t('uploadFailed'));
      toast.error(t('uploadFailed'), {
        position: 'top-right',
        duration: 5000,
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    // Check if this is a pending file (has temp- prefix)
    if (fileId.startsWith('temp-')) {
      // Get the file from our files array
      const fileToRemove = files.find(f => f.id === fileId);
      
      // Remove from pending files array if we have the reference
      if (fileToRemove && fileToRemove._file) {
        setPendingFiles(prev => prev.filter(file => file !== fileToRemove._file));
      }
      
      // Remove from visual files list
      setFiles(prev => prev.filter(file => file.id !== fileId));
      
      // Clean up the object URL to prevent memory leaks
      if (fileToRemove?.previewUrl) {
        URL.revokeObjectURL(fileToRemove.previewUrl);
      }
      
      toast.success(t('fileDeleted'));
      return;
    }
    
    // Handle server-uploaded files
    try {
      await maintenanceFiles.deleteFile(fileId);
      setFiles(prev => prev.filter(file => file.id !== fileId));
      toast.success(t('fileDeleted'));
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error(t('deleteFailed'), {
        position: 'top-right',
        duration: 5000,
      });
    }
  };

  const EmptyState = () => {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
          <ImageIcon className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="font-medium text-base mb-1">{t('noPhotos')}</h3>
        <p className="text-sm text-muted-foreground max-w-xs">
          {t('dragPhotosHere')}
        </p>
      </div>
    );
  };

  // Modal for image preview
  const ImagePreviewModal = () => {
    if (!previewImage) return null;

    return (
      <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4" onClick={() => setPreviewImage(null)}>
        <div className="relative max-w-4xl max-h-[90vh] w-full">
          <button 
            onClick={(e) => {
              e.stopPropagation();
              setPreviewImage(null);
            }}
            className="absolute top-2 right-2 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
          <img 
            src={previewImage} 
            alt="Preview" 
            className="max-h-[90vh] max-w-full object-contain mx-auto rounded-lg" 
          />
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      <Card className="overflow-hidden border-border">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="p-2.5 rounded-lg bg-primary/10 text-primary shadow-sm">
              <ImageIcon className="h-5 w-5" />
            </div>
            <CardTitle>{t('maintenanceTitle')}</CardTitle>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            {t('dragAndDrop')}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {!readOnly && (
            <div 
              {...getRootProps()} 
              className={cn(
                "border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer",
                isDragActive && !isDragReject ? "border-primary bg-primary/5" : "border-border",
                isDragReject ? "border-destructive bg-destructive/5" : "",
                isUploading || readOnly ? "opacity-50 cursor-not-allowed" : "hover:border-primary/50 hover:bg-primary/5"
              )}
            >
              <input {...getInputProps()} id="file-upload" />
              <div className="flex flex-col items-center justify-center py-4 px-2">
                <div className="rounded-full bg-primary/10 p-3 mb-3">
                  <Upload className="h-5 w-5 text-primary" />
                </div>
                <p className="text-sm font-medium mb-1">{t('dragPhotos')}</p>
                <p className="text-xs text-muted-foreground mb-2">
                  {t('supportedFormats')}
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={isUploading || readOnly}
                >
                  {t('browseFiles')}
                </Button>
              </div>
            </div>
          )}

          {uploadError && (
            <div className="bg-destructive/10 text-destructive rounded-md p-3 flex items-start space-x-2 text-sm">
              <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <p>{uploadError}</p>
            </div>
          )}

          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>{t('uploading')}</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          <div className="mt-4">
            <AnimatePresence initial={false}>
              {isLoading ? (
                <div className="py-8 flex justify-center">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : files.length === 0 ? (
                <EmptyState />
              ) : (
                <motion.div layout className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {files.map(file => (
                    <motion.div
                      key={file.id}
                      className="border rounded-md overflow-hidden"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      whileHover={{ y: -2 }}
                      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                    >
                      <div 
                        className="aspect-square bg-muted relative cursor-pointer group"
                        onClick={() => setPreviewImage(file.previewUrl || file.url || null)}
                      >
                        {file.previewUrl || file.url ? (
                          <>
                            <img 
                              src={file.previewUrl || file.url} 
                              alt={file.fileName} 
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                              <ZoomIn className="h-8 w-8 text-white" />
                            </div>
                          </>
                        ) : (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <ImageIcon className="h-10 w-10 text-muted-foreground/50" />
                          </div>
                        )}
                      </div>
                      
                      <div className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="truncate pr-2">
                            <p className="text-sm font-medium truncate">{file.fileName}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatFileSize(file.fileSize)} • {formatDate(file.createdAt)}
                            </p>
                          </div>
                          {!readOnly && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                file.id && handleDeleteFile(file.id);
                              }}
                              className="p-1.5 rounded-full hover:bg-destructive/10 text-destructive hover:bg-destructive/20 transition-colors flex-shrink-0"
                              title={t('delete')}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>
      
      {/* Image preview modal */}
      <ImagePreviewModal />
    </div>
  );
});

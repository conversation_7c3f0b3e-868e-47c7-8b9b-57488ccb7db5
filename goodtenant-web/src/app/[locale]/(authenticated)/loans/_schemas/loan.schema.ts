import { z } from 'zod';
import { Loan, LoanStatus } from '@/types/loan';

// Phone regex that allows most common phone number formats
const phoneRegex = /^[\+\d\s\-\(\)\.]{5,25}$/;

// Base schema with optional fields for the form
export const loanFormSchema = z.object({
  id: z.string().optional(),
  propertyId: z.string().optional(),
  // Basic loan details
  loanName: z.string().optional(),
  loanType: z.string().optional(),
  loanTerm: z.coerce.number().min(0).optional(),
  loanNumber: z.string().optional(),
  loanAmount: z.coerce.number({
    invalid_type_error: 'Loan amount must be a number',
  }).min(0, 'Loan amount cannot be negative').optional(),
  interestRate: z.coerce.number({
    invalid_type_error: 'Interest rate must be a number',
  }).min(0, 'Interest rate cannot be negative').max(100, 'Interest rate cannot exceed 100%').optional(),
  
  // Lender information
  lenderName: z.string().optional(),
  lenderContactName: z.string().optional(),
  lenderEmail: z.string().email('Invalid email address').or(z.literal('')).optional(),
  lenderPhone: z.string()
    .regex(phoneRegex, { message: 'Please enter a valid phone number' })
    .or(z.literal(''))
    .optional(),
  lenderWebsite: z.string().url('Invalid URL').or(z.literal('')).optional(),
  lenderCountry: z.string().default('USA'),
  
  // Legacy lender address fields
  lenderAddressLine1: z.string().optional(),
  lenderAddressLine2: z.string().optional(),
  lenderCity: z.string().optional(),
  lenderState: z.string().optional(),
  lenderPostalCode: z.string().optional(),
  
  // Payment details
  paymentDay: z.coerce.number().min(1, 'Day must be at least 1').max(31, 'Day cannot exceed 31').optional(),
  monthlyPayment: z.coerce.number().min(0).optional(),
  
  // Legacy fields
  customerServicePhone: z.string()
    .regex(phoneRegex, { message: 'Please enter a valid phone number' })
    .or(z.literal(''))
    .optional()
    .transform(val => val?.trim() || ''),
  principal: z.coerce.number({
    invalid_type_error: 'Principal must be a number',
  }).min(0, 'Principal cannot be negative').optional(),
  escrow: z.coerce.number({
    invalid_type_error: 'Escrow must be a number',
  }).min(0, 'Escrow cannot be negative').optional(),
  
  // Notes/comments
  notes: z.string().optional(),
  comments: z.string().optional(),
});

// Validation schema for submission
// Create a refined schema for submission with required fields
export const LoanFormSchema = loanFormSchema.extend({
  lenderCountry: z.string().min(1, 'Country is required'),
  customerServicePhone: z.string().min(1, 'Customer service phone is required')
})
  .refine((data) => data.propertyId && data.propertyId.length > 0, {
    message: 'Property is required',
    path: ['propertyId']
  })
  .refine((data) => data.lenderName && data.lenderName.length > 0, {
    message: 'Lender name is required',
    path: ['lenderName']
  })
  .refine((data) => data.loanNumber && data.loanNumber.length > 0, {
    message: 'Loan number is required',
    path: ['loanNumber']
  });

export type LoanFormValues = z.infer<typeof loanFormSchema>;
export type LoanSubmitValues = z.infer<typeof LoanFormSchema>;

export function transformLoanToFormValues(loan?: Partial<Loan> | null): LoanFormValues {
  if (!loan) {
    return {
      id: '',
      propertyId: '',
      // Basic loan details
      loanName: '',
      loanType: '',
      loanTerm: 0,
      loanNumber: '',
      loanAmount: 0,
      interestRate: 0,
      
      // Lender information
      lenderName: '',
      lenderContactName: '',
      lenderEmail: '',
      lenderPhone: '',
      lenderWebsite: '',
      lenderCountry: 'USA',
      
      // Legacy lender address fields
      lenderAddressLine1: '',
      lenderAddressLine2: '',
      lenderCity: '',
      lenderState: '',
      lenderPostalCode: '',
      
      // Payment details
      paymentDay: 1,
      monthlyPayment: 0,
      
      // Legacy fields
      customerServicePhone: '',
      principal: 0,
      escrow: 0,
      
      // Notes/comments
      notes: '',
      comments: '',
    };
  }

  return {
    id: loan.id || '',
    propertyId: loan.propertyId || '',
    
    // Basic loan details
    loanName: loan.loanName || '',
    loanType: loan.loanType || '',
    loanTerm: typeof loan.loanTerm === 'string' ? parseInt(loan.loanTerm) || 0 : loan.loanTerm || 0,
    loanNumber: loan.loanNumber || '',
    loanAmount: typeof loan.loanAmount === 'string' ? parseFloat(loan.loanAmount) || 0 : loan.loanAmount || 0,
    interestRate: typeof loan.interestRate === 'string' ? parseFloat(loan.interestRate) || 0 : loan.interestRate || 0,
    
    // Lender information
    lenderName: loan.lenderName || '',
    lenderContactName: loan.lenderContactName || '',
    lenderEmail: loan.lenderEmail || '',
    lenderPhone: loan.lenderPhone || '',
    lenderWebsite: loan.lenderWebsite || '',
    lenderCountry: loan.lenderCountry || 'USA',
    
    // Legacy lender address fields
    lenderAddressLine1: loan.lenderAddressLine1 || '',
    lenderAddressLine2: loan.lenderAddressLine2 || '',
    lenderCity: loan.lenderCity || '',
    lenderState: loan.lenderState || '',
    lenderPostalCode: loan.lenderPostalCode || '',
    
    // Payment details
    paymentDay: typeof loan.paymentDay === 'string' ? parseInt(loan.paymentDay) || 1 : loan.paymentDay || 1,
    monthlyPayment: typeof loan.monthlyPayment === 'string' ? parseFloat(loan.monthlyPayment) || 0 : loan.monthlyPayment || 0,
    
    // Legacy fields
    customerServicePhone: loan.customerServicePhone || '',
    principal: typeof loan.principal === 'string' ? parseFloat(loan.principal) || 0 : loan.principal || 0,
    escrow: typeof loan.escrow === 'string' ? parseFloat(loan.escrow) || 0 : loan.escrow || 0,
    
    // Notes/comments
    notes: loan.notes || '',
    comments: loan.comments || '',
  };
}

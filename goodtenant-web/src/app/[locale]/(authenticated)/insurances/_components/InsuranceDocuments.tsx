'use client';

import { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useTranslations } from 'next-intl';
import { Upload, Trash2, Download, FileText, Image as ImageIcon, Loader2, Plus, X, AlertCircle, FileArchive, FileSpreadsheet, FileCode, FileAudio, FileVideo } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatFileSize } from '@/lib/fileUtils';

// Helper function to format date
const formatDate = (dateString?: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString();
};
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { insuranceFiles } from '@/app/services/fileService';
import { FileMetadata } from '@/types/file';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

const getFileIcon = (fileType?: string) => {
  if (!fileType) return <FileText className="h-4 w-4" />;
  
  if (fileType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />;
  if (fileType.includes('pdf')) return <FileText className="h-4 w-4 text-red-500" />;
  if (fileType.includes('word') || fileType.includes('document')) return <FileText className="h-4 w-4 text-blue-500" />;
  if (fileType.includes('spreadsheet') || fileType.includes('excel')) return <FileSpreadsheet className="h-4 w-4 text-green-600" />;
  if (fileType.includes('zip') || fileType.includes('compressed')) return <FileArchive className="h-4 w-4 text-amber-500" />;
  if (fileType.includes('audio')) return <FileAudio className="h-4 w-4 text-purple-500" />;
  if (fileType.includes('video')) return <FileVideo className="h-4 w-4 text-pink-500" />;
  if (fileType.includes('code') || fileType.includes('text')) return <FileCode className="h-4 w-4 text-gray-500" />;
  
  return <FileText className="h-4 w-4 text-gray-400" />;
};

const getFileTypeName = (fileType?: string) => {
  if (!fileType) return 'Document';
  
  if (fileType.startsWith('image/')) return 'Image';
  if (fileType.includes('pdf')) return 'PDF';
  if (fileType.includes('word') || fileType.includes('document')) return 'Word';
  if (fileType.includes('spreadsheet') || fileType.includes('excel')) return 'Spreadsheet';
  if (fileType.includes('zip') || fileType.includes('compressed')) return 'Archive';
  if (fileType.includes('audio')) return 'Audio';
  if (fileType.includes('video')) return 'Video';
  if (fileType.includes('code') || fileType.includes('text')) return 'Code';
  
  return fileType.split('/')[1]?.toUpperCase() || 'File';
};

// Define the uploadedBy type
interface UploadedByUser {
  id: string;
  name: string;
  email: string;
}

// Extend the FileMetadata type to include our additional fields
type FileMetadataExtended = Omit<FileMetadata, 'fileType' | 'uploadedBy'> & {
  name: string;
  size: number;
  uploadedAt: string;
  fileType: string;
  url: string;
  uploadedBy?: UploadedByUser | string;
};

interface InsuranceDocumentsProps {
  insuranceId: string;
  className?: string;
  readOnly?: boolean;
}

export function InsuranceDocuments({ insuranceId, className = '', readOnly = false }: InsuranceDocumentsProps) {
  const t = useTranslations();
 
  const [files, setFiles] = useState<FileMetadataExtended[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  useEffect(() => {
    if (insuranceId) {
      fetchFiles();
    }
  }, [insuranceId]);

  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      const response = await insuranceFiles.getFiles(insuranceId);
      // Map the response to ensure all required fields are present
      const filesWithMetadata: FileMetadataExtended[] = response.map(file => ({
        ...file,
        name: file.fileName || 'Untitled',
        size: file.fileSize || 0,
        fileType: file.fileType || 'application/octet-stream',
        uploadedAt: (file as any).uploadedAt || new Date().toISOString(),
        url: file.url || '#',
        uploadedBy: (file as any).uploadedBy // Keep as is, handle in the render
      }));
      setFiles(filesWithMetadata);
    } catch (error) {
      console.error('Error fetching files:', error);
      toast.error(t('errors.fetchFailed') || 'Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const validateFile = (file: File) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      const errorMsg = t('invalidFileType') || 'Invalid file type';
      toast.error(errorMsg);
      return { valid: false, error: errorMsg };
    }
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      const errorMsg = t('fileTooLarge') || 'File is too large. Maximum size is 10MB';
      toast.error(errorMsg);
      return { valid: false, error: errorMsg };
    }
    
    return { valid: true };
  };

  

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!acceptedFiles.length || isUploading) return;
    const file = acceptedFiles[0];
    await handleFileUpload(file);
  }, [isUploading]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt'],
      'image/*': ['.jpg', '.jpeg', '.png', '.gif']
    },
    maxFiles: 1,
    disabled: isUploading
  });

  const handleFileUpload = async (file: File) => {
    if (!insuranceId) return;

    try {
      setIsUploading(true);
      setUploadProgress(0);
      setUploadError(null);

      const response = await insuranceFiles.uploadFile({
        file,
        entityId: insuranceId,
        description: file.name,
        onUploadProgress: (progress: number) => {
          setUploadProgress(progress);
        }
      }) as FileMetadataExtended;

      // Ensure the response has the required fields
      const fileWithMetadata = {
        ...response,
        name: file.name,
        size: file.size,
        fileType: file.type,
        uploadedAt: new Date().toISOString(),
        url: response.url || '#'
      };

      setFiles(prev => [...prev, fileWithMetadata]);
      toast.success(t('uploadSuccess') || 'File uploaded successfully');
    } catch (error) {
      console.error('Error uploading file:', error);
      setUploadError(
        error instanceof Error 
          ? error.message 
          : t('uploadFailed') || 'Failed to upload file'
      );
      toast.error(t('uploadFailed') || 'Failed to upload file');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      await insuranceFiles.deleteFile(fileId);
      setFiles(prev => prev.filter(file => file.id !== fileId));
      toast.success(t('deleteSuccess') || 'File deleted successfully');
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error(t('deleteFailed') || 'Failed to delete file');
    }
  };



  const EmptyState = () => (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center py-12 px-4 text-center"
    >
      <div className="bg-muted/40 p-6 rounded-full mb-4">
        <FileText className="h-10 w-10 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium mb-2">
        {t('noDocuments') || 'No documents yet'}
      </h3>
      <p className="text-sm text-muted-foreground max-w-md mb-4">
        {t('uploadDescription') || 'Upload your first document by dragging and dropping files here or click the button below.'}
      </p>
      <Button 
        variant="outline" 
        onClick={() => document.getElementById('file-upload')?.click()}
        className="mt-2"
      >
        <Upload className="h-4 w-4 mr-2" />
        {t('upload') || 'Upload Insurance Document'}
      </Button>
    </motion.div>
  );

  return (
    <div className={cn('space-y-4', className)}>
      <Card className="overflow-hidden">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="p-2.5 rounded-lg bg-primary/10 text-primary shadow-sm">
              <FileText className="h-5 w-5" />
            </div>
            <CardTitle>{t('insuranceDocumentTitle') || 'Insurance Documents'}</CardTitle>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            {t('insuranceDocumentDescription') || 'Upload and manage insurance-related documents'}
          </p>
        </CardHeader>
        <CardContent>
          {!readOnly && (
            <div 
              {...getRootProps()} 
              className={cn(
                'group relative border-2 border-dashed rounded-lg p-6 text-center transition-all',
                'cursor-pointer hover:border-primary/50 hover:bg-primary/5',
                isDragActive && 'border-primary bg-primary/5 ring-2 ring-primary/20',
                isUploading && 'opacity-70 pointer-events-none',
                'overflow-hidden border-dashed'
              )}
            >
              <input id="file-upload" {...getInputProps()} />
              <div className="relative z-10 flex flex-col items-center justify-center space-y-3">
                {isUploading ? (
                  <div className="w-full space-y-4">
                    <div className="flex items-center justify-center space-x-2">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      <span className="font-medium">
                        {t('uploading') || 'Uploading...'} {uploadProgress}%
                      </span>
                    </div>
                    <Progress value={uploadProgress} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      {t('uploadingDetails') || 'Your file is being uploaded. Please wait...'}
                    </p>
                  </div>
                ) : (
                  <>
                    <div className="p-3 rounded-full bg-primary/10 text-primary transition-colors group-hover:bg-primary/20">
                      <Upload className="h-6 w-6" />
                    </div>
                    <div className="space-y-1 text-center">
                      <p className="text-sm font-medium">
                        {isDragActive 
                          ? t('dropHere') || 'Drop the file here' 
                          : t('dragAndDrop') || 'Drag & drop files here, or click to select'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {t('fileTypes') || 'PDF, DOCX, XLSX, JPG, PNG (max 10MB)'}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {uploadError && (
            <div className="mt-4 text-sm text-destructive flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              {uploadError}
            </div>
          )}

          <div className="mt-6 space-y-4">
            <h3 className="text-sm font-medium">{t('uploadedFiles') || 'Uploaded Files'}</h3>
            <AnimatePresence>
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : files.length === 0 ? (
                <EmptyState />
              ) : (
                <motion.div 
                  className="grid gap-3 mt-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {files.map((file) => (
                    <motion.div
                      key={file.id}
                      className={cn(
                        "flex items-center justify-between p-3 border rounded-lg transition-colors",
                        !readOnly && "hover:bg-muted/50"
                      )}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      whileHover={{ x: 2 }}
                      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={cn(
                          'p-2 rounded-lg',
                          file.fileType?.startsWith('image/') ? 'bg-blue-50 dark:bg-blue-900/30' :
                          file.fileType?.includes('pdf') ? 'bg-red-50 dark:bg-red-900/30' :
                          'bg-muted'
                        )}>
                          {getFileIcon(file.fileType)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium truncate max-w-[200px] sm:max-w-xs">
                              {file.name}
                            </p>
                            <Badge variant="outline" className="text-xs font-normal">
                              {getFileTypeName(file.fileType)}
                            </Badge>
                          </div>
                          <div className="flex flex-wrap items-center gap-x-4 gap-y-1 mt-1 text-xs text-muted-foreground">
                            <span>{formatFileSize(file.size)}</span>
                            <span>• {formatDate(file.uploadedAt)}</span>
                            {file.uploadedBy && (
                              <div className="flex items-center">
                                <span className="mr-1">•</span>
                                <Avatar className="h-4 w-4 mr-1">
                                  <AvatarFallback className="text-[10px] h-4 w-4">
                                    {typeof file.uploadedBy === 'string' 
                                      ? file.uploadedBy.charAt(0).toUpperCase() 
                                      : file.uploadedBy?.name?.charAt(0).toUpperCase() || 'U'}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="truncate max-w-[100px]">
                                  {typeof file.uploadedBy === 'string' 
                                    ? file.uploadedBy 
                                    : file.uploadedBy?.name || 'User'}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center mt-3 sm:mt-0 sm:pl-2 space-x-1">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => file.url && window.open(file.url, '_blank')}
                            className="p-1.5 rounded-full hover:bg-muted text-muted-foreground hover:text-foreground transition-colors"
                            title="Download"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                          {!readOnly && (
                            <button
                              onClick={() => file.id && handleDeleteFile(file.id)}
                              className="p-1.5 rounded-full hover:bg-destructive/10 text-destructive hover:bg-destructive/20 transition-colors"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { z } from 'zod';
import { 
  PROPERTY_TYPES, 
  PROPERTY_STATUS, 
  PRICE_INTERVALS,
  PropertyType,
  PropertyStatus,
  PriceInterval 
} from '@/types/property';

export const propertyTypes = Object.entries(PROPERTY_TYPES).map(([key, value]) => ({
  value: key as PropertyType,
  label: value
}));

export const propertyStatuses = Object.entries(PROPERTY_STATUS).map(([key, value]) => ({
  value: key as PropertyStatus,
  label: value
}));

export const priceIntervals = Object.entries(PRICE_INTERVALS).map(([key, value]) => ({
  value: key as PriceInterval,
  label: value
}));

// Base schema with all fields as optional for the form
export const propertyFormSchema = z.object({
  hoaId: z.string().optional(),
  name: z.string().optional(),
  description: z.string().optional(),
  propertyType: z.nativeEnum(PROPERTY_TYPES).optional(),
  status: z.nativeEnum(PROPERTY_STATUS).optional(),
  addressLine1: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  priceAmount: z.coerce.number().optional(),
  priceCurrency: z.string().default('USD'),
  priceInterval: z.nativeEnum(PRICE_INTERVALS).default('month'),
  sizeSquareFeet: z.coerce.number().optional(),
  yearBuilt: z.coerce.number().default(new Date().getFullYear()),
  bedrooms: z.coerce.number().optional(),
  bathrooms: z.coerce.number().optional(),
  depositAmount: z.coerce.number().optional(),
  addressLine2: z.string().optional(),
  latitude: z.coerce.number().default(0),
  longitude: z.coerce.number().default(0),
  images: z.array(z.instanceof(File)).optional()
});

// Validation schema for submission
export const PropertyFormSchema = propertyFormSchema
  .refine((data) => data.name && data.name.length > 0, {
    message: 'Name is required',
    path: ['name']
  })
  .refine((data) => data.propertyType, {
    message: 'Property type is required',
    path: ['propertyType']
  })
  .refine((data) => data.status, {
    message: 'Status is required',
    path: ['status']
  })
  .refine((data) => data.addressLine1 && data.addressLine1.length > 0, {
    message: 'Address is required',
    path: ['addressLine1']
  })
  .refine((data) => data.city && data.city.length > 0, {
    message: 'City is required',
    path: ['city']
  })
  .refine((data) => data.state && data.state.length > 0, {
    message: 'State is required',
    path: ['state']
  })
  .refine((data) => data.postalCode && data.postalCode.length > 0, {
    message: 'Postal code is required',
    path: ['postalCode']
  })
  .refine((data) => data.country && data.country.length > 0, {
    message: 'Country is required',
    path: ['country']
  })
  .refine((data) => data.priceAmount && data.priceAmount > 0, {
    message: 'Price must be greater than 0',
    path: ['priceAmount']
  })
  .refine((data) => data.sizeSquareFeet && data.sizeSquareFeet > 0, {
    message: 'Size must be greater than 0',
    path: ['sizeSquareFeet']
  })
  .refine((data) => data.yearBuilt && data.yearBuilt >= 1800 && data.yearBuilt <= new Date().getFullYear() + 1, {
    message: 'Year must be between 1800 and next year',
    path: ['yearBuilt']
  });

export type PropertyFormValues = z.infer<typeof propertyFormSchema>;
export type PropertyFormSubmission = z.infer<typeof PropertyFormSchema>;

export function transformPropertyToFormValues(property?: any): PropertyFormValues {
  if (!property) {
    return {
      name: '',
      description: '',
      propertyType: 'apartment',
      status: 'vacant',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'USA',
      priceAmount: 0,
      priceCurrency: 'USD',
      priceInterval: 'month',
      sizeSquareFeet: 0,
      yearBuilt: new Date().getFullYear(),
      bedrooms: 0,
      bathrooms: 0,
      depositAmount: 0,
      latitude: 0,
      longitude: 0,
      images: []
    };
  }

  return {
    ...property,
    images: [] // Will be handled separately
  };
}

'use client';

import { useEffect, useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { 
  Download, FileText, Globe,  Phone, DollarSign, Percent, Home, 
  Calendar,  Building2, FileDigit, File, Upload, Trash2, 
  Image as ImageIcon, Loader2, AlertCircle 
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Loan } from '@/types/loan';
import { loanService } from '@/app/services/loanService';
import { loanFiles } from '@/app/services/fileService';
import { FileMetadata } from '@/types/file';
import { toast } from 'sonner';

// Extend FileMetadata with additional properties
interface FileMetadataWithName extends Omit<FileMetadata, 'fileType'> {
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadedAt: string;
}

export function LoanInfo({ loanId }: { loanId: string }) {
  const t = useTranslations('loans');
  const [loan, setLoan] = useState<Loan | null>(null);
  const [files, setFiles] = useState<FileMetadataWithName[]>([]);
  const [loading, setLoading] = useState(true);
  const [filesLoading, setFilesLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Format file size helper function
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: useCallback(async (acceptedFiles: File[]) => {
      if (!acceptedFiles.length || isUploading) return;
      const file = acceptedFiles[0];
      await handleFileUpload(file);
    }, [isUploading]),
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt'],
      'image/*': ['.jpg', '.jpeg', '.png', '.gif']
    },
    maxFiles: 1,
    disabled: isUploading
  });

  useEffect(() => {
    const fetchLoan = async () => {
      try {
        setLoading(true);
        const response = await loanService.getLoanById(loanId);
        if (response.success) {
          setLoan(response.data);
        }
      } catch (error) {
        console.error('Error fetching loan details:', error);
        toast.error(t('loans.errors.fetchFailed') || 'Failed to load loan information', {
          position: 'top-right',
          duration: 5000,
        });
      } finally {
        setLoading(false);
      }
    };

    const fetchLoanFiles = async () => {
      if (!loanId) return;
      
      try {
        setFilesLoading(true);
        const files = await loanFiles.getFiles(loanId);
        // Map the files to ensure they have all required properties
        const mappedFiles = files.map(file => ({
          ...file,
          fileName: file.fileName || 'Unknown',
          fileType: file.fileType || 'application/octet-stream',
          fileSize: file.fileSize || 0,
          uploadedAt: new Date().toISOString()
        }));
        setFiles(mappedFiles);
      } catch (error) {
        console.error('Error fetching loan files:', error);
        toast.error(t('documents.errors.fetchFailed') || 'Failed to load loan files', {
          position: 'top-right',
          duration: 5000,
        });
      } finally {
        setFilesLoading(false);
      }
    };

    if (loanId) {
      fetchLoan();
      fetchLoanFiles();
    }
  }, [loanId, t]);

  const validateFile = (file: File) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      const errorMsg = t('documents.errors.invalidFileType') || 'Invalid file type';
      toast.error(errorMsg, {
        position: 'top-right',
        duration: 5000,
      });
      return { valid: false, error: errorMsg };
    }
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      const errorMsg = t('documents.errors.fileTooLarge') || 'File is too large. Maximum size is 10MB';
      toast.error(errorMsg, {
        position: 'top-right',
        duration: 5000,
      });
      return { valid: false, error: errorMsg };
    }
    
    return { valid: true };
  };

  const handleFileUpload = async (file: File) => {
    if (!loanId) return;

    try {
      setIsUploading(true);
      setUploadProgress(0);
      setUploadError(null);

      const uploadedFile = await loanFiles.uploadFile({
        file,
        entityId: loanId,
        description: file.name,
        onUploadProgress: (progress: number) => {
          setUploadProgress(progress);
        },
      });

      // Ensure the file has all required properties
      const fileWithName: FileMetadataWithName = {
        ...uploadedFile,
        fileName: uploadedFile.fileName || file.name,
        fileType: file.type,
        fileSize: file.size,
        uploadedAt: new Date().toISOString()
      };

      setFiles(prev => [...prev, fileWithName]);
      toast.success(t('documents.uploadSuccess') || 'File uploaded successfully', {
        position: 'top-right',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to upload file';
      setUploadError(errorMsg);
      toast.error(errorMsg, {
        position: 'top-right',
        duration: 5000,
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    if (!confirm(t('documents.confirmDelete') || 'Are you sure you want to delete this file?')) {
      return;
    }

    try {
      await loanFiles.deleteFile(fileId);
      setFiles(prev => prev.filter(file => file.id !== fileId));
      toast.success(t('documents.deleteSuccess') || 'File deleted successfully', {
        position: 'top-right',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error(t('documents.errors.deleteFailed') || 'Failed to delete file', {
        position: 'top-right',
        duration: 5000,
      });
    }
  };

  const handleDownloadFile = async (file: FileMetadataWithName) => {
    try {
      if (!file.url && file.fileKey) {
        // If file doesn't have a URL, try to get a signed URL
        const signedUrl = await loanFiles.getSignedUrl(file.fileKey);
        window.open(signedUrl, '_blank');
      } else if (file.url) {
        window.open(file.url, '_blank');
      } else {
        throw new Error('No valid file URL or key available');
      }
      toast.info(`${t('documents.downloading') || 'Downloading'} ${file.fileName}`, {
        position: 'top-right',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error(t('documents.errors.downloadFailed') || 'Failed to download file', {
        position: 'top-right',
        duration: 5000,
      });
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />;
    if (fileType === 'application/pdf') return <FileText className="h-4 w-4 text-red-500" />;
    if (fileType.includes('word')) return <FileText className="h-4 w-4 text-blue-500" />;
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return <FileText className="h-4 w-4 text-green-600" />;
    return <File className="h-4 w-4" />;
  };

  const formatCurrency = (amount: number | string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Number(amount));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!loan) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {t('noLoanInfo') || 'No loan information available'}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Loan Information */}
      <Card>
        <CardHeader>
          <CardTitle>Loan Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Loan Details */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                Loan Details
              </h3>
              <div className="space-y-2 text-sm pl-6">
                <p className="flex items-center gap-2">
                  <span className="font-medium w-32">Loan Amount:</span>
                  <span>{formatCurrency(loan.loanAmount)}</span>
                </p>
                <p className="flex items-center gap-2">
                  <span className="font-medium w-32">Interest Rate:</span>
                  <span className="flex items-center gap-1">
                    <Percent className="h-3 w-3" />
                    {Number(loan.interestRate).toFixed(2)}%
                  </span>
                </p>
                <p className="flex items-center gap-2">
                  <span className="font-medium w-32">Principal:</span>
                  <span>{formatCurrency(loan.principal)}</span>
                </p>
                <p className="flex items-center gap-2">
                  <span className="font-medium w-32">Escrow:</span>
                  <span>{formatCurrency(loan.escrow)}</span>
                </p>
                <p className="flex items-center gap-2">
                  <span className="font-medium w-32">Loan Number:</span>
                  <span className="flex items-center gap-1">
                    <FileDigit className="h-4 w-4 text-muted-foreground" />
                    {loan.loanNumber}
                  </span>
                </p>
                {loan.createdAt && (
                  <p className="flex items-center gap-2">
                    <span className="font-medium w-32">Date Created:</span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {formatDate(loan.createdAt)}
                    </span>
                  </p>
                )}
              </div>
            </div>

            {/* Lender Information */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                Lender Information
              </h3>
              <div className="space-y-2 text-sm pl-6">
                <p className="font-medium">{loan.lenderName}</p>
                <p>{loan.lenderAddressLine1}</p>
                {loan.lenderAddressLine2 && <p>{loan.lenderAddressLine2}</p>}
                <p>
                  {loan.lenderCity}, {loan.lenderState} {loan.lenderPostalCode}
                </p>
                <p>{loan.lenderCountry}</p>
                
                {loan.customerServicePhone && (
                  <p className="flex items-center gap-2 mt-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    {loan.customerServicePhone}
                  </p>
                )}
                
                {loan.lenderWebsite && (
                  <p className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <a 
                      href={loan.lenderWebsite.startsWith('http') ? loan.lenderWebsite : `https://${loan.lenderWebsite}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline text-primary"
                    >
                      {loan.lenderWebsite.replace(/^https?:\/\//, '')}
                    </a>
                  </p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Section */}
      <Card>
        <CardHeader>
          <CardTitle>{t('documents.title') || 'Loan Documents'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Upload Area */}
            <div 
              {...getRootProps()} 
              className={cn(
                'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
                isDragActive ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50',
                isUploading && 'opacity-50 cursor-not-allowed'
              )}
            >
              <input {...getInputProps()} />
              <div className="flex flex-col items-center justify-center space-y-2">
                {isUploading ? (
                  <>
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="text-sm text-muted-foreground">
                      {t('documents.uploading') || 'Uploading...'} {uploadProgress > 0 && `(${Math.round(uploadProgress)}%)`}
                    </p>
                  </>
                ) : (
                  <>
                    <Upload className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">
                        {isDragActive 
                          ? t('documents.dropHere') || 'Drop the file here' 
                          : t('documents.dragAndDrop') || 'Drag and drop files here, or click to select'}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {t('documents.fileTypes') || 'PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (max 10MB)'}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {uploadError && (
              <div className="flex items-center gap-2 text-destructive text-sm">
                <AlertCircle className="h-4 w-4" />
                {uploadError}
              </div>
            )}

            {/* Files List */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium">{t('documents.uploadedFiles') || 'Uploaded Files'}</h3>
              {filesLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-5 w-5 animate-spin text-primary" />
                </div>
              ) : files.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  {t('documents.noFiles') || 'No documents uploaded yet'}
                </p>
              ) : (
                <div className="divide-y divide-border rounded-md border">
                  {files.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-3 hover:bg-muted/50">
                      <div className="flex items-center gap-3 min-w-0">
                        {getFileIcon(file.fileType)}
                        <div className="min-w-0">
                          <p className="text-sm font-medium truncate">{file.fileName}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatFileSize(file.fileSize)} • {new Date(file.uploadedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownloadFile(file);
                          }}
                        >
                          <Download className="h-4 w-4" />
                          <span className="sr-only">{t('common.download') || 'Download'}</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-destructive hover:text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteFile(file.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">{t('common.delete') || 'Delete'}</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useTranslations } from 'next-intl';
import { File, Trash2, Download, FileText, Image as ImageIcon, Loader2, Plus, X } from 'lucide-react'; 
import { cn } from '@/lib/utils';
import { formatFileSize } from '@/lib/fileUtils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { propertyFiles } from '@/app/services/fileService';
import { FileMetadata } from '@/types/file';

interface PropertyDocumentsProps {
  propertyId: string;
  className?: string;
}

export function PropertyDocuments({ propertyId, className = '' }: PropertyDocumentsProps) {
  const t = useTranslations();
  const [files, setFiles] = useState<FileMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Simple toast implementation
  const showToast = useCallback(({ title, variant = 'default' }: { title: string; variant?: 'default' | 'destructive' }) => {
    // In a real app, you would use a toast notification library
    console.log(`[${variant}] ${title}`);
  }, []);

  useEffect(() => {
    fetchFiles();
  }, [propertyId]);

  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      const response = await propertyFiles.getFiles(propertyId);
      // Filter out image files and files with 'showcase' in the description
      const documentFiles = response.filter(file => {
        const isImage = file.fileType?.startsWith('image/');
        const isShowcase = file.description?.toLowerCase().includes('showcase');
        return !isImage && !isShowcase;
      });
      setFiles(documentFiles);
    } catch (error) {
      console.error('Error fetching files:', error);
      showToast({
        title: t('errors.fetchFailed') || 'Failed to load documents',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateFile = (file: File) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      const errorMsg = t('errors.invalidFileType') || 'Invalid file type';
      showToast({
        title: errorMsg,
        variant: 'destructive'
      });
      return { valid: false, error: errorMsg };
    }
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      const errorMsg = t('fileTooLarge') || 'File is too large. Maximum size is 10MB';
      showToast({
        title: errorMsg,
        variant: 'destructive'
      });
      return { valid: false, error: errorMsg };
    }
    
    return { valid: true };
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const { valid } = validateFile(file);
    if (!valid) {
      e.target.value = '';
      return;
    }

    await handleFileUpload(file);
  }; 
  
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!acceptedFiles.length || isUploading) return;
    
    const file = acceptedFiles[0];
    const { valid } = validateFile(file);
    if (!valid) return;
    
    await handleFileUpload(file);
  }, [isUploading]);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
    disabled: isUploading,
    noClick: false
  });

  const handleFileUpload = async (file: File) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      await propertyFiles.uploadFile({
        file,
        entityId: propertyId,
        description: 'document',
        onUploadProgress: (progress) => setUploadProgress(progress),
      });

      showToast({
        title: t('uploadSuccess') || 'File uploaded successfully',
      });
      fetchFiles();
    } catch (error) {
      console.error('Error uploading file:', error);
      showToast({
        title: t('errors.uploadFailed') || 'Failed to upload file',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    if (!confirm(t('deleteConfirm') || 'Are you sure you want to delete this document?')) return;

    try {
      await propertyFiles.deleteFile(fileId);
      showToast({
        title: t('deleteSuccess') || 'Document deleted successfully',
      });
      setFiles(files.filter((file) => file.id !== fileId));
    } catch (error) {
      console.error('Error deleting file:', error);
      showToast({
        title: t('errors.deleteFailed') || 'Failed to delete document',
        variant: 'destructive',
      });
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className="h-4 w-4" />;
    }
    return <FileText className="h-4 w-4" />;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">{t('loading') || 'Loading...'}</span>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{t('title') || 'Documents'}</CardTitle>
      </CardHeader>
      <CardContent>
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors mb-4',
            isDragActive 
              ? 'border-primary bg-primary/10' 
              : 'border-muted-foreground/25 hover:border-muted-foreground/50',
            isUploading && 'opacity-50 cursor-not-allowed',
            'group'
          )}
        >
          <input {...getInputProps()} />
          <div className="space-y-2">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center group-hover:bg-muted/80 transition-colors">
              {isUploading ? (
                <Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
              ) : (
                <Plus className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <p className="text-sm text-muted-foreground">
              {isDragActive
                ? t('dropHere') || 'Drop your files here'
                : t('dragAndDrop') || 'Drag & drop your files here, or click to browse'}
            </p>
            <p className="text-xs text-muted-foreground/70">
              {t('supportedFormats') || 'Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT'}
            </p>
          </div>
        </div>
        
        {uploadError && (
          <div className="mb-4 text-sm text-destructive">
            {uploadError}
          </div>
        )}
        {isUploading && (
          <div className="mb-4">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>{t('uploading') || 'Uploading'}...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="relative h-2 w-full overflow-hidden rounded-full bg-secondary">
              <div
                className="h-full bg-primary transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {files.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <File className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">{t('noDocuments') || 'No documents uploaded yet'}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {files.map((file) => (
              <div key={file.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <FileText className="h-6 w-6 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{file.fileName}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(file.fileSize)}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => window.open(file.url, '_blank')}
                    title={t('download') || 'Download'}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteFile(file.id)}
                    title={t('delete') || 'Delete'}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

"use client";

import { useTranslations } from 'next-intl';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertyService } from '@/app/services/propertyService';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { toast } from 'sonner';
import { Loader2, UserPlus, Trash2, MoreVertical } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { UserSelect } from '@/components/ui/user-select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { DatePicker } from '@/components/ui/date-picker';
import { format, parseISO } from 'date-fns';
import { useState, useCallback } from 'react';
import { getAccessColumns } from './access-columns';

const formSchema = z.object({
  userId: z.string().min(1, 'Please select a user'),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

interface PropertyAccessProps {
  propertyId: string;
}

export function PropertyAccess({ propertyId }: PropertyAccessProps) {
  const t = useTranslations('properties');
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Fetch property users
  const { data, isLoading, error } = useQuery({
    queryKey: ['propertyUsers', propertyId, page, pageSize],
    queryFn: () => propertyService.getPropertyUsers(propertyId, page, pageSize),
  });

  // Format dates for display in the table
  const formatDate = useCallback((date?: Date | string) => {
    if (!date) return 'N/A';
    try {
      const dateObj = typeof date === 'string' ? parseISO(date) : date;
      return format(dateObj, 'PPP');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }, []);

  // Format dates for API submission
  const formatDateForApi = useCallback((date?: Date | string) => {
    if (!date) return undefined;
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toISOString();
  }, []);



  // Add user mutation
  const addUserMutation = useMutation({
    mutationFn: async ({ userId, startDate, endDate }: { userId: string; startDate?: Date; endDate?: Date }) => {
      try {
        const payload: any = { userId };
        if (startDate) payload.startDate = startDate;
        if (endDate) payload.endDate = endDate;
        return await propertyService.addUserToProperty(propertyId, payload);
      } catch (error) {
        console.error('Error adding user to property:', error);
        throw error; // Re-throw to trigger onError
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['propertyUsers', propertyId] });
      toast.success('User added successfully', {
        position: 'top-right',
        duration: 3000,
      });
      form.reset();
      setIsDialogOpen(false);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to add user';
      console.error('Add user error:', errorMessage, error);
      toast.error(errorMessage, {
        position: 'top-right',
        duration: 5000,
      });
    },
  });

  // Form handling
  type FormValues = z.infer<typeof formSchema>;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      userId: '',
      startDate: undefined,
      endDate: undefined,
    },
  });

  // Watch form values for date range validation
  const watchedValues = form.watch();
  const startDateValue = watchedValues.startDate;
  const endDateValue = watchedValues.endDate;

  // Function to check if a date is disabled for start date picker
  const isStartDateDisabled = useCallback((date: Date) => {
    if (!endDateValue) return false;
    const endDate = endDateValue instanceof Date ? endDateValue : new Date(endDateValue);
    return date > endDate;
  }, [endDateValue]);

  // Function to check if a date is disabled for end date picker
  const isEndDateDisabled = useCallback((date: Date) => {
    if (!startDateValue) return false;
    const startDate = startDateValue instanceof Date ? startDateValue : new Date(startDateValue);
    return date < startDate;
  }, [startDateValue]);

  const handleRemoveUser = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this user?')) return;
    try {
      await propertyService.removeUserFromProperty(propertyId, userId);
      queryClient.invalidateQueries({ queryKey: ['propertyUsers', propertyId] });
      toast.success('User removed successfully', {
        position: 'top-right',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error removing user:', error);
      toast.error('Failed to remove user', {
        position: 'top-right',
        duration: 5000,
      });
    }
  };

  const onSubmit = (values: FormValues) => {
    addUserMutation.mutate({
      userId: values.userId,
      startDate: values.startDate ? new Date(values.startDate) : undefined,
      endDate: values.endDate ? new Date(values.endDate) : undefined,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-12 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 bg-muted/50 rounded-lg">
        <p className="text-destructive">
          Failed to load users. Please try again later.
        </p>
      </div>
    );
  }

  const columns = getAccessColumns(handleRemoveUser, false);

  const isRemoving = false; // Track removing state if needed

  return (
    <div className="space-y-4">
      <div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="sr-only">
              <UserPlus className="h-4 w-4 mr-2" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add User to Property</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="userId"
                  render={({ field, fieldState: { error } }) => (
                    <FormItem>
                      <FormLabel>Select User</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <UserSelect
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Search for a user..."
                            disabled={addUserMutation.isPending}
                          />
                          {addUserMutation.isPending && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                            </div>
                          )}
                        </div>
                      </FormControl>
                      {error ? (
                        <FormMessage>{error.message}</FormMessage>
                      ) : (
                        <p className="text-xs text-muted-foreground mt-1">
                          Start typing to search for users
                        </p>
                      )}
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Start Date (Optional)</FormLabel>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                          disabled={isStartDateDisabled}
                          placeholder="Select start date"
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>End Date (Optional)</FormLabel>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                          disabled={isEndDateDisabled}
                          placeholder="Select end date"
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={addUserMutation.isPending}
                    className={addUserMutation.isPending ? 'opacity-70' : ''}
                  >
                    {addUserMutation.isPending ? 'Adding...' : 'Add User'}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary/10 text-primary">
                <UserPlus className="h-5 w-5" />
              </div>
              <span>Users with Access</span>
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              Manage user access to this property
            </p>
          </div>
          <Button 
            onClick={() => setIsDialogOpen(true)}
            disabled={addUserMutation.isPending}
            className="w-full sm:w-auto"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>

        <div className="rounded-lg border bg-card">
          <div className="relative overflow-x-auto">
            {/* Desktop Table */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Access</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                          <span>Loading users...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : data?.data?.length ? (
                    data.data.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                              <span className="text-sm font-medium text-muted-foreground">
                                {user.firstName?.charAt(0).toUpperCase() || 'U'}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium">
                                {user.firstName} {user.lastName ? `${user.lastName.charAt(0)}.` : ''}
                              </div>
                              <div className="text-sm text-muted-foreground">{user.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>Tenant</TableCell>
                        <TableCell>Full Access</TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveUser(user.id)}
                            disabled={isRemoving}
                            className="text-destructive hover:bg-destructive/10"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Remove</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="h-64 text-center">
                        <div className="flex flex-col items-center justify-center space-y-2 py-8">
                          <UserPlus className="h-12 w-12 text-muted-foreground" />
                          <div className="text-lg font-medium">No users with access</div>
                          <p className="text-sm text-muted-foreground">
                            Add a user to grant them access to this property
                          </p>
                          <Button
                            variant="default"
                            onClick={() => setIsDialogOpen(true)}
                            className="mt-2"
                          >
                            <UserPlus className="h-4 w-4 mr-2" />
                            Add User
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-3 p-4">
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent mr-2" />
                  <span>Loading users...</span>
                </div>
              ) : data?.data?.length ? (
                data.data.map((user) => (
                  <Card key={user.id} className="overflow-hidden">
                    <CardHeader className="p-4 pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                            <span className="text-sm font-medium text-muted-foreground">
                              {user.firstName?.charAt(0).toUpperCase() || 'U'}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium">
                              {user.firstName} {user.lastName ? user.lastName.charAt(0) + '.' : ''}
                            </div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8 -mr-2">
                              <MoreVertical className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem 
                              onClick={() => handleRemoveUser(user.id)}
                              className="text-destructive"
                              disabled={isRemoving}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove Access
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-muted-foreground">Role</div>
                          <div>Tenant</div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Status</div>
                          <div>
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Active
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card className="text-center p-6">
                  <UserPlus className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-1">No users with access</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Add a user to grant them access to this property
                  </p>
                  <Button onClick={() => setIsDialogOpen(true)}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </Card>
              )}
            </div>
          </div>

          {/* Pagination */}
          {data?.data?.length ? (
            <div className="px-4 py-3 border-t">
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing <span className="font-medium">{(page - 1) * pageSize + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(page * pageSize, data.pagination?.total || 0)}
                  </span>{' '}
                  of <span className="font-medium">{data.pagination?.total || 0}</span> users
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(p => p + 1)}
                    disabled={page * pageSize >= (data?.pagination?.total || 0)}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}

type Messages = Record<string, any>;

// Cache for messages to avoid reloading them
const messageCache = new Map<string, Messages>();

// Import JSON files directly
import enMessages from '../messages/en/index';
import esMessages from '../messages/es/index';

export function getMessagesSync(locale: string): Messages {
  // Check cache first
  if (messageCache.has(locale)) {
    return messageCache.get(locale)!;
  }

  try {
    console.log(`[i18n] Loading messages synchronously for locale: ${locale}`);
    
    // Get messages based on locale
    let messages: Messages = {};
    
    if (locale === 'en') {
      messages = enMessages;
    } else if (locale === 'es') {
      messages = esMessages;
    } else {
      console.warn(`[i18n] Unsupported locale: ${locale}, falling back to 'en'`);
      messages = enMessages;
    }
    
    // Cache the result
    messageCache.set(locale, messages);
    
    // Log sample keys for debugging
    const sampleKeys = Object.keys(messages).slice(0, 5);
    console.log('[i18n] Successfully loaded messages for', locale);
    console.log('[i18n] Sample keys:', sampleKeys);

    return messages;
  } catch (error) {
    console.error(`[i18n] Error loading messages for ${locale}:`, error);
    return {};
  }
}

export async function getMessagesAsync(locale: string): Promise<Messages> {
  // For client-side, we can use the sync version since it's already cached
  return getMessagesSync(locale);
}

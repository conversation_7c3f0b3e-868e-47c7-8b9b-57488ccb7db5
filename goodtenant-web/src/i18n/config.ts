import {notFound} from 'next/navigation';
import {getMessagesSync} from './utils';

export const locales = ['en', 'es'] as const;
export type Locale = (typeof locales)[number];

export const defaultLocale: Locale = 'en';

export function isLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

export function getLocalePartsFrom({locale}: {locale: string}) {
  const localeParts = locale.toLowerCase().split('-');
  return {
    lang: localeParts[0],
  };
}

// This is the configuration for next-intl
export const i18n = {
  // These are all the locales you want to support in your application
  locales: locales as unknown as string[],
  // This is the default locale you want to be used when visiting
  // a non-locale prefixed path e.g. `/hello`
  defaultLocale,
  // Default locale detection is disabled, since the locales would be matched
  // using the `localePrefix` setting of the middleware
  localeDetection: false,
} as const;

// Get the messages for a specific locale
export function getMessages(locale: string) {
  try {
    console.log(`[i18n] Loading messages for locale: ${locale}`);
    const messages = getMessagesSync(locale as Locale);
    console.log(`[i18n] Successfully loaded messages for ${locale}`);
    return messages;
  } catch (error) {
    console.error(`[i18n] Error loading messages for ${locale}:`, error);
    notFound();
  }
}

// This is the configuration for next-intl's getRequestConfig
export async function getRequestConfig({locale}: {locale: string}) {
  if (!isLocale(locale)) {
    console.error(`[i18n] Invalid locale: ${locale}`);
    notFound();
  }

  try {
    const messages = getMessagesSync(locale);
    console.log(`[i18n] Loaded ${Object.keys(messages).length} messages for ${locale}`);
    
    return {
      messages,
      // You can add other i18n options here
      // https://next-intl-docs.vercel.app/docs/configuration#options
      timeZone: 'UTC',
      now: new Date(),
    };
  } catch (error) {
    console.error(`[i18n] Error in getRequestConfig for ${locale}:`, error);
    notFound();
  }
}
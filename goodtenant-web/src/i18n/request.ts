import {getRequestConfig} from 'next-intl/server';
import {notFound} from 'next/navigation';
import {getMessagesSync} from './utils';

// Define the locales your application supports
export const locales = ['en', 'es'] as const;
export type Locale = (typeof locales)[number];
export const defaultLocale: Locale = 'en';

// Validate that the incoming locale parameter is valid
export function isValidLocale(locale: string | undefined): locale is Locale {
  return locale ? locales.includes(locale as Locale) : false;
}

export default getRequestConfig(async ({locale}) => {
  console.log(`[i18n] Loading messages for locale: ${locale}`);
  
  // Validate that the incoming `locale` parameter is valid
  if (!isValidLocale(locale)) {
    console.error(`[i18n] Invalid locale: ${locale}`);
    notFound();
  }

  try {
    // Use synchronous loading for server components
    const messages = getMessagesSync(locale);
    console.log(`[i18n] Loaded messages for ${locale}:`, Object.keys(messages));
    
    return {
      locale,
      messages,
      timeZone: 'UTC'
    };
  } catch (error) {
    console.error(`[i18n] Error loading messages for ${locale}:`, error);
    throw error; // This will be caught by Next.js error boundary
  }
});
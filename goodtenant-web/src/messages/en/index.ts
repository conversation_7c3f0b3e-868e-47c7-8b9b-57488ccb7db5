// Import all English translation modules here
import dashboard from './dashboard.json';
import settings from './settings.json';
import users from './users.json';
import sidebar from './sidebar.json';
import navbar from './navbar.json';
import login from './login.json';
import hoas from './hoas.json';
import loans from './loans.json';
import insurance from './insurance.json';
import taxes from './taxes.json';
import utilities from './utilities.json';
import leases from './leases.json';
import maintenance from './maintenance.json';
import resetpassword from './resetpassword.json';
import register from './register.json';
import verifyEmail from './verify-email.json';
import documents from './documents.json';
import onboarding from './onboarding.json';
import properties from './properties.json';
import payments from './payments.json';
import templates from './templates.json';
import notifications from './notifications.json';


// Merge all messages into a single namespace
const messages = {
  ...dashboard,
  ...settings,
  ...users,
  ...sidebar,
  ...navbar,
  ...login,
  ...hoas,
  ...loans,
  ...insurance,
  ...taxes,
  ...utilities,
  ...leases,
  ...maintenance,
  ...resetpassword,
  ...register,
  ...verifyEmail,
  ...documents,
  ...onboarding,
  ...properties,
  ...payments,
  ...templates,
  ...notifications,
  };


export default messages;

// Export individual namespaces for type safety
export const namespaces = {
  dashboard,
  settings,
  users,
  sidebar,
  navbar,
  login,
  hoas,
  loans,
  insurance,
  taxes,
  utilities,
  leases,
  maintenance,
  resetpassword,
  register,
  verifyEmail,
  documents,
  onboarding,
  properties,
  payments,
  templates,
  notifications,
};

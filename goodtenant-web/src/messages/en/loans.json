{"loansTitle": "Loans", "loansDescription": "{count, plural, one {# loan} other {# loans}} found", "newLoan": "New Loan", "noLoans": "No loans found", "noLoansDescription": "You don't have any loans yet. Create one to get started.", "fetchError": "Failed to load loans. Please try again.", "deleteLoanTitle": "Delete Loan", "deleteLoanDescription": "Are you sure you want to delete this loan? This action cannot be undone.", "delete": "Delete", "deleting": "Deleting...", "deleteSuccess": "<PERSON>an deleted successfully", "deleteError": "Failed to delete loan", "basicDetails": "Basic Details", "enterBasicLoanDetails": "Enter basic loan information", "property": "Property", "loanName": "Loan Name", "enterLoanName": "Enter loan name", "loanAmount": "<PERSON><PERSON>", "interestRate": "Interest Rate", "loanTerm": "<PERSON>an <PERSON>", "numberOfYears": "Number of years", "loanType": "Loan Type", "enterLoanType": "Enter loan type", "lenderDetails": "Lender Details", "enterLenderInformation": "Enter lender information", "lenderName": "Lender Name", "enterLenderName": "Enter lender name", "lenderContactName": "Contact Name", "enterLenderContactName": "Enter contact name", "lenderEmail": "Lender Email", "enterLenderEmail": "Enter lender email", "lenderPhone": "Lender Phone", "enterLenderPhone": "Enter lender phone", "lenderWebsite": "Lender Website", "enterLenderWebsite": "Enter lender website", "lenderAddress": "Lender Address", "enterLenderAddress": "Enter lender address", "lenderCity": "City", "enterLenderCity": "Enter city", "lenderState": "State/Province", "enterLenderState": "Enter state/province", "lenderZipCode": "Postal Code", "enterLenderZipCode": "Enter postal code", "lenderCountry": "Country", "country": "Country", "selectCountry": "Select country", "saveChanges": "Save Changes", "cancel": "Cancel", "createLoan": "Create New Loan", "createLoanDescription": "Fill out the form below to add a new loan", "editLoan": "<PERSON>an", "editLoanDescription": "Update the loan details below", "loanCreated": "<PERSON>an created successfully", "loanUpdated": "Loan updated successfully", "loanNotFound": "Loan not found", "failedToFetchLoan": "Failed to load loan details", "errorLoadingLoan": "Error loading loan information", "updateLoanError": "Failed to update loan", "paymentDetails": "Payment Details", "enterPaymentInformation": "Enter payment information", "paymentDay": "Payment Day", "enterPaymentDay": "Day of month (1-31)", "monthlyPayment": "Monthly Payment", "notes": "Notes", "enterNotes": "Enter any additional notes", "saving": "Saving...", "back": "Back", "edit": "Edit", "viewLoan": "View Loan", "viewLoanDescription": "View loan details for this property", "loadingLoanDetails": "Loading loan details...", "loanDocumentsTitle": "Loan Documents", "loanDocumentsDescription": "Upload and manage loan-related documents", "uploading": "Uploading", "uploadingDetails": "Your file is being uploaded. Please wait...", "dropHere": "Drop the file here", "dragAndDrop": "Drag & drop files here, or click to select", "fileTypes": "PDF, DOCX, XLSX, JPG, PNG (max 10MB)", "uploadedFiles": "Uploaded Files", "upload": "Upload Document", "fileTooLarge": "File is too large. Maximum size is 10MB", "uploadSuccess": "File uploaded successfully", "allStatuses": "All Statuses", "active": "Active", "paid_off": "Paid <PERSON>", "defaulted": "Defaulted", "refinanced": "Refinanced"}
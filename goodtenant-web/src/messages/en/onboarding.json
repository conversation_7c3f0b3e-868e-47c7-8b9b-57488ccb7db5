{"noTokenProvided": "No invitation token provided. Please use the link from your invitation email.", "invalidToken": "Invalid or expired invitation token. Please request a new invitation.", "verificationError": "Error verifying your invitation. Please try again later.", "onboardingError": "Error completing onboarding. Please try again.", "errorTitle": "Error", "invalidInvitation": "Invalid Invitation", "contactSupport": "Please contact support if you believe this is an error.", "verifying": "Verifying your invitation...", "welcome": "Welcome to GoodTenant", "invitedTo": "You've been invited to", "backToHome": "Back to Home", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "password": "Password", "vehicles": "Vehicles", "addVehicle": "Add Vehicle", "noVehicles": "No vehicles added", "vehicle": "Vehicle", "make": "Make", "model": "Model", "year": "Year", "color": "Color", "licensePlate": "License Plate", "pets": "Pets", "addPet": "Add Pet", "noPets": "No pets added", "pet": "Pet", "name": "Name", "type": "Type", "breed": "Breed", "weight": "Weight", "age": "Age", "sex": "Sex", "registration": "Registration", "selectPetType": "Select pet type", "selectPetSex": "Select pet sex", "occupants": "Occupants", "addOccupant": "Add Occupant", "noOccupants": "No other occupants", "occupant": "Occupant", "relationship": "Relationship", "step": "Step", "of": "of", "back": "Back", "next": "Next", "complete": "Complete", "submitting": "Submitting...", "submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "required": "Required", "optional": "Optional", "petTypes": {"dog": "Dog", "cat": "Cat", "bird": "<PERSON>", "fish": "Fish", "reptile": "Reptile", "small_animal": "Small Animal", "other": "Other"}, "sexTypes": {"male": "Male", "female": "Female", "unknown": "Unknown"}, "documentsTitle": "Sign Your Documents", "documentsDescription": "Please review and sign the following documents to complete your onboarding.", "signHere": "Sign Here", "clearSignature": "Clear", "saveSignature": "Save Signature", "documentSigned": "Document signed successfully", "documentSigningError": "Error signing document. Please try again.", "allDocumentsSigned": "All documents have been signed successfully!"}
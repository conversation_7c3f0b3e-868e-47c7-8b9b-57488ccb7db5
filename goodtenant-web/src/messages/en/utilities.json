{"utilitiesTitle": "Utilities", "utilitiesDescription": "{count, plural, one {# utility} other {# utilities}} found", "addUtility": "Add Utility", "deleteUtilityTitle": "Delete Utility", "deleteUtilityDescription": "Are you sure you want to delete this utility? This action cannot be undone.", "deleteUtilitySuccess": "Utility deleted successfully", "deleteUtilityError": "Failed to delete utility", "noUtilitiesTitle": "No utilities found", "noUtilitiesDescription": "You don't have any utilities yet. Add one to get started.", "fetchError": "Failed to load utilities. Please try again.", "delete": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "newUtility": "New Utility", "utilityDescription": "Track and manage utility accounts for your properties", "editUtility": "Edit Utility", "editUtilityDescription": "Update the details of this utility account", "viewUtility": "View Utility", "backToUtilities": "Back to Utilities", "utilityDetails": "Utility Details", "financialDetails": "Financial Details", "financialDetailsDescription": "Set up billing and payment information", "property": "Property", "selectProperty": "Select a property", "utilityType": "Utility Type", "providerName": "Provider Name", "enterProviderName": "Enter provider name", "accountNumber": "Account Number", "enterAccountNumber": "Enter account number", "billingCycle": "Billing Cycle", "dueDate": "Due Date", "pickADay": "Day of month (1-31)", "amount": "Amount", "status": "Status", "isIncludedInRent": "Include in Rent", "isActive": "Active", "notes": "Notes", "notesPlaceholder": "Add any additional notes about this utility...", "save": "Save", "saving": "Saving...", "create": "Create", "creating": "Creating...", "successCreate": "Utility created successfully", "successUpdate": "Utility updated successfully", "unknown": "An unknown error occurred", "utilityNotFound": "Utility not found", "utilityDocumentsTitle": "Utility Documents", "utilityDocumentsDescription": "Upload and manage utility-related documents", "emptyTitleReadOnly": "No documents available", "emptyTitle": "No documents yet", "emptyDescriptionReadOnly": "There are no documents associated with this utility.", "emptyDescription": "Upload your first document by dragging and dropping files here or click the button below.", "upload": "Upload Document", "viewModeNotice": "View mode - Uploading files is disabled", "utilityDropHere": "Drop the file here", "utilityDragAndDrop": "Drag & drop files here, or click to select", "utilityFileTypes": "PDF, DOCX, XLSX, JPG, PNG (max 10MB)", "uploading": "Uploading", "uploadingDetails": "Your file is being uploaded. Please wait...", "dropOrClick": "Drop files here or click to upload", "maxSize": "Max file size: 10MB. PDF, images, and office docs allowed.", "loading": "Loading documents...", "confirmDelete": "Are you sure you want to delete this file?", "downloadFailed": "Failed to download file", "youAreInViewMode": "You are in view mode", "toMakeChanges": "to make changes", "selectUtilityType": "Select utility type", "allTypes": "All Types", "selectBillingCycle": "Select billing cycle", "monthly": "Monthly", "bimonthly": "Bimonthly", "quarterly": "Quarterly", "annually": "Annually"}
{"insuranceTitle": "Insurances", "insuranceDescription": "{count, plural, one {# insurance} other {# insurances}} found", "addInsurance": "Add Insurance", "noInsurances": "No insurances found", "noInsurancesDescription": "You don't have any insurances yet. Create one to get started.", "fetchError": "Failed to load insurances. Please try again.", "deleteConfirmTitle": "Delete Insurance", "deleteConfirmDescription": "Are you sure you want to delete this insurance? This action cannot be undone.", "delete": "Delete", "deleting": "Deleting...", "deleteSuccess": "Insurance deleted successfully", "deleteError": "Failed to delete insurance", "createSuccess": "Insurance created successfully", "createError": "Failed to create insurance. Please try again.", "submitError": "An error occurred while submitting the form. Please try again.", "newInsurance": "New Insurance", "addNewInsuranceDescription": "Fill in the details below to create a new insurance policy.", "createInsurance": "Create Insurance", "policyInformationTitle": "Policy Information", "policyInformationDescription": "Basic information about the insurance policy", "policyDetailsTitle": "Policy Details", "policyDetailsDescription": "Detailed information about the coverage", "policyPeriodTitle": "Policy Period", "policyPeriodDescription": "Start and end dates of the policy", "paymentInformationTitle": "Payment Information", "paymentInformationDescription": "Payment details and status", "agentInformationTitle": "Agent Information", "agentInformationDescription": "Contact information for the insurance agent", "additionalInformationTitle": "Additional Information", "additionalInformationDescription": "Any additional notes or details", "provider": "Insurance Provider", "providerPlaceholder": "Enter insurance company name", "policyNumber": "Policy Number", "policyNumberPlaceholder": "Enter policy number", "insuranceType": "Insurance Type", "premiumAmount": "Premium Amount", "premiumAmountPlaceholder": "0.00", "coverageAmount": "Coverage Amount", "coverageAmountPlaceholder": "0.00", "deductible": "Deductible", "deductiblePlaceholder": "0.00", "paymentFrequency": "Payment Frequency", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "effectiveStartDate": "Start Date", "effectiveEndDate": "End Date", "agentName": "Agent Name", "agentNamePlaceholder": "Enter agent's name", "agentPhone": "Agent Phone", "agentPhonePlaceholder": "Enter agent's phone number", "agentEmail": "Agent <PERSON><PERSON>", "agentEmailPlaceholder": "Enter agent's email address", "notes": "Notes", "notesPlaceholder": "Enter any additional notes or details", "save": "Save", "cancel": "Cancel", "viewInsurance": "View Insurance", "viewInsuranceDescription": "View and manage insurance policy details", "youAreInViewMode": "You are in view mode", "editInsurance": "Edit Insurance", "editInsuranceDescription": "Update the insurance policy details", "toMakeChanges": "to make changes", "backToInsurances": "Back to Insurances", "youAreEditingThis": "You are editing this insurance policy.", "switchToViewMode": "Switch to view mode", "toViewDetails": "to view details", "saveChanges": "Save Changes", "insuranceUpdatedSuccessfully": "Insurance updated successfully", "errorUpdatingInsurance": "Failed to update insurance. Please try again.", "documents": "Documents", "property": "Property", "liability": "Liability", "renters": "Renters", "landlord": "Landlord", "other": "Other", "invalidFileType": "Invalid file type. Please upload a PDF, image, or document file.", "uploadFailed": "Failed to upload file. Please try again.", "uploadSuccess": "File uploaded successfully", "deleteFailed": "Failed to delete file. Please try again.", "noDocuments": "No documents yet", "uploadDescription": "Upload your first document by dragging and dropping files here or click the button below.", "uploadedFiles": "Uploaded Files", "insuranceDocumentTitle": "Insurance Documents", "insuranceDocumentDescription": "Upload and manage insurance-related documents"}
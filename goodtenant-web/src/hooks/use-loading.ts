import { useState, useCallback } from 'react';

export function useLoading(initialState = false) {
  const [loading, setLoading] = useState(initialState);

  const withLoading = useCallback(async <T,>(promise: Promise<T> | (() => Promise<T>)): Promise<T> => {
    setLoading(true);
    try {
      const result = await (typeof promise === 'function' ? promise() : promise);
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    setLoading,
    withLoading,
  };
}

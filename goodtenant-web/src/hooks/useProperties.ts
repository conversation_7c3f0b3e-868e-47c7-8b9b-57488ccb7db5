'use client';

import { useState, useEffect } from 'react';
import { propertyService } from '@/app/services/propertyService';

interface UsePropertiesResult {
  data: any[];
  isLoading: boolean;
  error: Error | null;
}

export function useProperties(): UsePropertiesResult {
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchProperties = async () => {
      try {
        setIsLoading(true);
        const response = await propertyService.getProperties();
        setData(Array.isArray(response) ? response : response.data || []);
      } catch (err) {
        console.error('Error fetching properties:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch properties'));
        // Ensure we still return an empty array on error
        setData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProperties();
  }, []);

  return { data, isLoading, error };
}

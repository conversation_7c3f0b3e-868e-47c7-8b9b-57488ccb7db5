import { useCallback } from 'react';
import { useNotifications } from '@/app/context/NotificationContext';
import { NotificationType, NotificationEntity } from '@/types/notification';

type NotificationOptions = {
  type?: NotificationType;
  entityType?: NotificationEntity;
  entityId?: string;
  actionUrl?: string;
};

export const useNotification = () => {
  const { addNotification } = useNotifications();

  const notify = useCallback(
    async (
      userId: string,
      title: string,
      message: string,
      options: NotificationOptions = {}
    ) => {
      try {
        const notification = await addNotification({
          userId,
          title,
          message,
          type: options.type || 'SYSTEM',
          entityType: options.entityType,
          entityId: options.entityId,
          actionUrl: options.actionUrl,
          isRead: false,
        });
        return notification;
      } catch (error) {
        console.error('Failed to create notification:', error);
        throw error;
      }
    },
    [addNotification]
  );

  return { notify };
};

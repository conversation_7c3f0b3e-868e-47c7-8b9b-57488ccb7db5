// goodtenant-web/src/hooks/useSignature.ts
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/app/context/AuthContext';
import * as signatureService from '@/app/services/signatureService';
import { useToast } from '@/components/ui/use-toast';

export const useSignature = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [signature, setSignature] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchSignature = useCallback(async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const sig = await signatureService.getSignature();
      setSignature(sig?.url || null);
    } catch (err) {
      console.error('Error fetching signature:', err);
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchSignature();
  }, [fetchSignature]);

  const uploadSignature = async (file: File) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await signatureService.uploadSignature(file);
      setSignature(result.url);
      toast({
        title: 'Success',
        description: 'Signature uploaded successfully',
      });
      return result;
    } catch (err) {
      console.error('Error uploading signature:', err);
      setError(err as Error);
      toast({
        title: 'Error',
        description: 'Failed to upload signature',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const removeSignature = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await signatureService.deleteSignature();
      setSignature(null);
      toast({
        title: 'Success',
        description: 'Signature removed successfully',
      });
    } catch (err) {
      console.error('Error removing signature:', err);
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signature,
    isLoading,
    error,
    uploadSignature,
    removeSignature,
    deleteSignature: removeSignature, // Alias for backward compatibility
    refresh: fetchSignature,
  };
};
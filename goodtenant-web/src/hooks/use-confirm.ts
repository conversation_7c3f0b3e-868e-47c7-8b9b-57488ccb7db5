import { useState, useCallback } from 'react';

interface ConfirmOptions {
  title: string;
  description: string;
  confirmText: string;
  cancelText: string;
  variant?: 'default' | 'destructive';
  onConfirm?: () => void;
  onCancel?: () => void;
}

export function useConfirm(defaultOptions?: Partial<ConfirmOptions>) {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<ConfirmOptions>({
    title: 'Are you sure?',
    description: 'This action cannot be undone.',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    variant: 'default',
    ...defaultOptions,
  });
  const [isConfirming, setIsConfirming] = useState(false);

  const confirm = useCallback((customOptions?: Partial<ConfirmOptions>) => {
    return new Promise<boolean>((resolve) => {
      setOptions(prev => ({
        ...prev,
        ...customOptions,
        onConfirm: async () => {
          setIsConfirming(true);
          try {
            await customOptions?.onConfirm?.();
            resolve(true);
          } finally {
            setIsConfirming(false);
            setIsOpen(false);
          }
        },
        onCancel: () => {
          customOptions?.onCancel?.();
          resolve(false);
          setIsOpen(false);
        },
      }));
      setIsOpen(true);
    });
  }, []);

  return {
    isOpen,
    setIsOpen,
    confirm,
    isConfirming,
    ...options,
  };
}

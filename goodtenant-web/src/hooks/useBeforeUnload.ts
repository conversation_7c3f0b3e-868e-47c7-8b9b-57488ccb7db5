import { useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook that shows a browser confirmation dialog when trying to leave the page
 * with unsaved changes.
 * 
 * @param {string | null} message - The message to show in the confirmation dialog.
 *                               If null or undefined, the beforeunload handler will be removed.
 */
export function useBeforeUnload(message: string | null) {
  const savedCallback = useRef<typeof message>(null);

  // Update the saved callback if the message changes
  useEffect(() => {
    savedCallback.current = message;
  }, [message]);

  // Handle the beforeunload event
  const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
    if (savedCallback.current) {
      // Modern browsers require setting returnValue to show the dialog
      event.preventDefault();
      // Chrome requires returnValue to be set
      event.returnValue = savedCallback.current;
      // Return the message for older browsers
      return savedCallback.current;
    }
  }, []);

  // Set up and clean up the event listener
  useEffect(() => {
    if (typeof window === 'undefined') return;

    if (savedCallback.current) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [handleBeforeUnload]);
}

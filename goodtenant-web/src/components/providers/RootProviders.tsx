"use client";

import { ThemeProvider } from "@/components/theme-provider";
import LocaleProvider from "@/components/LocaleProvider";
import { AuthProvider, useAuth } from "@/app/context/AuthContext";
import { QueryProvider } from "@/providers/query-provider";
import { NotificationProvider } from "../../app/context/NotificationContext";
import { Toaster } from "sonner";
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { ErrorBoundary } from 'react-error-boundary';
import { Button } from "@/components/ui/button";
import { AlertCircle, Loader2 } from "lucide-react";
import { useEffect, useState } from 'react';
import { stripeService } from "@/app/services/stripeService";
import { StripeProvider } from "@/app/context/StripeContext";

// Component to handle Stripe Elements with dynamic publishable key
const StripeProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  const { user } = useAuth();
  const [stripePromise, setStripePromise] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [stripeAvailable, setStripeAvailable] = useState(true);

  useEffect(() => {
    const initializeStripe = async () => {
      try {
        if (!user) {
          setLoading(false);
          return;
        }

        // Get the first account ID from either accounts or accountUsers array
        const accountId = user.accounts?.[0]?.id || user.accountUsers?.[0]?.accountId;
        if (!accountId) {
          setStripeAvailable(false);
          setLoading(false);
          return;
        }

        try {
          // Fetch the publishable key from the API
          const { publishableKey } = await stripeService.getPublishableKey(accountId);
          if (publishableKey) {
            // Initialize Stripe with the fetched key
            setStripePromise(loadStripe(publishableKey));
            setStripeAvailable(true);
          } else {
            // No publishable key available, but don't treat as fatal error
            setStripeAvailable(false);
          }
        } catch (stripeErr) {
          // Stripe service unavailable, but don't treat as fatal error
          console.warn('Stripe service unavailable:', stripeErr);
          setStripeAvailable(false);
        }
      } catch (err) {
        console.error('Error during app initialization:', err);
        setStripeAvailable(false);
      } finally {
        setLoading(false);
      }
    };

    initializeStripe();
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-10">
        <Loader2 className="h-5 w-5 animate-spin" />
      </div>
    );
  }

  // If Stripe is not available, just render children without Elements wrapper
  if (!stripeAvailable || !stripePromise) {
    // Just pass through children without Stripe integration
    return <>{children}</>;
  }

  // Stripe is available and initialized
  return (
    <Elements stripe={stripePromise}>
      {children}
    </Elements>
  )
};

// Fallback component when Stripe fails to load
const StripeErrorFallback = ({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => (
  <div className="flex items-center justify-center min-h-screen p-4">
    <div className="text-center">
      <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h2 className="text-xl font-semibold mb-2">Payment Error</h2>
      <p className="text-muted-foreground mb-4">
        {error.message || 'An error occurred while loading the payment system.'}
      </p>
      <Button onClick={resetErrorBoundary}>
        Try Again
      </Button>
    </div>
  </div>
);

// Error boundary fallback component
function ErrorFallback({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void 
}) {
  return (
    <div className="flex h-screen w-full flex-col items-center justify-center gap-4 p-4 text-center">
      <AlertCircle className="h-12 w-12 text-destructive" />
      <h1 className="text-2xl font-bold">Something went wrong</h1>
      <p className="text-muted-foreground">
        {error.message || 'An unexpected error occurred'}
      </p>
      <Button
        variant="outline"
        onClick={resetErrorBoundary}
        className="mt-4"
      >
        Try again
      </Button>
    </div>
  );
}

// Main content component wrapped in providers
function ProvidersContent({ children }: { children: React.ReactNode }) {
  // Handle uncaught errors
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('Uncaught error:', event.error);
      // You can log this to an error reporting service
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <AuthProvider>
        <StripeProvider>
          <QueryProvider>
            <NotificationProvider>
              <ErrorBoundary FallbackComponent={StripeErrorFallback}>
                <StripeProviderWrapper>
                  {children}
                  <Toaster 
                    richColors
                    position="top-right"
                    theme="system"
                    className="font-sans"
                  />
                </StripeProviderWrapper>
              </ErrorBoundary>
            </NotificationProvider>
          </QueryProvider>
        </StripeProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export function RootProviders({ 
  children,
  locale 
}: { 
  children: React.ReactNode;
  locale: string;
}) {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // Reset the error boundary when the user clicks the reset button
        window.location.reload();
      }}
    >
      <LocaleProvider locale={locale}>
        <ErrorBoundary
          FallbackComponent={ErrorFallback}
          onReset={() => {
            // Reset the error boundary when the user clicks the reset button
            window.location.reload();
          }}
        >
          <ProvidersContent>
            {children}
          </ProvidersContent>
        </ErrorBoundary>
      </LocaleProvider>
    </ErrorBoundary>
  );
}

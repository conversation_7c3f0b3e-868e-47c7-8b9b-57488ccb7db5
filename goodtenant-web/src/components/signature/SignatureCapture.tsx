// goodtenant-web/src/components/signature/SignatureCapture.tsx
'use client';

import { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2, Trash2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { 
  base64ToFile, 
  getSignature, 
  type SignatureData as ApiSignatureData 
} from '@/app/services/signatureService';
import { useSignature } from '@/hooks/use-signature';

// Dynamically import the signature pad to avoid SSR issues
// Create a wrapped version of SignaturePad that properly handles refs
const SignaturePadWrapper = dynamic(
  () => import('react-signature-canvas').then((mod) => {
    const Component = mod.default;
    // Create a wrapper component that forwards the ref
    const WrappedSignaturePad = ({ innerRef, ...props }: any) => (
      <Component ref={innerRef} {...props} />
    );
    return WrappedSignaturePad;
  }),
  { 
    ssr: false,
    loading: () => <div className="w-full h-48 bg-gray-100 flex items-center justify-center">Loading signature pad...</div>
  }
);

// Main component that uses the wrapped SignaturePad
const SignaturePad = ({ ...props }: any) => {
  return <SignaturePadWrapper {...props} innerRef={props.innerRef} />;
};

// Re-export the SignatureData type from the service
type SignatureData = ApiSignatureData;

interface SignatureCaptureProps {
  onSave?: (signatureData: SignatureData) => void;
  onCancel?: () => void;
  initialSignatureUrl?: string | null;
}

export const SignatureCapture = ({ onSave, onCancel, initialSignatureUrl = null }: SignatureCaptureProps) => {
  const { uploadSignature, removeSignature: deleteSignature, isLoading } = useSignature();
  const t = useTranslations();
  const [signature, setSignature] = useState<string | null>(null);
  const [existingSignature, setExistingSignature] = useState<{ url: string; expiresAt?: string } | null>(null);
  const [imageLoadError, setImageLoadError] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const sigCanvas = useRef<any>(null);


  // Update existingSignature when initialSignatureUrl changes
  useEffect(() => {
    const loadSignature = (url: string) => {
      console.log('Setting signature URL:', url);
      setExistingSignature({
        url: url,
        expiresAt: new Date(Date.now() + 3600 * 1000).toISOString()
      });
      setImageLoadError(false);
      return true;
    };

    const initializeSignature = async () => {
      try {
        if (initialSignatureUrl) {
          console.log('Using initialSignatureUrl:', initialSignatureUrl);
          loadSignature(initialSignatureUrl);
        } else {
          console.log('Fetching signature data from API...');
          const sig = await getSignature();
          console.log('Signature API response:', sig);
          
          if (sig) {
            // Use signedUrl if available, otherwise fall back to url
            const imageUrl = sig.signedUrl || sig.url;
            if (imageUrl) {
              console.log('Setting signature URL from API:', imageUrl);
              loadSignature(imageUrl);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing signature:', error);
        setImageLoadError(true);
      }
    };

    initializeSignature();
  }, [initialSignatureUrl]);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteSignature();
      setExistingSignature(null);
      toast.success(t('signatureDeleteSuccess'));
    } catch (error) {
      console.error('Error deleting signature:', error);
      toast.error(t('signatureDeleteError'));
    } finally {
      setIsDeleting(false);
    }
  };

  const clear = () => {
    sigCanvas.current?.clear();
    setSignature(null);
    setExistingSignature(null);
  };

  const handleSave = async () => {
    if (!signature) {
      const signatureData = sigCanvas.current?.toDataURL('image/png');
      if (signatureData) {
        setSignature(signatureData);
      }
      return;
    }

    try {
      console.log('Saving signature...');
      const file = base64ToFile(signature, 'signature.png');
      const result = await uploadSignature(file);
      
      console.log('Upload result:', result);
      
      // The API returns the signature data with url/signedUrl
      const imageUrl = result.signedUrl || result.url;
      if (!imageUrl) {
        throw new Error('No URL returned from server');
      }
      
      // Update local state with the new signature
      setExistingSignature({
        url: imageUrl,
        expiresAt: result.expiresAt || new Date(Date.now() + 3600 * 1000).toISOString()
      });
      
      // Notify parent with the saved signature data
      onSave?.(result);
      
      // Show success message
      toast.success(t('signature.saveSuccess'));
    } catch (error) {
      console.error('Error saving signature:', error);
      toast.error(t('signature.saveError'));
      throw error; // Re-throw to allow parent component to handle the error
    }
  };

  const handleEnd = () => {
    const signatureData = sigCanvas.current?.toDataURL('image/png');
    if (signatureData) {
      setSignature(signatureData);
    }
  };

  return (
    <div className="space-y-4">
      {existingSignature ? (
        <div className="space-y-4">
          <div className="border rounded-md p-4 bg-white">
            <div className="relative w-full h-48 flex items-center justify-center border rounded bg-gray-50">
            {existingSignature && !imageLoadError ? (
              <Image
                src={existingSignature.url}
                alt="Saved signature"
                fill
                className="object-contain p-4"
                sizes="(max-width: 768px) 100vw, 50vw"
                onLoad={() => setImageLoadError(false)}
                onError={() => {
                  console.error('Failed to load signature image');
                  setImageLoadError(true);
                }}
                unoptimized={process.env.NODE_ENV !== 'production'}
              />
            ) : (
              <div className="text-center p-4 text-gray-500">
                <p>{t('signatureLoadError')}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => {
                    setImageLoadError(false);
                    if (existingSignature) {
                      const newUrl = `${existingSignature.url}${existingSignature.url.includes('?') ? '&' : '?'}t=${Date.now()}`;
                      setExistingSignature({
                        ...existingSignature,
                        url: newUrl
                      });
                    }
                  }}
                >
                  {t('signatureRetry')}
                </Button>
              </div>
            )}
          </div>
          </div>
          <div className="flex justify-end">
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading || isDeleting}
              className="w-full sm:w-auto"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t('signatureDeleteButton')}
                </>
              )}
            </Button>
          </div>
        </div>
      ) : (
        <>
          <div className="border rounded-md p-4 bg-white">
            <SignaturePad
              innerRef={sigCanvas}
              canvasProps={{
                className: 'w-full h-48 bg-white',
              }}
              onEnd={handleEnd}
            />
          </div>
          
          <div className="flex justify-end">
            <div className="space-x-2">
              {/* Show Clear button only in edit mode and when there's a signature */}
              {!existingSignature && signature && (
                <Button
                  variant="outline"
                  onClick={clear}
                  disabled={isLoading}
                >
                  {t('signatureClearButton')}
                </Button>
              )}
              
              <Button
                onClick={handleSave}
                disabled={isLoading || !signature}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  existingSignature ? t('signatureUpdateButton') : t('signatureSaveButton')
                )}
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
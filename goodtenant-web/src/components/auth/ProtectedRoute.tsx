'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import { LoadingSpinner } from '../ui/loading-spinner';

type ProtectedRouteProps = {
  children: React.ReactNode;
  requiredRoles?: string[];
  isPublic?: boolean; // Add this prop to mark public routes
  loadingComponent?: React.ReactNode;
  unauthorizedComponent?: React.ReactNode;
};

// List of public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/tenant-onboarding',
];

export function ProtectedRoute({ 
  children, 
  requiredRoles, 
  isPublic = false, 
  loadingComponent = <LoadingSpinner />, 
  unauthorizedComponent = <div>Unauthorized</div> 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user, role, account } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Check if current route is public
  const isRoutePublic = isPublic || publicRoutes.some(route => 
    pathname?.includes(route) || 
    pathname?.startsWith(`/en${route}`)
  );

  useEffect(() => {
    // If it's a public route, don't redirect
    if (isRoutePublic) {
      return;
    }

    // Handle unauthenticated users for protected routes
    if (!isLoading && !isAuthenticated) {
      const locale = pathname?.split('/')[1] || 'en';
      router.push(`/${locale}/auth/login?redirect=${encodeURIComponent(pathname)}`);
      return;
    }

    // Check user roles if required
    if (!isLoading && isAuthenticated && requiredRoles?.length) {
      // Check three sources of role information
      const hasRequiredRole = 
        // 1. Check role from context (primary role)
        (role && requiredRoles.includes(role)) ||
        // 2. Check account role if available (ensure 'roles' property exists)
        (account && 'roles' in account && account.roles?.primaryRole && requiredRoles.includes(account.roles.primaryRole)) ||
        // 3. Fall back to user.roles array
        user?.roles?.some(userRole => requiredRoles.includes(userRole.name));
      
      if (!hasRequiredRole) {
        const locale = pathname?.split('/')[1] || 'en';
        router.push(`/${locale}/dashboard`);
        return;
      }
    }
  }, [isAuthenticated, isLoading, pathname, router, requiredRoles, user, isRoutePublic, unauthorizedComponent]);

  // Don't render anything until we know the auth state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        {loadingComponent}
      </div>
    );
  }

  // If it's a public route, render the children
  if (isRoutePublic) {
    return <>{children}</>;
  }

  // If not authenticated, show unauthorized or redirect
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        {unauthorizedComponent}
      </div>
    );
  }

  // Check roles if required
  if (requiredRoles?.length) {
    // Check three sources of role information
    const hasRequiredRole = 
      // 1. Check role from context (primary role)
      (role && requiredRoles.includes(role)) ||
      // 2. Check account role if available (ensure 'roles' property exists)
      (account && 'roles' in account && account.roles?.primaryRole && requiredRoles.includes(account.roles.primaryRole)) ||
      // 3. Fall back to user.roles array
      user?.roles?.some(userRole => requiredRoles.includes(userRole.name));
    
    if (!hasRequiredRole) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          {unauthorizedComponent}
        </div>
      );
    }
  }

  // If we get here, user is authenticated and has required roles
  return <>{children}</>;
}

'use client';

import { <PERSON><PERSON>ontaine<PERSON>, TileLayer } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { Property } from '@/types/property';
import { useEffect, useState } from 'react';
import { CustomMarker } from './CustomMarker';

interface PropertiesMapProps {
  properties: Property[];
  height?: string;
  zoom?: number;
  className?: string;
}

export default function PropertiesMap({ 
  properties, 
  height = '500px', 
  zoom = 13,
  className = ''
}: PropertiesMapProps) {
  const [isClient, setIsClient] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>([0, 0]);

  useEffect(() => {
    console.log('Properties in map component:', properties);
    setIsClient(true);
    
    // Filter out properties without coordinates
    const validProperties = properties.filter(p => 
      p.latitude !== undefined && 
      p.longitude !== undefined &&
      p.latitude !== null && 
      p.longitude !== null
    );
    
    console.log('Valid properties with coordinates:', validProperties);
    
    // Set map center to the first valid property or default to a central location
    if (validProperties.length > 0) {
      const firstProperty = validProperties[0];
      const center: [number, number] = [
        Number(firstProperty.latitude), 
        Number(firstProperty.longitude)
      ];
      console.log('Setting map center to:', center);
      setMapCenter(center);
    } else {
      console.log('No valid properties with coordinates found');
    }
  }, [properties]);

  // Don't render the map on the server side
  if (!isClient) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 ${className}`} 
        style={{ height }}
      >
        <div className="text-gray-500">Loading map...</div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <MapContainer 
        center={mapCenter} 
        zoom={zoom} 
        style={{ height: '100%', width: '100%', borderRadius: '0.5rem' }}
        className="z-0"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {properties
          .filter(property => 
            property.latitude !== undefined && 
            property.longitude !== undefined &&
            property.latitude !== null && 
            property.longitude !== null
          )
          .map((property) => {
            const position: [number, number] = [
              Number(property.latitude), 
              Number(property.longitude)
            ];
            
            // Example: Add random notifications for demo purposes
            // In a real app, you would get this from your notifications/events data
            const notificationCount = Math.floor(Math.random() * 5); // 0-4 notifications
            
            // Different colors based on property type or status
            const colors = {
              apartment: '#3b82f6', // blue
              house: '#10b981',     // green
              condo: '#8b5cf6',     // purple
              townhouse: '#f59e0b', // amber
              commercial: '#ec4899', // pink
              other: '#6b7280'      // gray
            };
            
            const markerColor = colors[property.propertyType as keyof typeof colors] || colors.other;
            
            return (
              <CustomMarker
                key={property.id}
                position={position}
                property={property}
                notificationCount={notificationCount}
                color={markerColor}
              />
            );
          })}
      </MapContainer>
    </div>
  );
}

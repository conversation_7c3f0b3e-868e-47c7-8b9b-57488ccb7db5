import { Marker, Popup } from 'react-leaflet';
import { divIcon } from 'leaflet';
import { renderToStaticMarkup } from 'react-dom/server';

interface CustomMarkerProps {
  position: [number, number];
  property: {
    id: string;
    name?: string;
    addressLine1?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    priceAmount?: number;
    priceInterval?: string;
  };
  notificationCount?: number;
  color?: string;
}

export function CustomMarker({ 
  position, 
  property, 
  notificationCount = 0,
  color = '#3b82f6' // Default blue color
}: CustomMarkerProps) {
  // Create a custom icon with a badge
  const createCustomIcon = () => {
    const html = `
      <div style="position: relative; transform: translate(-50%, -100%);">
        <svg width="30" height="40" viewBox="0 0 30 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            d="M15 0C6.71573 0 0 6.71573 0 15C0 22.5 15 40 15 40C15 40 30 22.5 30 15C30 6.71573 23.2843 0 15 0Z" 
            fill="${color}" 
            stroke="white" 
            stroke-width="2"
          />
          ${notificationCount > 0 ? `
            <circle 
              cx="22" 
              cy="10" 
              r="10" 
              fill="#ef4444" 
              stroke="white" 
              stroke-width="2"
            />
            <text 
              x="22" 
              y="14" 
              font-family="Arial" 
              font-size="10" 
              font-weight="bold" 
              fill="white" 
              text-anchor="middle"
              alignment-baseline="middle"
            >
              ${notificationCount > 9 ? '9+' : notificationCount}
            </text>` : ''}
        </svg>
      </div>
    `;

    return divIcon({
      html,
      className: 'custom-marker',
      iconSize: [30, 40],
      iconAnchor: [15, 40],
      popupAnchor: [0, -40],
    });
  };

  return (
    <Marker 
      position={position}
      icon={createCustomIcon()}
    >
      <Popup>
        <div className="space-y-1 min-w-[200px]">
          <h4 className="font-semibold text-base">{property.name || 'Unnamed Property'}</h4>
          {property.addressLine1 && (
            <p className="text-sm">{property.addressLine1}</p>
          )}
          {(property.city || property.state || property.postalCode) && (
            <p className="text-sm">
              {[property.city, property.state, property.postalCode]
                .filter(Boolean)
                .join(', ')}
            </p>
          )}
          {property.priceAmount && (
            <p className="text-sm text-blue-600 font-medium">
              ${property.priceAmount.toLocaleString()}
              {property.priceInterval ? ` / ${property.priceInterval}` : ''}
            </p>
          )}
          {notificationCount > 0 && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              {notificationCount} new notification{notificationCount > 1 ? 's' : ''}
            </div>
          )}
        </div>
      </Popup>
    </Marker>
  );
}

'use client';

import { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';


type PropertyFilterProps = {
  value: string | undefined;
  onChange: (value: string | undefined) => void;
  className?: string;
  properties: Array<{ id: string; name: string }>;
  small?: boolean;
  disabled?: boolean;
};

export function PropertyFilter({ 
  value, 
  onChange, 
  className, 
  properties = [], 
  small = false, 
  disabled = false 
}: PropertyFilterProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations('common');
  
  const ALL_PROPERTIES = 'all';
  const selectValue = value || ALL_PROPERTIES;

  const filteredProperties = useMemo(() => {
    if (!searchTerm) return properties;
    return properties.filter(property => 
      property.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [properties, searchTerm]);

  const selectedProperty = properties.find(p => p.id === value);

  const handleValueChange = (val: string) => {
    if (disabled) return;
    onChange(val === ALL_PROPERTIES ? undefined : val);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled) return;
    onChange(undefined);
    setSearchTerm('');
  };

  return (
    <div className={cn('relative w-full', className)}>
     
      
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className={cn(
            'flex items-center justify-between w-full text-left',
            'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
            'rounded-md shadow-sm',
            small ? 'h-8 px-2 text-sm' : 'h-10 px-3',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            'transition-colors duration-200',
            disabled && 'opacity-50 cursor-not-allowed',
            className
          )}
          disabled={disabled}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          aria-labelledby="property-label"
        >
          <span className="truncate">
            {selectedProperty ? selectedProperty.name : t('allProperties')}
          </span>
          <div className="flex items-center ml-2">
            {value && (
              <X 
                className="h-4 w-4 text-muted-foreground hover:text-foreground mr-1"
                onClick={handleClear}
                aria-label="Clear selection"
              />
            )}
            <ChevronDown className={cn("h-4 w-4 text-muted-foreground transition-transform", {
              'transform rotate-180': isOpen
            })} />
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-popover rounded-md shadow-lg border">
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={t('searchProperties')}
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  autoFocus
                />
              </div>
            </div>
            
            <ScrollArea className="max-h-60 overflow-auto">
              <div role="listbox" className="py-1">
                <button
                  type="button"
                  onClick={() => handleValueChange(ALL_PROPERTIES)}
                  className={cn(
                    'w-full text-left px-4 py-2 text-sm',
                    'hover:bg-accent hover:text-accent-foreground',
                    'focus:bg-accent focus:text-accent-foreground outline-none',
                    selectValue === ALL_PROPERTIES ? 'bg-accent font-medium' : ''
                  )}
                  role="option"
                  aria-selected={selectValue === ALL_PROPERTIES}
                >
                  {t('allProperties')}
                </button>
                
                {filteredProperties.length > 0 ? (
                  filteredProperties.map((property) => (
                    <button
                      key={property.id}
                      type="button"
                      onClick={() => handleValueChange(property.id)}
                      className={cn(
                        'w-full text-left px-4 py-2 text-sm',
                        'hover:bg-accent hover:text-accent-foreground',
                        'focus:bg-accent focus:text-accent-foreground outline-none',
                        'flex items-center',
                        selectValue === property.id ? 'bg-accent font-medium' : ''
                      )}
                      role="option"
                      aria-selected={selectValue === property.id}
                    >
                      <span className="truncate">{property.name}</span>
                    </button>
                  ))
                ) : (
                  <div className="px-4 py-2 text-sm text-muted-foreground">
                    {t('noPropertiesFound')}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    </div>
  );
}

// Add ChevronDown if not already imported
const ChevronDown = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="m6 9 6 6 6-6" />
  </svg>
);

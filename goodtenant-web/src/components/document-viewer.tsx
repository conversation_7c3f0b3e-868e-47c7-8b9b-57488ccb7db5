'use client';

import { TemplateContent, TemplateContentItem } from '@/types/template';
import { cn } from '@/lib/utils';
import { useState, useRef, useCallback, useEffect, forwardRef, useImperativeHandle } from 'react';
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Edit3, Pen, Trash2, Save, Download } from 'lucide-react';
import dynamic from 'next/dynamic';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { getLandlordSignatureByLease } from '@/app/services/signatureService';

// Import SignaturePad dynamically to avoid SSR issues
const SignaturePad = dynamic<any>(
  () => import('react-signature-canvas'),
  { ssr: false }
);

type Occupant = {
  firstName: string;
  lastName: string;
};

type DocumentViewerProps = {
  content: TemplateContent;
  className?: string;
  adultOccupants?: Occupant[];
  onSigningStatusChange?: (isComplete: boolean, signedPages: number, totalPages: number) => void;
  leaseId?: string; // Add leaseId to fetch landlord signature
  tenantToken?: string; // Add tenant invitation token for authentication
};

interface TempSignaturePosition {
  x: number;
  y: number;
  occupantId?: string;
  occupantName?: string;
}

interface SignatureData {
  dataURL: string;
  page: number;
  type: 'initial' | 'signature';
  posX: number;
  posY: number;
  occupantId?: string;
  occupantName?: string;
}

interface DocumentViewerRef {
  exportToPdf: () => Promise<Blob>;
  generatePDF: () => Promise<Blob>;
  areAllSignaturesComplete: () => boolean;
}

const DocumentViewer = forwardRef<DocumentViewerRef, DocumentViewerProps>(({
  content,
  className,
  adultOccupants = [],
  onSigningStatusChange,
  leaseId,
  tenantToken
}: DocumentViewerProps, ref: React.Ref<any>) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [isMobile, setIsMobile] = useState(false);
  const viewerRef = useRef<HTMLDivElement>(null);
  const signaturePadRef = useRef<any>(null);
  const touchStartX = useRef(0);
  const touchStartY = useRef(0);
  
  // Signature states
  const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);
  const [signatureType, setSignatureType] = useState<'initial' | 'signature'>('initial');
  const [signatures, setSignatures] = useState<SignatureData[]>([]);
  const [tempSignaturePosition, setTempSignaturePosition] = useState<TempSignaturePosition | null>(null);
  const [signaturePlacementMode, setSignaturePlacementMode] = useState(false);
  const [signedPages, setSignedPages] = useState<Set<number>>(new Set());
  const [totalPages, setTotalPages] = useState(0);
  const [landlordSignature, setLandlordSignature] = useState<string | null>(null);
  
  // Fetch landlord signature when leaseId or tenantToken changes
  useEffect(() => {
    const fetchLandlordSignature = async () => {
      console.log('Attempting to fetch landlord signature for leaseId:', leaseId);
      
      if (!leaseId) {
        console.log('No leaseId provided, skipping signature fetch');
        return;
      }
      
      try {
        console.log('Calling getLandlordSignatureByLease with leaseId:', leaseId, 'and token:', tenantToken || 'none');
        // Pass the tenantToken if available (for onboarding flow), otherwise pass empty string
        const signatureUrl = await getLandlordSignatureByLease(leaseId, tenantToken || '');
        console.log('Received signature URL:', signatureUrl);
        
        if (signatureUrl) {
          console.log('Setting landlord signature URL:', signatureUrl);
          setLandlordSignature(signatureUrl);
        } else {
          console.log('No signature URL found');
        }
      } catch (error) {
        console.error('Error fetching landlord signature:', error);
      }
    };

    fetchLandlordSignature();
  }, [leaseId, tenantToken]);

  // Notify parent component about signing status changes
  useEffect(() => {
    if (onSigningStatusChange) {
      onSigningStatusChange(signedPages.size >= totalPages && totalPages > 0, signedPages.size, totalPages);
    }
  }, [signedPages, totalPages, onSigningStatusChange]);

  // Check for mobile on component mount and window resize
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  if (!content?.content?.length) {
    return <div className="text-muted-foreground">No content available</div>;
  }

  // Function to render content that may have HTML formatting
  const renderFormattedContent = (item: any) => {
    // If item has formatting and formattedContent is available, use it
    if (item.hasFormatting && item.formattedContent) {
      // Use a div to safely contain any block-level elements
      return <div className="formatted-content" dangerouslySetInnerHTML={{ __html: item.formattedContent }} />;
    }
    
    // Otherwise render the text with variable markup if present
    if (item.variables && item.variables.length > 0) {
      let text = item.text || '';
      // Replace variable placeholders with styled spans
      item.variables.forEach((varName: string) => {
        const varRegex = new RegExp(`\{\{${varName}\}\}`, 'g');
        text = text.replace(
          varRegex, 
          `<span data-type="variable" class="variable-tag" id="${varName}">${varName}</span>`
        );
      });
      // Use a div to safely contain any block-level elements
      return <div className="formatted-content" dangerouslySetInnerHTML={{ __html: text }} />;
    }
    
    // For plain text, use a span to prevent invalid nesting
    return <span>{item.text}</span>;
  };

  // Log when adult occupants are detected
  useEffect(() => {
    if (adultOccupants.length > 0) {
      console.log('Adult occupants detected:', adultOccupants);
    }
  }, [adultOccupants]);


  // Helper function to create PDF-safe styles (no oklch, etc.)
  const createPdfSafeStyles = () => {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      * {
        color: #000000 !important;
        background-color: #ffffff !important;
        border-color: #000000 !important;
        box-shadow: none !important;
        text-shadow: none !important;
      }
      [data-signature] {
        display: block;
        position: absolute;
        z-index: 100;
      }
    `;
    return styleElement;
  };

  
  // Handle PDF download or generation with signatures
  const handleDownloadPDF = async (noDownload?: boolean) => {
    try {
      // Create loading indicator
      const loadingDiv = document.createElement('div');
      loadingDiv.style.position = 'fixed';
      loadingDiv.style.top = '20px';
      loadingDiv.style.left = '50%';
      loadingDiv.style.transform = 'translateX(-50%)';
      loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      loadingDiv.style.color = 'white';
      loadingDiv.style.padding = '10px 20px';
      loadingDiv.style.borderRadius = '4px';
      loadingDiv.style.zIndex = '9999';
      loadingDiv.textContent = noDownload ? 'Preparing document... This may take a moment' : 'Preparing PDF... This may take a moment';
      document.body.appendChild(loadingDiv);
      
      // Small delay to ensure loading indicator appears
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Get all page elements
      const pageElements = document.querySelectorAll('.document-page');
      const totalPages = pageElements.length;
      
      if (pageElements.length === 0) {
        throw new Error('No document pages found');
      }

      // Document dimensions (8.5 x 11 inches)
      const INCH_TO_MM = 25.4;
      const PAGE_WIDTH_MM = 8.5 * INCH_TO_MM;  // 8.5 inches in mm
      const PAGE_HEIGHT_MM = 11 * INCH_TO_MM;  // 11 inches in mm
      
      // Get the margins from the component constants
      const MARGIN_TOP = MARGIN_TOP_MM;
      const MARGIN_RIGHT = MARGIN_RIGHT_MM;
      const MARGIN_BOTTOM = MARGIN_BOTTOM_MM;
      const MARGIN_LEFT = MARGIN_LEFT_MM;
      
      // Calculate content dimensions
      const contentWidthMM = PAGE_WIDTH_MM - MARGIN_LEFT - MARGIN_RIGHT;
      const contentHeightMM = PAGE_HEIGHT_MM - MARGIN_TOP - MARGIN_BOTTOM;
      
      // Create a new PDF instance with the correct page size
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: [PAGE_WIDTH_MM, PAGE_HEIGHT_MM],
        compress: true,
        hotfixes: ['px_scaling']
      });
      
      // Create rendering container with the same dimensions as the document page
      const renderContainer = document.createElement('div');
      renderContainer.style.position = 'absolute';
      renderContainer.style.left = '-9999px';
      renderContainer.style.top = '0';
      renderContainer.style.width = `${PAGE_WIDTH_MM}mm`;
      renderContainer.style.height = `${PAGE_HEIGHT_MM}mm`;
      renderContainer.style.backgroundColor = 'white';
      renderContainer.style.overflow = 'hidden';
      renderContainer.style.boxSizing = 'border-box';
      document.body.appendChild(renderContainer);

      // Wait for fonts to be ready
      await document.fonts.ready;

      // Create a function to ensure images are loaded
      const ensureImagesLoaded = (element: HTMLElement): Promise<void> => {
        return new Promise((resolve) => {
          const images = element.querySelectorAll('img');
          let loadedCount = 0;
          const totalImages = images.length;

          if (totalImages === 0) {
            resolve();
            return;
          }

          const imageLoaded = () => {
            loadedCount++;
            if (loadedCount >= totalImages) {
              resolve();
            }
          };

          images.forEach((img) => {
            if (img.complete) {
              loadedCount++;
            } else {
              img.onload = imageLoaded;
              img.onerror = imageLoaded;
            }
          });

          // In case all images were already loaded
          if (loadedCount >= totalImages) {
            resolve();
          }
        });
      };

      // Make all pages visible temporarily (but hidden) so we can clone them properly
      pageElements.forEach((page) => {
        (page as HTMLElement).style.display = 'block';
        (page as HTMLElement).classList.remove('hidden');
        (page as HTMLElement).style.transform = 'none';
      });

      // Process each page
      for (let i = 0; i < totalPages; i++) {
        // Update loading indicator
        loadingDiv.textContent = `Preparing PDF... (${i + 1}/${totalPages})`;

        const pageElement = pageElements[i] as HTMLElement;

        // Clear render container
        renderContainer.innerHTML = '';

        // Start with a fresh A4 page wrapper
        const a4Wrapper = document.createElement('div');
        a4Wrapper.id = 'a4-wrapper';
        a4Wrapper.style.width = '210mm';
        a4Wrapper.style.minHeight = '297mm';
        a4Wrapper.style.position = 'relative';
        a4Wrapper.style.backgroundColor = 'white';
        a4Wrapper.style.overflow = 'visible';
        a4Wrapper.style.padding = `${MARGIN_TOP}mm ${MARGIN_RIGHT}mm ${MARGIN_BOTTOM}mm ${MARGIN_LEFT}mm`;
        a4Wrapper.style.boxSizing = 'border-box';
        a4Wrapper.style.display = 'block';

        // Clone the page into the A4 wrapper
        const clone = pageElement.cloneNode(true) as HTMLElement;
        clone.style.transform = 'none'; // Remove any transforms
        clone.style.width = '100%';
        clone.style.height = 'auto'; // Allow content to determine height
        clone.style.padding = '0';
        clone.style.margin = '0';
        clone.style.boxSizing = 'border-box';
        clone.style.display = 'block';
        clone.classList.remove('hidden');

        // Hide page number
        const pageNumberElement = clone.querySelector('.page-number');
        if (pageNumberElement) {
          (pageNumberElement as HTMLElement).style.display = 'none';
        }
        
        // We don't need to handle signatures here as they're already rendered in the document pages
        // This prevents duplicate signatures in the generated PDF
        const pageNumber = i + 1;

        // Apply PDF-safe styles
        renderContainer.appendChild(createPdfSafeStyles());

        // Add the cloned page to A4 wrapper
        a4Wrapper.appendChild(clone);


        // Add the A4 wrapper to render container
        renderContainer.appendChild(a4Wrapper);


        // No longer adding signatures here - they're already in the document
        // This prevents duplicate signatures in the generated PDF

        // Wait for all images to load
        await ensureImagesLoaded(renderContainer);

        // Wait a bit to ensure everything is rendered
        await new Promise(resolve => setTimeout(resolve, 200));

        try {
          // Calculate dimensions in pixels (96 DPI = 3.78 px/mm)
          const PX_PER_MM = 3.78;
          const pageWidthPx = PAGE_WIDTH_MM * PX_PER_MM;
          
          // Get the actual content height
          const contentHeightPx = a4Wrapper.scrollHeight;
          
          // Create a temporary wrapper to measure content
          const tempWrapper = document.createElement('div');
          tempWrapper.style.position = 'absolute';
          tempWrapper.style.left = '-9999px';
          tempWrapper.style.top = '0';
          tempWrapper.style.width = `${PAGE_WIDTH_MM}mm`;
          tempWrapper.style.overflow = 'visible';
          document.body.appendChild(tempWrapper);
          
          // Clone the content to measure its true height
          const contentClone = a4Wrapper.cloneNode(true) as HTMLElement;
          contentClone.style.position = 'static';
          contentClone.style.height = 'auto';
          contentClone.style.overflow = 'visible';
          tempWrapper.appendChild(contentClone);
          
          const measuredHeight = contentClone.scrollHeight;
          document.body.removeChild(tempWrapper);
          
          // Calculate the scale to fit content within the page
          const contentScale = Math.min(1, ((PAGE_WIDTH_MM - 20) * PX_PER_MM * 1.2) / pageWidthPx); // 10mm margin on each side
          
          // Generate canvas with the exact dimensions needed
          const canvas = await html2canvas(a4Wrapper, {
            scale: contentScale,
            useCORS: true,
            allowTaint: true,
            logging: true,
            backgroundColor: '#FFFFFF',
            width: pageWidthPx,
            height: measuredHeight,
            windowWidth: pageWidthPx,
            windowHeight: measuredHeight,
            scrollX: 0,
            scrollY: 0,
            x: 0,
            y: 0,
            dpi: 96,
            letterRendering: true,
            removeContainer: false,
            onclone: (clonedDoc: Document) => {
              const clonedA4Wrapper = clonedDoc.getElementById('a4-wrapper');
              if (clonedA4Wrapper) {
                const wrapper = clonedA4Wrapper as HTMLElement;
                wrapper.style.width = `${PAGE_WIDTH_MM}mm`;
                wrapper.style.minHeight = 'auto';
                wrapper.style.height = 'auto';
                wrapper.style.overflow = 'visible';
                wrapper.style.display = 'block';
                wrapper.style.position = 'static';
                wrapper.style.padding = '0';
                wrapper.style.margin = '0';
              }
            }
          } as any);

          // Get image data with good quality (0.9 is a good balance between quality and file size)
          const imgData = canvas.toDataURL('image/jpeg', 0.9);

          // Add new page for all but the first page
          if (i > 0) {
            pdf.addPage([PAGE_WIDTH_MM, PAGE_HEIGHT_MM], 'portrait');
          }

          // Calculate image dimensions in mm
          const imgWidthMm = canvas.width / 3.78;
          const imgHeightMm = canvas.height / 3.78;
          
          // Calculate margins (10mm on each side)
          const margin = 10; // 10mm margin
          
          // Calculate available space after margins
          const availableWidth = PAGE_WIDTH_MM - (2 * margin);
          const availableHeight = PAGE_HEIGHT_MM - (2 * margin);
          
          // Calculate scale to fit within margins while maintaining aspect ratio
          const scaleX = availableWidth / imgWidthMm;
          const scaleY = availableHeight / imgHeightMm;
          const scale = Math.min(scaleX, scaleY, 1); // Don't scale up
          
          // Calculate final dimensions with scale applied
          const finalWidth = imgWidthMm * scale;
          const finalHeight = imgHeightMm * scale;
          
          // Center the image on the page with margins
          const xOffset = (PAGE_WIDTH_MM - finalWidth) / 2;
          const yOffset = (PAGE_HEIGHT_MM - finalHeight) / 2;
          
          // Add the image with margins
          pdf.addImage(imgData, 'JPEG', xOffset, yOffset, finalWidth, finalHeight);

        } catch (error) {
          console.error(`Error processing page ${i + 1}:`, error);
          throw new Error(`Failed to process page ${i + 1}. Please try again.`);
        }
      }

      // Remove the render container and loading indicator
      document.body.removeChild(renderContainer);
      document.body.removeChild(loadingDiv);

      // Restore page visibility to original state (only show current page)
      pageElements.forEach((page, idx) => {
        const pageNumber = idx + 1;
        const isVisiblePage = pageNumber === currentPage;

        const pageElement = page as HTMLElement;
        pageElement.style.display = isVisiblePage ? 'block' : 'none';
        if (!isVisiblePage) {
          pageElement.classList.add('hidden');
        }
        pageElement.style.transform = `scale(${zoomLevel})`;
      });

      // Either save the PDF or return the blob
      if (noDownload) {
        // Return blob for external upload
        const pdfBlob = pdf.output('blob');
        return pdfBlob;
      } else {
        // Download the PDF in browser
        pdf.save('document-with-signatures.pdf');
      }

    } catch (error) {
      console.error('Error generating PDF:', error);
      if (!noDownload) {
        alert('Error generating PDF. Please try again.');
      }
      // Remove loading indicator if it exists
      const loadingDiv = document.querySelector('div[style*="position: fixed"][style*="zIndex: 9999"]');
      if (loadingDiv && loadingDiv.parentNode) {
        loadingDiv.parentNode.removeChild(loadingDiv);
      }
      throw error; // Re-throw to allow caller to handle error
    }
  };

  // Handle zoom in/out
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.1, 2)); // Max zoom: 200%
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.1, 0.5)); // Min zoom: 50%
  };

  // Handle touch events for swipe navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
    touchStartY.current = e.touches[0].clientY;
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStartX.current || !touchStartY.current) return;

    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    const diffX = touchStartX.current - touchEndX;
    const diffY = touchStartY.current - touchEndY;

    // Only consider horizontal swipes with minimal vertical movement
    if (Math.abs(diffX) > 50 && Math.abs(diffY) < 100) {
      if (diffX > 0) {
        // Swipe left - go to next page
        setCurrentPage(p => Math.min(pages.length, p + 1));
      } else {
        // Swipe right - go to previous page
        setCurrentPage(p => Math.max(1, p - 1));
      }
    }

    // Reset touch coordinates
    touchStartX.current = 0;
    touchStartY.current = 0;
  };

  // Signature handling functions
  const openSignatureModal = (type: 'initial' | 'signature') => {
    setSignatureType(type);
    setIsSignatureModalOpen(true);
    // Prevent scrolling when signature modal is open on mobile
    if (isMobile) {
      document.body.style.overflow = 'hidden';
    }
  };

  const closeSignatureModal = () => {
    setIsSignatureModalOpen(false);
    if (isMobile) {
      document.body.style.overflow = 'auto';
    }
  };

  const clearSignature = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
    }
  };

  const saveSignature = () => {
    if (signaturePadRef.current && !signaturePadRef.current.isEmpty()) {
      const dataURL = signaturePadRef.current.toDataURL();

      if (tempSignaturePosition) {
        setSignatures(prev => {
          // If this is an initial, we'll add it to all pages
          const isInitial = signatureType === 'initial';
          const pagesToSign = isInitial ? Array.from({ length: totalPages }, (_, i) => i + 1) : [currentPage];
          
          // Create new signatures for each page
          const newSignatures = pagesToSign.map(page => ({
            dataURL,
            page,
            type: signatureType,
            posX: tempSignaturePosition.x,
            posY: tempSignaturePosition.y,
            occupantId: tempSignaturePosition.occupantId,
            occupantName: tempSignaturePosition.occupantName
          }));

          // Filter out existing signatures of the same type and occupant (if any)
          const filteredSignatures = prev.filter(sig => 
            !(sig.type === signatureType && 
              (tempSignaturePosition.occupantId 
                ? sig.occupantId === tempSignaturePosition.occupantId 
                : sig.page === currentPage))
          );
          
          const updatedSignatures = [...filteredSignatures, ...newSignatures];
          
          // Update signed pages set
          const pagesWithSignatures = new Set<number>();
          updatedSignatures.forEach(sig => {
            pagesWithSignatures.add(sig.page);
          });
          setSignedPages(pagesWithSignatures);
          
          return updatedSignatures;
        });
        
        setTempSignaturePosition(null);
        closeSignatureModal();
        setSignaturePlacementMode(false);

        // Clear the signature pad for next use
        signaturePadRef.current.clear();
      }
    } else if (isMobile) {
      // On mobile, show a more noticeable error if trying to save empty signature
      alert('Please provide a signature before saving');
    }
  };

  const handleDocumentClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!signaturePlacementMode) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / zoomLevel);
    const y = ((e.clientY - rect.top) / zoomLevel);

    setTempSignaturePosition({ x, y });
    openSignatureModal(signatureType);
  }, [signaturePlacementMode, signatureType, zoomLevel]);

  const startInitialPlacement = () => {
    setSignatureType('initial');
    setSignaturePlacementMode(true);
  };

  const startSignaturePlacement = () => {
    setSignatureType('signature');
    setSignaturePlacementMode(true);
  };

  const removeSignature = (index: number) => {
    setSignatures(prev => {
      const newSignatures = prev.filter((_, i) => i !== index);
      // Update signed pages set when signatures are removed
      const pagesWithSignatures = new Set<number>();
      newSignatures.forEach(sig => {
        pagesWithSignatures.add(sig.page);
      });
      setSignedPages(pagesWithSignatures);
      return newSignatures;
    });
  };

  // A4 page dimensions in mm
  const A4_WIDTH_MM = 210;
  const A4_HEIGHT_MM = 297;
  
  // Margins in mm (same values used in PDF generation)
  const MARGIN_TOP_MM = 20;
  const MARGIN_RIGHT_MM = 20;
  const MARGIN_BOTTOM_MM = 20;
  const MARGIN_LEFT_MM = 20;
  
  // Convert mm to pixels at 96 DPI (standard screen resolution)
  const MM_TO_PX = 96 / 25.4;
  
  // Calculate pixel dimensions
  const A4_WIDTH_PX = A4_WIDTH_MM * MM_TO_PX;
  const A4_HEIGHT_PX = A4_HEIGHT_MM * MM_TO_PX;
  
  // Calculate margin pixels
  const MARGIN_TOP_PX = MARGIN_TOP_MM * MM_TO_PX;
  const MARGIN_RIGHT_PX = MARGIN_RIGHT_MM * MM_TO_PX;
  const MARGIN_BOTTOM_PX = MARGIN_BOTTOM_MM * MM_TO_PX;
  const MARGIN_LEFT_PX = MARGIN_LEFT_MM * MM_TO_PX;
  
  // Content dimensions (with margins)
  const CONTENT_WIDTH_PX = A4_WIDTH_PX - MARGIN_LEFT_PX - MARGIN_RIGHT_PX;
  const CONTENT_HEIGHT_PX = A4_HEIGHT_PX - MARGIN_TOP_PX - MARGIN_BOTTOM_PX;

  // Group content into pages based on actual rendered height
  const groupContentIntoPages = (): TemplateContentItem[][] => {
    const pages: TemplateContentItem[][] = [];
    let currentPage: TemplateContentItem[] = [];

    // Constants for page dimensions (in pixels at 96 DPI)
    const A4_WIDTH_MM = 210;
    const A4_HEIGHT_MM = 297;
    const MM_TO_PX = 96 / 25.4; // 96 DPI / 25.4 mm per inch
    const PAGE_HEIGHT_PX = A4_HEIGHT_MM * MM_TO_PX;
    const PAGE_PADDING_TOP_PX = 20 * MM_TO_PX; // 20mm top padding
    const PAGE_PADDING_BOTTOM_PX = 30 * MM_TO_PX; // 30mm bottom padding
    const AVAILABLE_HEIGHT = PAGE_HEIGHT_PX - PAGE_PADDING_TOP_PX - PAGE_PADDING_BOTTOM_PX;

    // Create a temporary hidden container to measure content height
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.visibility = 'hidden';
    tempContainer.style.width = `${A4_WIDTH_MM * MM_TO_PX}px`;
    tempContainer.style.padding = `0 ${20 * MM_TO_PX}px`; // 20mm horizontal padding
    document.body.appendChild(tempContainer);

    try {
      let currentHeight = 0;

      for (const item of content.content) {
        // Create a temporary element to measure height
        const tempItem = document.createElement('div');

        // Apply similar styles as in the actual render
        tempItem.style.marginBottom = '1rem';
        if (item.type === 'heading1') {
          tempItem.style.fontSize = '1.5rem';
          tempItem.style.fontWeight = 'bold';
          tempItem.style.marginTop = '1.5rem';
          tempItem.style.marginBottom = '1rem';
        } else if (item.type === 'heading2') {
          tempItem.style.fontSize = '1.25rem';
          tempItem.style.fontWeight = '600';
          tempItem.style.marginTop = '1.25rem';
          tempItem.style.marginBottom = '0.75rem';
        } else {
          tempItem.style.fontSize = '1rem';
          tempItem.style.lineHeight = '1.5';
        }

        // Set content (simplified for measurement)
        tempItem.textContent = item.text || '';
        tempContainer.appendChild(tempItem);

        // Get the rendered height
        const itemHeight = tempItem.offsetHeight;
        tempContainer.removeChild(tempItem);

        // If adding this item would exceed the page height, start a new page
        if (currentHeight + itemHeight > AVAILABLE_HEIGHT && currentPage.length > 0) {
          pages.push([...currentPage]);
          currentPage = [];
          currentHeight = 0;
        }

        // Add item to current page
        currentPage.push(item);
        currentHeight += itemHeight;

        // Force a new page for major sections (heading1)
        if (item.type === 'heading1') {
          if (currentPage.length > 1) {
            // If there's content before this heading, move it to a new page
            const lastItem = currentPage.pop();
            if (currentPage.length > 0) {
              pages.push([...currentPage]);
            }
            currentPage = lastItem ? [lastItem] : [];
            currentHeight = itemHeight;
          }
        }
      }

      // Add the last page if it has content
      if (currentPage.length > 0) {
        pages.push(currentPage);
      }
    } finally {
      // Clean up
      document.body.removeChild(tempContainer);
    }

    return pages;
  };

  const pages = groupContentIntoPages();
  
  // Check if all required signatures on the last page are complete
  const areAllSignaturesComplete = useCallback(() => {
    const lastPage = pages.length;
    console.log('Checking signatures for page', lastPage);
    console.log('Current signatures:', signatures);
    console.log('Adult occupants:', adultOccupants);
    
    // First check if the last page is signed (for initials)
    if (!signedPages.has(lastPage)) {
      console.log('Last page not signed yet');
      return false;
    }
    
    // Check for tenant signature (always required)
    const hasTenantSignature = signatures.some(sig => 
      sig.page === lastPage && 
      sig.type === 'signature' && 
      !sig.occupantId // Tenant signature has no occupantId
    );
    
    console.log('Has tenant signature:', hasTenantSignature);
    
    if (!hasTenantSignature) {
      console.log('Missing tenant signature');
      return false;
    }
    
    // Check for all adult occupant signatures if there are any
    if (adultOccupants && adultOccupants.length > 0) {
      console.log('Checking', adultOccupants.length, 'adult occupants');
      const allOccupantsSigned = adultOccupants.every(occupant => {
        // Match the format used when creating the signature
        const fullName = `${occupant.firstName} ${occupant.lastName}`.toLowerCase();
        const hasSigned = signatures.some(sig => 
          sig.page === lastPage && 
          sig.type === 'signature' && 
          sig.occupantName && 
          sig.occupantName.toLowerCase() === fullName
        );
        console.log(`Checking occupant ${fullName} - has${hasSigned ? '' : ' not'} signed`);
        console.log('Matching signatures:', signatures.filter(sig => 
          sig.page === lastPage && 
          sig.type === 'signature' &&
          sig.occupantName
        ));
        return hasSigned;
      });
      
      if (!allOccupantsSigned) {
        console.log('Not all occupants have signed');
        return false;
      }
    } else {
      console.log('No adult occupants to check');
    }
    
    console.log('All required signatures are present');
    return true;
  }, [adultOccupants, signatures, signedPages, pages.length]);

  useEffect(() => {
    setTotalPages(pages.length);
  }, [pages.length]);

  // Handle pinch-to-zoom
  const handleWheel = useCallback((e: WheelEvent) => {
    if (e.ctrlKey) {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      setZoomLevel(prev => Math.min(Math.max(0.5, prev + delta), 2));
    }
  }, []);

  // Add wheel event for pinch-to-zoom on trackpads
  useEffect(() => {
    const viewer = viewerRef.current;
    if (viewer) {
      viewer.addEventListener('wheel', handleWheel);
      return () => viewer.removeEventListener('wheel', handleWheel);
    }
  }, [handleWheel]);

  // Expose methods to parent component via forwardRef
  useImperativeHandle(ref, () => ({
    exportToPdf: handleDownloadPDF, // Legacy method (downloads the PDF)
    generatePDF: () => handleDownloadPDF(true), // New method (returns blob without download)
    areAllSignaturesComplete: () => areAllSignaturesComplete()
  }), [handleDownloadPDF, areAllSignaturesComplete]);

return (
  <div 
    ref={viewerRef}
    className="document-viewer flex flex-col items-center" 
    style={{ width: '100%', overflow: 'auto' }}
    onTouchStart={handleTouchStart}
    onTouchEnd={handleTouchEnd}
  >
    {/* Document content rendering is handled below in the main pages section */}

    <style jsx global>{`
      .document-page {
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
        padding: 1rem;
        margin: 0.5rem auto 1rem;
        min-height: auto;
        width: 100%;
        max-width: 100%;
        position: relative;
        transition: transform 0.2s ease;
        overflow: hidden;
        touch-action: pan-y;
      }
      
      @media (min-width: 768px) {
        .document-page {
          padding: 2rem;
          margin: 1rem auto 2rem;
          min-height: 11in;
          width: 8.5in;
          max-width: 100%;
          box-sizing: border-box;
        }
      }
      
      .document-controls {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: white;
        border-bottom: 1px solid #e2e8f0;
        padding: 0.5rem;
        margin-bottom: 1rem;
      }

      .zoom-level {
        font-size: 0.875rem;
        color: #64748b;
      }
      
      .page-number {
        text-align: center;
        font-size: 0.75rem;
        color: #64748b;
        position: absolute;
        bottom: 0.5rem;
        width: 100%;
        left: 0;
      }
      
      @media print {
        .document-controls {
          display: none;
        }
        
        .document-page {
          box-shadow: none;
          margin: 0;
          padding: 0.5in;
          page-break-after: always;
          transform: none !important;
        }

        .hidden {
          display: block !important;
        }
      }
    `}</style>

      {/* Document controls - Responsive for mobile */}
      <div className="document-controls flex flex-col md:flex-row justify-between items-stretch md:items-center print:hidden gap-2 p-2">
        {/* Page navigation - Top on mobile, left on desktop */}
        <div className="flex items-center justify-between w-full">
          {/* Page counter - positioned absolutely at the bottom center */}
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white/80 backdrop-blur-sm px-4 py-1.5 rounded-full shadow-sm border border-gray-200 flex items-center gap-1">
            {currentPage === pages.length && !areAllSignaturesComplete() && (
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-red-500 whitespace-nowrap">
                Please complete all signatures
              </div>
            )}
            {pages.length > 1 && (
              <>
                <button 
                  onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                  className="p-1 rounded-full disabled:opacity-30 hover:bg-gray-100 flex-shrink-0"
                  aria-label="Previous page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <span className="text-xs text-gray-600 whitespace-nowrap px-1">
                  {currentPage} / {pages.length}
                </span>
                <button 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // If we're on the last page and signatures aren't complete, do nothing
                    if (currentPage === pages.length && !areAllSignaturesComplete()) {
                      return;
                    }
                    
                    // Only proceed if not already on the last page
                    if (currentPage < pages.length) {
                      setCurrentPage(p => Math.min(pages.length, p + 1));
                    }
                  }}
                  disabled={currentPage === pages.length && !areAllSignaturesComplete()}
                  className={cn(
                    "p-1 rounded-full flex-shrink-0 select-none",
                    currentPage === pages.length && !areAllSignaturesComplete() 
                      ? "opacity-30 cursor-not-allowed" 
                      : "hover:bg-gray-100 disabled:opacity-30"
                  )}
                  aria-label={currentPage === pages.length && !areAllSignaturesComplete() 
                    ? "Complete all signatures to continue" 
                    : "Next page"
                  }
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </>
            )}
          </div>
        </div>
        
        {/* Zoom, signature, and action controls */}
        <div className="flex items-center justify-between w-full md:w-auto gap-2">
          {/* Zoom controls */}
          <div className="flex items-center bg-gray-50 rounded-full p-1">
            <button 
              onClick={handleZoomOut}
              disabled={zoomLevel <= 0.5}
              className="p-1.5 rounded-full disabled:opacity-30 hover:bg-white"
              aria-label="Zoom out"
            >
              <ZoomOut className="h-4 w-4" />
            </button>
            <span className="zoom-level text-xs w-12 text-center">
              {Math.round(zoomLevel * 100)}%
            </span>
            <button 
              onClick={handleZoomIn}
              disabled={zoomLevel >= 2}
              className="p-1.5 rounded-full disabled:opacity-30 hover:bg-white"
              aria-label="Zoom in"
            >
              <ZoomIn className="h-4 w-4" />
            </button>
          </div>
          
          {/* Download button */}
          <div className="flex items-center gap-1 bg-gray-50 rounded-full p-1">
            <button 
              onClick={() => handleDownloadPDF()}
              className="p-1.5 rounded-full hover:bg-white"
              aria-label="Download PDF"
              title="Download PDF with signatures"
            >
              <Download className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {signaturePlacementMode && (
          <div className="absolute bottom-16 left-0 right-0 bg-yellow-50 text-yellow-800 text-sm p-2 text-center">
            Tap where you want to place the {signatureType === 'initial' ? 'initial' : 'signature'}
          </div>
        )}
      </div>
      
      {/* Signature Modal - Enhanced for mobile */}
      {isSignatureModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] flex flex-col" style={{ height: isMobile ? '80%' : 'auto' }}>
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium">{signatureType === 'initial' ? 'Add Initial' : 'Add Signature'}</h3>
              <button 
                onClick={closeSignatureModal}
                className="text-gray-400 hover:text-gray-600 text-2xl"
                aria-label="Close"
              >
                &times;
              </button>
            </div>
            
            <div className="p-4 flex-1 flex flex-col">
              <div className="border border-gray-300 rounded-md mb-4 flex-1 flex flex-col">
                <div className="p-2 text-xs text-gray-500 bg-gray-50 border-b">
                  {signatureType === 'initial' ? (
                    <div className="space-y-1">
                      <p>Draw your initial below</p>
                      <p className="text-amber-700 font-medium">
                        Note: Your initial will be added to all pages of the document
                      </p>
                    </div>
                  ) : (
                    'Sign your name'
                  )}
                </div>
                <div className="flex-1 relative">
                  {typeof window !== 'undefined' && (
                    <div style={{ width: '100%', height: isMobile ? '200px' : '160px' }}>
                      <SignaturePad 
                        ref={signaturePadRef}
                        penColor="black"
                        canvasProps={{
                          className: 'signature-canvas w-full h-full',
                          style: { 
                            width: '100%', 
                            height: '100%',
                            touchAction: 'none' // Prevent scrolling while signing
                          }
                        }}
                        clearOnResize={false}
                      />
                    </div>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mt-2">
                <button 
                  onClick={clearSignature}
                  className="px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center justify-center gap-2 text-sm"
                >
                  <Trash2 className="h-4 w-4" /> Clear
                </button>
                <button 
                  onClick={saveSignature}
                  className="px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center gap-2 text-sm"
                >
                  <Save className="h-4 w-4" /> Save {signatureType === 'initial' ? 'Initial' : 'Signature'}
                </button>
              </div>
              
              {isMobile && (
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Use your finger to sign in the box above
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      
      {/* Document pages */}
      <div className="w-full flex-1 flex flex-col items-center p-4 overflow-auto">
        {/* Status bar showing signature progress */}
        <div className="w-full max-w-4xl mb-4 bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-600 text-center">
            {signedPages.size} of {totalPages} pages signed
          </div>
        </div>
        {pages.map((pageItems, idx) => {
          const pageNumber = idx + 1;
          const isCurrentPage = pageNumber === currentPage;

          return (
            <div
              key={`page-${idx}`}
              data-page={pageNumber}
              className={cn(
                'document-page bg-white shadow-lg mb-8 print:shadow-none print:mb-0',
                isCurrentPage ? 'block' : 'hidden',
                'transform origin-top' // Ensure zoom scales from the top
              )}
              style={{
                width: '8.5in',
                minHeight: '11in',
                padding: `${MARGIN_TOP_MM}mm ${MARGIN_RIGHT_MM}mm ${MARGIN_BOTTOM_MM}mm ${MARGIN_LEFT_MM}mm`,
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'top center',
                boxSizing: 'border-box',
                margin: '0 auto',
                position: 'relative',
                backgroundColor: 'white',
                breakAfter: 'page',
                breakInside: 'avoid',
                overflow: 'hidden',
                display: isCurrentPage ? 'block' : 'none'
              }}
              onClick={handleDocumentClick}
            >
              {pageItems.map((item, index) => {
                const key = `${pageNumber}-${item.type}-${index}`;
                const textAlign = item.textAlign || 'left';
                const style = {
                  textAlign,
                  marginBottom: '0.75rem',
                  lineHeight: 1.6,
                };

                // Handle different content types
                switch (item.type) {
                  case 'heading1':
                    return (
                      <h1 key={key} style={style} className="text-2xl font-bold mt-6 mb-4">
                        {renderFormattedContent(item)}
                      </h1>
                    );
                  case 'heading2':
                    return (
                      <h2 key={key} style={style} className="text-xl font-semibold mt-5 mb-3">
                        {renderFormattedContent(item)}
                      </h2>
                    );
                  case 'heading3':
                    return (
                      <h3 key={key} style={style} className="text-lg font-medium mt-4 mb-2">
                        {renderFormattedContent(item)}
                      </h3>
                    );
                  case 'bullet':
                    return (
                      <ul key={key} style={style} className="list-disc pl-6 space-y-1">
                        <li>
                          {renderFormattedContent(item)}
                        </li>
                      </ul>
                    );
                  case 'ordered':
                    return (
                      <ol key={key} style={style} className="list-decimal pl-6 space-y-1">
                        <li>
                          {renderFormattedContent(item)}
                        </li>
                      </ol>
                    );
                  case 'paragraph':
                  default:
                    return (
                      <p key={key} style={style} className="mb-4">
                        {renderFormattedContent(item)}
                      </p>
                    );
                }
              })}
              
              {/* Page counter at the bottom center of each page */}
              <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                <span className="text-xs text-gray-500">
                  Page {pageNumber} of {pages.length}
                </span>
              </div>
              
              {/* Initial indicator at the bottom of each page except last */}
              {pageNumber !== pages.length && (
                <div 
                  className="absolute bottom-8 right-8 w-16 h-10 flex flex-col items-center justify-end"
                >
                  <div 
                    className="w-full border-b border-gray-400 h-8 flex items-end justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSignatureType('initial');
                      // Position the initial directly in this element
                      const rect = e.currentTarget.getBoundingClientRect();
                      const pageRect = e.currentTarget.closest('.document-page')?.getBoundingClientRect();
                      
                      if (pageRect) {
                        // Calculate position relative to the page
                        const x = rect.left - pageRect.left + (rect.width / 2) - 20; // Center the initial
                        const y = rect.top - pageRect.top + (rect.height / 2) - 10;
                        
                        setTempSignaturePosition({ x, y });
                        setIsSignatureModalOpen(true);
                      }
                    }}
                  >
                    <span className="text-xs text-gray-500 hover:text-gray-700">Initial here</span>
                  </div>
                  {/* Show initial if it exists for this page */}
                  {(() => {
                    const initial = signatures.find(sig => sig.page === pageNumber && sig.type === 'initial');
                    if (!initial) return null;
                    
                    return (
                      <div 
                        className="absolute inset-0 flex items-center justify-center group"
                      >
                        <img 
                          src={initial.dataURL} 
                          alt="Initial"
                          className="max-h-6"
                        />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeSignature(signatures.findIndex(s => s === initial));
                          }}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity print:hidden text-xs"
                        >
                          ×
                        </button>
                      </div>
                    );
                  })()}
                </div>
              )}
              
              {/* Signature blocks on the last page */}
              {pageNumber === pages.length && (
                <div className="mt-16 mb-8 space-y-8">
                  {/* Landlord Signature Section */}
                  {landlordSignature && (
                    <div className="mt-16 pt-8 border-t border-gray-200">
                      <div className="text-sm text-gray-500 mb-2">Landlord Signature:</div>
                      <div className="flex flex-col">
                        <img 
                          src={landlordSignature} 
                          alt="Landlord Signature" 
                          className="h-16 w-48 object-contain mb-2"
                        />
                        <div className="border-t border-gray-400 w-48 pt-1">
                          <div className="text-xs text-gray-500">Date: {new Date().toLocaleDateString()}</div>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Tenant Signature */}
                  <div className="flex flex-col">
                    <div 
                      className="border-t border-gray-300 mt-2 pt-1 pb-6 cursor-pointer hover:bg-gray-50 transition-colors relative"
                      onClick={(e) => {
                        if (!signatures.some(sig => sig.page === pageNumber && sig.type === 'signature')) {
                          e.stopPropagation();
                          setSignatureType('signature');
                          const rect = e.currentTarget.getBoundingClientRect();
                          const pageRect = e.currentTarget.closest('.document-page')?.getBoundingClientRect();
                          
                          if (pageRect) {
                            setTempSignaturePosition({
                              x: 50, // Position from left
                              y: rect.top - pageRect.top + 20 // Position from top of the signature area
                            });
                            setIsSignatureModalOpen(true);
                          }
                        }
                      }}
                    >
                      <span className="text-sm text-gray-600">Tenant Signature</span>
                      {/* Show signature if it exists */}
                      {signatures
                        .filter(sig => sig.page === pageNumber && sig.type === 'signature' && !sig.occupantId)
                        .map((signature, idx) => (
                          <div 
                            key={`sig-${pageNumber}-${idx}`}
                            className="relative mt-2 group inline-block"
                          >
                            <img 
                              src={signature.dataURL} 
                              alt="Tenant Signature"
                              className="max-h-16"
                            />
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                removeSignature(signatures.findIndex(s => s === signature));
                              }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity print:hidden text-xs"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      {!signatures.some(sig => sig.page === pageNumber && sig.type === 'signature' && !sig.occupantId) && (
                        <div className="text-xs text-gray-400 mt-1">Click to sign</div>
                      )}
                    </div>
                  </div>

                  {/* Adult Occupants Signatures */}
                  {Array.isArray(adultOccupants) && adultOccupants.length > 0 ? (
                    adultOccupants.map((occupant, index) => {
                      const signatureKey = `occupant-${index}`;
                      const hasSigned = signatures.some(sig => 
                        sig.page === pageNumber && 
                        sig.type === 'signature' && 
                        sig.occupantId === signatureKey
                      );
  
                      return (
                        <div key={signatureKey} className="flex flex-col mt-12 mb-4">
                          <h3 className="text-sm font-medium mb-2">Adult Occupant Signature</h3>
                          <div 
                            className="border-t border-gray-500 mt-2 pt-1 pb-6 cursor-pointer hover:bg-gray-50 transition-colors relative"
                            onClick={(e) => {
                              if (!hasSigned) {
                                e.stopPropagation();
                                setSignatureType('signature');
                                const rect = e.currentTarget.getBoundingClientRect();
                                const pageRect = e.currentTarget.closest('.document-page')?.getBoundingClientRect();
                                
                                if (pageRect) {
                                  setTempSignaturePosition({
                                    x: 50,
                                    y: rect.top - pageRect.top + 20,
                                    occupantId: signatureKey,
                                    occupantName: `${occupant.firstName} ${occupant.lastName}`
                                  });
                                  setIsSignatureModalOpen(true);
                                }
                              }
                            }}
                          >
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-semibold text-gray-800">
                                {occupant.firstName} {occupant.lastName} (Occupant)
                              </span>
                            </div>
                            
                            {signatures
                              .filter(sig => 
                                sig.page === pageNumber && 
                                sig.type === 'signature' && 
                                sig.occupantId === signatureKey
                              )
                              .map((signature, idx) => (
                                <div 
                                  key={`${signatureKey}-sig-${idx}`}
                                  className="relative mt-2 group inline-block"
                                >
                                  <img 
                                    src={signature.dataURL} 
                                    alt={`${occupant.firstName} ${occupant.lastName}'s Signature`}
                                    className="max-h-16"
                                  />
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      removeSignature(signatures.findIndex(s => s === signature));
                                    }}
                                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity print:hidden text-xs"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                              
                            {!hasSigned && (
                              <div className="text-xs text-gray-600 mt-1 font-medium">
                                Click here to sign as {occupant.firstName} {occupant.lastName}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-sm text-gray-500 italic mt-4">
                      No adult occupants to sign
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
});

DocumentViewer.displayName = 'DocumentViewer';

export { DocumentViewer };

'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';

export interface Property {
  id: string;
  name: string;
  addressLine1?: string;
  city?: string;
  state?: string;
}

interface PropertySelectProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  properties: Property[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function PropertySelect({
  value,
  onChange,
  properties = [],
  placeholder = 'Select property...',
  className,
  disabled = false,
}: PropertySelectProps) {
  const [open, setOpen] = React.useState(false);

  const selectedProperty = React.useMemo(
    () => properties.find((property) => property.id === value),
    [value, properties]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-full justify-between', className)}
          disabled={disabled}
        >
          {selectedProperty ? (
            <div className="flex-1 min-w-0 text-left overflow-hidden">
              <div className="truncate">{selectedProperty.name}</div>
              {selectedProperty.city && selectedProperty.state && (
                <div className="text-xs text-muted-foreground truncate">
                  {selectedProperty.city}, {selectedProperty.state}
                </div>
              )}
            </div>
          ) : (
            <span className="text-muted-foreground text-left">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-[var(--radix-popover-trigger-width)] max-w-[min(calc(100vw-2rem),25rem)] p-0" 
        align="start"
        sideOffset={8}
        collisionPadding={16}
      >
        <Command>
          <CommandInput placeholder="Search properties..." className="h-10" />
          <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
            No property found
          </CommandEmpty>
          <CommandGroup className="p-0">
            <ScrollArea className="max-h-[min(400px,60vh)] overflow-y-auto">
              <CommandItem
                key="all-properties"
                onSelect={() => {
                  onChange(undefined);
                  setOpen(false);
                }}
                className="flex items-center justify-between px-4 py-3"
              >
                <span className="truncate">All properties</span>
                {!value && <Check className="h-4 w-4 flex-shrink-0 text-primary" />}
              </CommandItem>
              {properties.map((property) => (
                <CommandItem
                  key={property.id}
                  value={property.id}
                  onSelect={(currentValue) => {
                    onChange(currentValue === value ? undefined : currentValue);
                    setOpen(false);
                  }}
                  className="flex items-center justify-between px-4 py-3"
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{property.name}</div>
                    {property.city && property.state && (
                      <div className="text-xs text-muted-foreground truncate">
                        {property.city}, {property.state}
                      </div>
                    )}
                  </div>
                  {value === property.id && (
                    <Check className="h-4 w-4 flex-shrink-0 text-primary ml-2" />
                  )}
                </CommandItem>
              ))}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

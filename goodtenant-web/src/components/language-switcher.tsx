'use client';

import { usePathname, useRouter } from 'next/navigation';
import { locales, type Locale } from '@/types/i18n';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useCallback } from 'react';
import { ChevronDown, Check, Languages } from 'lucide-react';
import { cn } from '@/lib/utils';

const languageConfig: Record<Locale, { name: string; flag: string }> = {
  en: { name: 'English', flag: '🇺🇸' },
  es: { name: 'Español', flag: '🇪🇸' },
};

export default function LanguageSwitcher({
  className,
}: {
  className?: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = (pathname?.split('/')[1] as Locale) || 'en';

  const switchLanguage = useCallback((locale: Locale) => {
    const currentPath = pathname || '/';
    const pathnameWithoutLocale = currentPath.split('/').slice(2).join('/');
    const newPath = `/${locale}/${pathnameWithoutLocale}`;
    
    document.cookie = `NEXT_LOCALE=${locale}; path=/; max-age=31536000; SameSite=Lax`;
    router.push(newPath);
    router.refresh();
  }, [pathname, router]);

  return (
    <div className={cn("relative", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm"
            className={cn(
              "flex items-center gap-1.5 px-3 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",
              className
            )}
            aria-label="Select language"
          >
            <span className="text-base font-emoji">{languageConfig[currentLocale]?.flag}</span>
            <span className="uppercase">{currentLocale}</span>
            <ChevronDown className="h-4 w-4 ml-0.5 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align="end" 
          className="w-40 z-[60] bg-background/95 backdrop-blur-sm"
          sideOffset={8}
        >
          {locales.map((locale) => (
            <DropdownMenuItem
              key={locale}
              onClick={() => switchLanguage(locale)}
              className={cn(
                'flex items-center justify-between px-3 py-2 text-sm cursor-pointer',
                'hover:bg-accent hover:text-accent-foreground',
                'transition-colors duration-150',
                currentLocale === locale && 'font-semibold bg-accent/50'
              )}
            >
              <div className="flex items-center gap-2">
                <span className="text-base font-emoji">{languageConfig[locale]?.flag}</span>
                <span>{languageConfig[locale]?.name}</span>
              </div>
              {currentLocale === locale && (
                <Check className="h-4 w-4 text-foreground/70" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

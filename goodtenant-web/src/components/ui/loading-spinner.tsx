import React from 'react';
import { cn } from '@/lib/utils';
import { Logo } from '../logo';

interface LoadingSpinnerProps {
  className?: string;
  withText?: boolean;
  text?: string;
}

export function LoadingSpinner({ 
  className, 
  withText = true, 
  text = 'Loading...' 
}: LoadingSpinnerProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center gap-4", className)}>
      <div className="relative">
        <div className="absolute inset-0 rounded-full bg-primary/10 animate-ping" />
        <div className="relative">
          <Logo withText={false} className="h-12 w-12" />
        </div>
      </div>
      {withText && (
        <p className="text-muted-foreground text-sm font-medium">{text}</p>
      )}
    </div>
  );
}

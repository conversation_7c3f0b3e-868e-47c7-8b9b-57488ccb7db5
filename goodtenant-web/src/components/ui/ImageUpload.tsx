// src/components/ui/ImageUpload.tsx
'use client';

import { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { X, Plus, Image as ImageIcon, Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

export interface ImageFile {
  id: string;
  url: string;
  file?: File;
  isNew?: boolean;
}

interface ImageUploadProps {
  name: string;
  label: string;
  maxFiles?: number;
  maxSizeMB?: number;
  value?: ImageFile[];
  onChange: (files: ImageFile[]) => void;
  onDelete?: (id: string) => Promise<void>;
  disabled?: boolean;
}

export function ImageUpload({
  name,
  label,
  maxFiles = 10,
  maxSizeMB = 5,
  value = [],
  onChange,
  onDelete,
  disabled = false,
}: ImageUploadProps) {
  const t = useTranslations();
  const [isUploading, setIsUploading] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      const newFiles = acceptedFiles.map((file) => ({
        id: `new-${Math.random().toString(36).substr(2, 9)}`,
        url: URL.createObjectURL(file),
        file,
        isNew: true,
      }));

      onChange([...value, ...newFiles].slice(0, maxFiles));
    },
    [maxFiles, onChange, value]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
    },
    maxSize: maxSizeMB * 1024 * 1024,
    maxFiles,
    disabled: disabled || value.length >= maxFiles,
  });

  const handleRemove = useCallback(
    async (id: string) => {
      if (disabled) return;

      const fileToRemove = value.find((file) => file.id === id);
      if (!fileToRemove) return;

      if (fileToRemove.isNew) {
        URL.revokeObjectURL(fileToRemove.url);
        onChange(value.filter((file) => file.id !== id));
      } else if (onDelete) {
        try {
          setDeletingId(id);
          await onDelete(id);
        } catch (error) {
          console.error('Error deleting file:', error);
        } finally {
          setDeletingId(null);
        }
      }
    },
    [disabled, onChange, onDelete, value]
  );

  useEffect(() => {
    return () => {
      value.forEach((file) => {
        if (file.isNew) {
          URL.revokeObjectURL(file.url);
        }
      });
    };
  }, [value]);

  const canUploadMore = value.length < maxFiles;

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-foreground mb-1">
          {label}
        </label>
        <p className="text-xs text-muted-foreground mb-2">
          {t('maxFiles', { count: maxFiles, size: maxSizeMB })}
        </p>
      </div>

      {value.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {value.map((file) => (
            <div
              key={file.id}
              className="relative group aspect-square rounded-md overflow-hidden border"
            >
              <img
                src={file.url}
                alt={file.file?.name || 'Property image'}
                className="w-full h-full object-cover"
              />
              <button
                type="button"
                onClick={() => handleRemove(file.id)}
                disabled={disabled || deletingId === file.id}
                className={cn(
                  'absolute top-1 right-1 bg-destructive text-destructive-foreground rounded-full p-1',
                  'opacity-0 group-hover:opacity-100 transition-opacity',
                  'disabled:opacity-50 disabled:cursor-not-allowed'
                )}
                title={t('remove')}
              >
                {deletingId === file.id ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <X className="h-3 w-3" />
                )}
              </button>
            </div>
          ))}
        </div>
      )}

      {canUploadMore && (
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
            'hover:border-border-hover',
            isDragActive
              ? 'border-primary bg-accent/50'
              : 'border-border bg-card',
            disabled ? 'opacity-50 cursor-not-allowed' : ''
          )}
        >
          <input {...getInputProps()} name={name} />
          <div className="space-y-2">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
              <Plus className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-sm text-foreground">
              {isDragActive ? t('dropHere') : t('dragAndDrop')}
            </p>
            <p className="text-xs text-muted-foreground">
              {t('fileTypes', { size: maxSizeMB })}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
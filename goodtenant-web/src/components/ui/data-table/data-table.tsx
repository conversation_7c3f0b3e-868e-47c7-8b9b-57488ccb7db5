'use client';

import * as React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Loader2, SearchX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DataTablePagination } from './data-table-pagination';
import { 
  useReactTable, 
  getCoreRowModel, 
  getFilteredRowModel, 
  getPaginationRowModel, 
  getSortedRowModel, 
  ColumnDef, 
  ColumnFiltersState, 
  SortingState, 
  VisibilityState, 
  Row, 
  flexRender,
  RowData,
  TableOptions,
} from '@tanstack/react-table';
import { CustomTableMeta } from './types';

// Extend the type to include our custom properties
declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    locale?: string; // Make locale optional since we provide a default
    onDelete?: (data: TData) => void; // Add onDelete to the interface
  }
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  page: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onSearch: (value: string) => void;
  searchValue?: string;
  emptyState?: {
    title: string;
    description: string;
    action?: {
      label: string;
      onClick: () => void;
    };
    icon?: React.ComponentType<{ className?: string }>;
  };
  onRowClick?: (row: Row<TData>) => void;
  customToolbar?: React.ReactNode;
  locale?: string;
  className?: string;
  mobileCardComponent?: React.ComponentType<{ row: TData }>;
  hideDefaultSearch?: boolean;
  isLoading?: boolean;
  isSearching?: boolean;
  meta?: CustomTableMeta<TData>;
  key?: string | number;
  showViewOptions?: boolean; // Keep for backward compatibility
}

export function DataTable<TData extends Record<string, any> = any, TValue = any>({
  columns,
  data,
  page,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  onSearch,
  searchValue,
  isLoading = false,
  isSearching = false, // New prop with default value


  emptyState,
  onRowClick,
  locale = 'en',
  className = '',
  mobileCardComponent,
  meta = {},
  customToolbar,
  // Pagination is shown by default, but hidden on mobile when using mobile card view
}: DataTableProps<TData, TValue> & {
  customToolbar?: React.ReactNode;
}) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Merge default meta with provided meta, with provided meta taking precedence
  const mergedMeta = {
    locale: locale || 'en',
    ...meta,
  };

  const tableOptions: TableOptions<TData> = {
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex: page - 1, // Convert to 0-based index for table
        pageSize,
      },
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalItems / pageSize),
    meta: mergedMeta,
  };

  const table = useReactTable<TData>({
    ...tableOptions,
    state: {
      ...tableOptions.state,
      pagination: {
        pageIndex: page - 1,
        pageSize,
      },
    },
    pageCount: Math.ceil(totalItems / pageSize),
  });



  // Handle row click
  const handleRowClick = (row: Row<TData>, e: React.MouseEvent) => {
    // Don't trigger row click if clicking on a button, link, or input
    const target = e.target as HTMLElement;
    const isActionElement = 
      target.tagName === 'BUTTON' || 
      target.tagName === 'A' ||
      target.tagName === 'INPUT' ||
      target.closest('button') ||
      target.closest('a') ||
      target.closest('input');

    if (!isActionElement && onRowClick) {
      onRowClick(row);
    }
  };



  // Handle mobile card swipe
  const [isDragging, setIsDragging] = React.useState(false);
  const [startX, setStartX] = React.useState(0);
  const [currentX, setCurrentX] = React.useState(0);
  const [isOpen, setIsOpen] = React.useState(false);
  const [actionsWidth, setActionsWidth] = React.useState(0);
  const actionsRef = React.useRef<HTMLDivElement>(null);

  // Measure actions width once on mount
  React.useEffect(() => {
    if (actionsRef.current) {
      setActionsWidth(actionsRef.current.offsetWidth);
    }
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!onRowClick) return;
    
    setIsDragging(true);
    setStartX(e.clientX);
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    const diff = startX - e.clientX;
    
    if (diff > 0) {
      setCurrentX(Math.max(-actionsWidth, -diff));
    } else {
      setCurrentX(Math.min(0, -diff));
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    
    if (currentX < -actionsWidth / 2) {
      setCurrentX(-actionsWidth);
      setIsOpen(true);
    } else {
      setCurrentX(0);
      setIsOpen(false);
    }
  };

  // Empty state

  const showEmptyState = !isLoading && !isSearching && data.length === 0 && !searchValue;
  const showNoResultsState = !isLoading && !isSearching && data.length === 0 && !!searchValue;


  return (
    <div className={`space-y-4 mx-2 sm:mx-4 md:mx-6 lg:mx-8 ${className}`}>
      {customToolbar}
      
      {/* Main container */}
      <div 
        className="relative"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        {/* Background for the entire area */}
        {mobileCardComponent && (
          <div className="absolute inset-0 bg-muted/5 -z-10" />
        )}
        
        {/* Content container */}
        <div 
          className={cn(
            'relative overflow-hidden',
            'transition-all duration-150',
            !mobileCardComponent && [
              'border rounded-lg',
              'bg-card',
              'border-border',
              'shadow-sm hover:shadow-md',
            ],
            mobileCardComponent && 'space-y-2', // Add vertical spacing for mobile cards
            className
          )}
          style={mobileCardComponent ? { background: 'transparent', boxShadow: 'none' } : undefined}
        >
          {/* Global styles for the table */}
          <style jsx global>{`
            /* Table styles using theme variables */
            .data-table {
              --tw-bg-opacity: 1;
              background-color: hsl(var(--card));
              color: hsl(var(--card-foreground));
            }
            .data-table-header {
              background-color: hsl(var(--card));
              border-bottom: 1px solid hsl(var(--border));
            }
            .data-table-row {
              transition: background-color 0.2s ease, transform 0.1s ease;
              border-bottom: 1px solid hsl(var(--border) / 0.5);
            }
            .data-table-row:last-child {
              border-bottom: none;
            }
            .data-table-row:hover {
              background-color: hsl(var(--accent) / 0.05);
            }
            .data-table-row:active {
              transform: scale(0.998);
              background-color: hsl(var(--accent) / 0.1);
            }
          `}</style>
        {/* Desktop Table View */}
        <div className="hidden md:block">
          <Table className="data-table w-full border-collapse">
            <TableHeader 
              className="data-table-header sticky top-0 z-10"
            >
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead 
                      key={header.id}
                      className={cn(
                        'font-medium text-xs uppercase tracking-wider text-muted-foreground',
                        'py-3 px-4 first:pl-6 last:pr-6',
                        'border-b border-border',
                        'hover:bg-muted/20 dark:hover:bg-muted/20 transition-colors',
                        'group relative',
                        'select-none',
                        header.column.getCanSort() ? 'cursor-pointer pr-8' : ''
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div className="flex items-center space-x-1">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        {header.column.getCanSort() && (
                          <span className="ml-1 opacity-50 group-hover:opacity-100">
                            {header.column.getIsSorted() === 'desc' ? (
                              <span className="h-4 w-4">↓</span>
                            ) : header.column.getIsSorted() === 'asc' ? (
                              <span className="h-4 w-4">↑</span>
                            ) : (
                              <span className="h-4 w-4 opacity-0 group-hover:opacity-50">↕</span>
                            )}
                          </span>
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>

              {isLoading ? (
                // Loading state for the table
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Loading data...</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : isSearching ? (
                // Searching state for the table
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Searching...</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : showEmptyState && emptyState ? (
                // Empty state
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-96 text-center">
                    <div className="flex flex-col items-center justify-center space-y-4 py-12">
                      {emptyState.icon && (
                        <emptyState.icon className="h-12 w-12 text-muted-foreground" />
                      )}
                      <h3 className="text-lg font-medium">{emptyState.title}</h3>
                      <p className="text-muted-foreground">{emptyState.description}</p>
                      {emptyState.action && (
                        <Button onClick={emptyState.action.onClick}>
                          {emptyState.action.label}
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : showNoResultsState ? (
                // No results state
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-96 text-center">
                    <div className="flex flex-col items-center justify-center space-y-4 py-12">
                      <SearchX className="h-12 w-12 text-muted-foreground" />
                      <h3 className="text-lg font-medium">No results found</h3>
                      <p className="text-muted-foreground max-w-md text-center mx-auto">
                        We couldn't find any items matching your search.
                      </p>
                      {(searchValue || columnFilters.length > 0) && (
                        <Button 
                          variant="outline" 
                          onClick={() => {
                            onSearch('');
                            setColumnFilters([]);
                          }}
                        >
                          Clear filters
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                // Data rows
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() ? "selected" : undefined}
                    onClick={(e) => handleRowClick(row, e)}
                    className={cn(
                      'data-table-row',
                      onRowClick && 'cursor-pointer',
                      'group',
                      'data-[state=selected]:bg-accent/10',
                      'hover:bg-accent/5',
                      'active:bg-accent/10',
                      'transition-colors duration-150',
                      'relative'
                    )}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Mobile Card View */}
        {mobileCardComponent && (
          <div className="md:hidden space-y-3">
            {showNoResultsState ? (
              <div className="flex flex-col items-center justify-center space-y-4 py-12 px-4">
                <SearchX className="h-12 w-12 text-muted-foreground" />
                <h3 className="text-lg font-medium text-center">No results found</h3>
                <p className="text-muted-foreground text-center mb-4 max-w-md mx-auto">
                  We couldn't find any items matching your search.
                </p>
                {(searchValue || columnFilters.length > 0) && (
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      onSearch('');
                      setColumnFilters([]);
                    }}
                  >
                    Clear filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                {table.getRowModel().rows.map((row) => (
                  <div 
                    key={row.id}
                    className={cn(
                      'transition-transform duration-200',
                      onRowClick && 'active:scale-[0.99]' // Subtle press effect
                    )}
                    onClick={(e) => onRowClick && handleRowClick(row, e)}
                  >
                    {React.createElement(mobileCardComponent, { row: row.original })}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        
        {/* Pagination - Hidden on mobile when using card view */}
        <div className={cn(
          'hidden w-full md:flex items-center border-t bg-card px-4 py-3',
          'shadow-[0_-1px_3px_rgba(0,0,0,0.04)] dark:shadow-[0_-1px_3px_rgba(0,0,0,0.15)]',
          !mobileCardComponent && 'flex' // Show on mobile if not using card view
        )}>
          <DataTablePagination
            table={table}
            page={page}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
            className="p-0"
          />
          </div>
        </div>
      </div>
    </div>
  );
}

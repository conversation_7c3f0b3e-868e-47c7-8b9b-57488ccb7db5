'use client';

import { cn } from '@/lib/utils';
import { UserRole } from '@/types/user';

interface RoleBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  role: UserRole | string;
  className?: string;
}

export const RoleBadge: React.FC<RoleBadgeProps> = ({ role, className, ...props }) => {
  // Map roles to their respective colors and display names
  const roleConfig = {
    account_owner: {
      label: 'Account Owner',
      className: 'bg-purple-100 text-purple-800 hover:bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400',
    },
    property_manager: {
      label: 'Property Manager',
      className: 'bg-blue-100 text-blue-800 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400',
    },
    leasing_agent: {
      label: 'Leasing Agent',
      className: 'bg-cyan-100 text-cyan-800 hover:bg-cyan-100 dark:bg-cyan-900/30 dark:text-cyan-400',
    },
    maintenance_staff: {
      label: 'Maintenance',
      className: 'bg-amber-100 text-amber-800 hover:bg-amber-100 dark:bg-amber-900/30 dark:text-amber-400',
    },
    tenant: {
      label: 'Tenant',
      className: 'bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400',
    },
  };

  // Default configuration for unknown roles
  const defaultConfig = {
    label: role.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
    className: 'bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-800/30 dark:text-gray-400',
  };

  const config = roleConfig[role as UserRole] || defaultConfig;

  return (
    <div 
      className={cn(
        'whitespace-nowrap inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        config.className,
        className
      )}
      {...props}
    >
      {config.label}
    </div>
  );
}

export function RoleBadges({ roles }: { roles: (UserRole | string)[] }) {
  if (!roles?.length) {
    return <span className="text-sm text-muted-foreground">No roles</span>;
  }

  return (
    <div className="flex flex-wrap gap-1.5">
      {roles.map((role, index) => (
        <RoleBadge key={index} role={role} />
      ))}
    </div>
  );
}

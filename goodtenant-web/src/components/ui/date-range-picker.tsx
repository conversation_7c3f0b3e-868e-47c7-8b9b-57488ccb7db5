"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export type DateRange = {
  from?: Date;
  to?: Date;
};

interface DateRangePickerProps {
  dateRange: DateRange | undefined;
  onSelect: (range: DateRange | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function DateRangePicker({
  dateRange,
  onSelect,
  placeholder = "Select date range",
  className,
  disabled = false,
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [startDate, setStartDate] = React.useState<Date | undefined>(dateRange?.from);
  const [endDate, setEndDate] = React.useState<Date | undefined>(dateRange?.to);

  const handleSelect = (date: Date | undefined) => {
    if (!date) return;
    
    // If both dates are selected or the selected date is before the start date, reset the selection
    if ((startDate && endDate) || (startDate && date < startDate)) {
      setStartDate(date);
      setEndDate(undefined);
    } else if (!startDate) {
      setStartDate(date);
    } else {
      setEndDate(date);
      onSelect({ from: startDate, to: date });
      setIsOpen(false);
    }
  };

  const displayText = React.useMemo(() => {
    if (startDate && endDate) {
      return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
    }
    if (startDate) {
      return `${format(startDate, 'MMM d, yyyy')} - `;
    }
    return placeholder;
  }, [startDate, endDate, placeholder]);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    setStartDate(undefined);
    setEndDate(undefined);
    onSelect(undefined);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !startDate && !endDate && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {displayText}
            {(startDate || endDate) && (
              <button
                onClick={handleClear}
                className="ml-auto rounded-full p-1 hover:bg-accent hover:text-accent-foreground"
              >
                <span className="sr-only">Clear selection</span>
                <span aria-hidden>×</span>
              </button>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={startDate}
            onSelect={handleSelect}
            disabled={disabled}
            initialFocus
            className="rounded-md border"
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

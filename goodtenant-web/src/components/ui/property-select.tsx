'use client';

import { useEffect, useState, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { propertyService } from '@/app/services/propertyService';
import { Property, PropertyType, PropertyStatus, PROPERTY_STATUS } from '@/types/property';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, X, Building, Home, Hotel, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PropertySelectProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  className?: string;
  buttonClassName?: string;
  disabled?: boolean;
  showAddress?: boolean;
  showStatus?: boolean;
  variant?: 'default' | 'compact';
}

export function PropertySelect({
  value,
  onChange,
  placeholder = 'Select a property...',
  className,
  buttonClassName,
  disabled = false,
  showAddress = true,
  showStatus = false,
  variant = 'default',
}: PropertySelectProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch properties with search
  const fetchProperties = useCallback(async (search = '') => {
    try {
      setLoading(true);
      // We'll use a larger page size since we're implementing client-side search
      const response = await propertyService.getProperties(1, 50);
      let filtered = response.data || [];
      
      // Client-side filtering if search term exists
      if (search) {
        const searchLower = search.toLowerCase();
        filtered = filtered.filter(
          (property: Property) =>
            property.name.toLowerCase().includes(searchLower) ||
            (property.addressLine1?.toLowerCase().includes(searchLower) || '') ||
            (property.city?.toLowerCase().includes(searchLower) || '')
        );
      }
      
      setProperties(filtered);
    } catch (error) {
      console.error('Error fetching properties:', error);
      setProperties([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchProperties();
  }, [fetchProperties]);

  // Update selected property when value changes
  useEffect(() => {
    if (value && properties.length > 0) {
      const prop = properties.find((p) => p.id === value);
      setSelectedProperty(prop || null);
    } else {
      setSelectedProperty(null);
    }
  }, [value, properties]);

  const handleSelect = (propertyId: string) => {
    const property = properties.find((p) => p.id === propertyId);
    if (property) {
      setSelectedProperty(property);
      onChange(propertyId);
      setOpen(false);
    }
  };
  
  const handleClear = () => {
    setSelectedProperty(null);
    setSearchTerm('');
    onChange(undefined);
    setOpen(false);
  };

  const getPropertyIcon = (propertyType?: PropertyType) => {
    switch (propertyType) {
      case 'apartment':
        return <Home className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'house':
        return <Home className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'commercial':
        return <Building className="h-4 w-4 mr-2 text-muted-foreground" />;
      default:
        return <Building className="h-4 w-4 mr-2 text-muted-foreground" />;
    }
  };

  const displayValue = selectedProperty 
    ? (
        <div className="flex items-center">
          {getPropertyIcon(selectedProperty.propertyType)}
          <div className="truncate">
            <div className="font-medium">{selectedProperty.name}</div>
            {showAddress && variant !== 'compact' && (
              <div className="text-xs text-muted-foreground truncate">
                {selectedProperty.addressLine1}, {selectedProperty.city}
              </div>
            )}
          </div>
        </div>
      )
    : (
      <span className="text-muted-foreground">{placeholder}</span>
    );
    
  const isPropertyActive = (status: PropertyStatus) => {
    return status === PROPERTY_STATUS.OCCUPIED || status === PROPERTY_STATUS.VACANT;
  };

  return (
    <div className={cn('w-full', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative w-full">
            <Button
              type="button"
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className={cn(
                'w-full justify-between truncate pr-8 h-auto py-2',
                variant === 'compact' ? 'min-h-9' : 'min-h-11',
                !selectedProperty && 'text-muted-foreground',
                disabled && 'opacity-50 cursor-not-allowed',
                'bg-card hover:bg-card/90 transition-colors',
                'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                buttonClassName
              )}
              disabled={disabled}
            >
              <span className="flex-1 text-left truncate">
                {displayValue}
              </span>
              {loading ? (
                <Loader2 className="h-4 w-4 ml-2 animate-spin opacity-50" />
              ) : (
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              )}
            </Button>
            {selectedProperty && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full w-6 p-0 opacity-70 hover:opacity-100"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClear();
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="Search properties..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            <CommandList className="max-h-[300px] overflow-y-auto">
              <CommandEmpty className="py-6 text-center text-sm">
                {loading ? (
                  <div className="flex flex-col items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin mb-2" />
                    <span>Loading properties...</span>
                  </div>
                ) : searchTerm ? (
                  <div className="py-4">
                    <p>No properties match &quot;{searchTerm}&quot;</p>
                    <p className="text-xs text-muted-foreground mt-1">Try a different search term</p>
                  </div>
                ) : (
                  <div className="py-4">
                    <p>No properties found</p>
                    <p className="text-xs text-muted-foreground mt-1">Add a property to get started</p>
                  </div>
                )}
              </CommandEmpty>
              <CommandGroup className="p-1">
                {properties.map((property) => (
                  <CommandItem
                    key={property.id}
                    value={property.id}
                    onSelect={handleSelect}
                    className={cn(
                      'flex flex-col items-start rounded-md p-2',
                      'aria-selected:bg-accent aria-selected:text-accent-foreground',
                      'hover:bg-accent hover:text-accent-foreground cursor-pointer',
                      'transition-colors duration-200',
                      'border border-transparent hover:border-border'
                    )}
                  >
                    <div className="flex items-center w-full">
                      <Check
                        className={cn(
                          'h-4 w-4 flex-shrink-0',
                          value === property.id ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      <div className="ml-2 flex-1 min-w-0">
                        <div className="flex items-center">
                          {getPropertyIcon(property.propertyType)}
                          <span className="font-medium truncate">
                            {property.name}
                          </span>
                          {showStatus && (
                            <span className={cn(
                              'ml-2 px-2 py-0.5 rounded-full text-xs capitalize',
                              isPropertyActive(property.status)
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                                : 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400'
                            )}>
                              {property.status.toLowerCase().replace(/_/g, ' ')}
                            </span>
                          )}
                        </div>
                        {showAddress && (
                          <div className="text-muted-foreground text-xs truncate ml-6 mt-0.5">
                            {property.addressLine1}, {property.city}, {property.state} {property.postalCode}
                          </div>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

'use client';

import { useEffect, useState, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { userService } from '@/app/services/userService';
import type { UserRole } from '@/types/user';
import { User } from '@/types/user';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, User as UserIcon, Loader2, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface UserSelectProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showEmail?: boolean;
  showRole?: boolean;
  variant?: 'default' | 'compact';
  roles?: UserRole[]; // Optional filter by roles
}

export function UserSelect({
  value,
  onChange,
  placeholder = 'Select a user...',
  className,
  disabled = false,
  showEmail = true,
  showRole = true,
  variant = 'default',
  roles = [],
}: UserSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch users with search
  const fetchUsers = useCallback(async (search: string) => {
    try {
      setLoading(true);
      const response = await userService.getUsers({
        search,
        limit: 50,
        page: 1
      });
      setUsers(response.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      setUsers([]);
      // Re-throw the error to be handled by the parent component
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load of selected user if value is provided
  useEffect(() => {
    if (value && !selectedUser) {
      const loadSelectedUser = async () => {
        try {
          const response = await userService.getUserById(value);
          setSelectedUser(response.data);
        } catch (error) {
          console.error('Error loading selected user:', error);
        }
      };
      loadSelectedUser();
    }
  }, [value, selectedUser]);

  // Fetch users when dropdown is opened or search term changes
  useEffect(() => {
    if (open) {
      fetchUsers(debouncedSearchTerm).catch(console.error);
    } else {
      // Reset search when closing the dropdown
      setSearchTerm('');
    }
  }, [debouncedSearchTerm, open, fetchUsers]);

  const handleSelect = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setSelectedUser(user);
      onChange(userId === value ? undefined : userId);
      setOpen(false);
    }
  };

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedUser(null);
    onChange(undefined);
  };

  // Get the primary role from the user's accountUsers
  const getPrimaryRole = (user: User) => {
    const defaultAccount = user.accountUsers?.find(au => au.isDefault);
    return defaultAccount?.primaryRole || '';
  };

  // Format role for display
  const formatRole = (role: string) => {
    return role
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get user initials for avatar fallback
  const getInitials = (user: User) => {
    return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase() || '?';
  };

  // Display value based on variant
  const displayValue = selectedUser 
    ? variant === 'compact'
      ? `${selectedUser.firstName} ${selectedUser.lastName}`.trim()
      : `${selectedUser.firstName} ${selectedUser.lastName}`.trim()
    : placeholder;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={null}
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            'w-full justify-between bg-card hover:bg-card/90 border border-input',
            variant === 'compact' ? 'h-9' : 'h-10',
            !selectedUser && 'text-muted-foreground',
            className
          )}
        >
          <div className="flex items-center overflow-hidden">
            {selectedUser ? (
              <>
                <Avatar className={cn('mr-2', variant === 'compact' ? 'h-5 w-5' : 'h-6 w-6')}>
                  <AvatarImage src={selectedUser.avatar || undefined} alt="" />
                  <AvatarFallback className="text-xs">
                    {getInitials(selectedUser)}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate">{displayValue}</span>
                {showRole && selectedUser.accountUsers?.length > 0 && (
                  <Badge 
                    variant="secondary" 
                    className="ml-2 hidden sm:inline-flex"
                  >
                    {formatRole(getPrimaryRole(selectedUser))}
                  </Badge>
                )}
              </>
            ) : (
              <span className="truncate">{displayValue}</span>
            )}
          </div>
          <div className="flex items-center">
            {selectedUser && (
              <X
                className="ml-2 h-4 w-4 opacity-50 hover:opacity-100"
                onClick={clearSelection}
              />
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search users..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          {loading ? (
            <div className="flex items-center justify-center py-6">
              <Loader2 className="h-5 w-5 animate-spin" />
            </div>
          ) : users.length === 0 ? (
            <CommandEmpty>No users found</CommandEmpty>
          ) : (
            <CommandGroup>
              <CommandList>
                {users.map((user) => (
                  <CommandItem
                    key={user.id}
                    value={user.id}
                    onSelect={handleSelect}
                    className="flex items-center gap-2"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar || undefined} alt="" />
                      <AvatarFallback className="text-xs">
                        {getInitials(user)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium truncate">
                          {user.firstName} {user.lastName}
                        </p>
                        {showRole && user.accountUsers?.length > 0 && (
                          <Badge variant="outline" className="hidden sm:inline-flex">
                            {formatRole(getPrimaryRole(user))}
                          </Badge>
                        )}
                      </div>
                      {showEmail && (
                        <p className="text-xs text-muted-foreground truncate">
                          {user.email}
                        </p>
                      )}
                    </div>
                    <Check
                      className={cn(
                        'ml-2 h-4 w-4',
                        value === user.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandList>
            </CommandGroup>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

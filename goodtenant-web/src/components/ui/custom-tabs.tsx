"use client";

import * as React from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollB<PERSON> } from "./scroll-area"
import { Tabs, Ta<PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "./tabs"
import { cn } from "@/lib/utils"

export interface TabItem {
  value: string
  label: string
  icon: React.ReactNode
  content: React.ReactNode
}

interface CustomTabsProps {
  defaultValue: string
  tabs: TabItem[]
  className?: string
  tabListClassName?: string
  tabTriggerClassName?: string
  contentClassName?: string
}

const CustomTabs = React.forwardRef<HTMLDivElement, CustomTabsProps>(
  ({
    defaultValue,
    tabs,
    className,
    tabListClassName,
    tabTriggerClassName,
    contentClassName,
    ...props
  }, ref) => {
    return (
      <Tabs 
        ref={ref} 
        defaultValue={defaultValue} 
        className={cn("w-full", className)}
        {...props}
      >
        <ScrollArea className="w-full">
          <TabsList 
            className={cn(
              "mb-3 gap-1 bg-transparent w-full justify-start overflow-x-auto py-1",
              tabListClassName
            )}
          >
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className={cn(
                  "rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-none transition-all whitespace-nowrap px-4 py-2 text-sm sm:text-base",
                  tabTriggerClassName
                )}
              >
                <span className="flex items-center gap-2">
                  <span className="shrink-0">{tab.icon}</span>
                  <span className="truncate">{tab.label}</span>
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
        
        {tabs.map((tab) => (
          <TabsContent 
            key={`${tab.value}-content`} 
            value={tab.value}
            className={cn("mt-0", contentClassName)}
          >
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    )
  }
)
CustomTabs.displayName = "CustomTabs"

export { CustomTabs }

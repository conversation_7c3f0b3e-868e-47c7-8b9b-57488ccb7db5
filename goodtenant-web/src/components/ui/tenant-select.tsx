'use client';

import { useEffect, useState, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { userService } from '@/app/services/userService';
import { User, UserRole } from '@/types/user';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, User as UserIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface TenantSelectProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showEmail?: boolean;
  showStatus?: boolean;
  variant?: 'default' | 'compact';
}

export function TenantSelect({
  value,
  onChange,
  placeholder = 'Select a tenant...',
  className,
  disabled = false,
  showEmail = true,
  showStatus = false,
  variant = 'default',
}: TenantSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [tenants, setTenants] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<User | null>(null);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch tenants with search
  const fetchTenants = useCallback(async (search = '') => {
    try {
      setLoading(true);
      // We'll use a larger page size since we're implementing client-side search
      const response = await userService.getUsers({
        role: 'tenant',
        limit: 50,
        ...(search && { search })
      });
      
      setTenants(response.data || []);
    } catch (error) {
      console.error('Error fetching tenants:', error);
      setTenants([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchTenants();
  }, [fetchTenants]);

  // Handle search term changes
  useEffect(() => {
    if (debouncedSearchTerm !== undefined) {
      fetchTenants(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, fetchTenants]);

  // Set selected tenant when value changes
  useEffect(() => {
    if (value && tenants.length > 0) {
      const tenant = tenants.find(t => t.id === value);
      setSelectedTenant(tenant || null);
    } else {
      setSelectedTenant(null);
    }
  }, [value, tenants]);

  const getInitials = (user: User) => {
    return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const getStatusVariant = (isActive: boolean = true) => {
    return isActive ? 'default' : 'outline';
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'w-full justify-between',
            variant === 'compact' ? 'h-9' : 'h-auto min-h-10 py-2',
            !selectedTenant && 'text-muted-foreground',
            className
          )}
          disabled={disabled}
        >
          {selectedTenant ? (
            <div className="flex items-center gap-2 flex-1 overflow-hidden">
              <div className="flex-shrink-0">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={selectedTenant.avatar || ''} alt={`${selectedTenant.firstName} ${selectedTenant.lastName}`} />
                  <AvatarFallback className="text-xs">
                    {getInitials(selectedTenant)}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div className="flex-1 min-w-0 overflow-hidden">
                <div className="flex items-center gap-2 truncate">
                  <span className="truncate font-medium">
                    {selectedTenant.firstName} {selectedTenant.lastName}
                  </span>
                  {showStatus && (
                    <Badge variant={getStatusVariant(selectedTenant.isActive)} className="shrink-0 text-xs">
                      {selectedTenant.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  )}
                </div>
                {showEmail && variant !== 'compact' && (
                  <p className="text-xs text-muted-foreground truncate">
                    {selectedTenant.email}
                  </p>
                )}
              </div>
            </div>
          ) : (
            <span className="truncate">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search tenants..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandList>
            {loading ? (
              <div className="py-6 text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto text-muted-foreground" />
                <p className="mt-2 text-sm text-muted-foreground">Loading tenants...</p>
              </div>
            ) : tenants.length === 0 ? (
              <CommandEmpty>No tenants found</CommandEmpty>
            ) : (
              <CommandGroup>
                {tenants.map((tenant) => (
                  <CommandItem
                    key={tenant.id}
                    value={tenant.id}
                    onSelect={() => {
                      onChange(tenant.id === value ? undefined : tenant.id);
                      setOpen(false);
                    }}
                    className="flex items-center gap-2"
                  >
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={tenant.avatar || ''} alt={`${tenant.firstName} ${tenant.lastName}`} />
                        <AvatarFallback className="text-xs">
                          {getInitials(tenant)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">
                            {tenant.firstName} {tenant.lastName}
                          </span>
                          {showStatus && (
                            <Badge variant={getStatusVariant(tenant.isActive)} className="shrink-0 text-xs">
                              {tenant.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          )}
                        </div>
                        {showEmail && variant !== 'compact' && (
                          <p className="text-xs text-muted-foreground truncate">
                            {tenant.email}
                          </p>
                        )}
                      </div>
                    </div>
                    <Check
                      className={cn(
                        'ml-2 h-4 w-4',
                        value === tenant.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

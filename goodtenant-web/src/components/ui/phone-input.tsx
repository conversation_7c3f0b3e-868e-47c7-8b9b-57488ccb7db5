'use client';

import { useState, useRef, useEffect } from 'react';
import { usePhoneInput, CountryIso2, defaultCountries } from 'react-international-phone';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Input } from './input';
import ReactCountryFlag from 'react-country-flag';

interface PhoneInputProps {
  value?: string;
  onChange: (phone: string) => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
}

// Import the correct type from the library
import type { CountryData as LibCountryData } from 'react-international-phone';

// Define our own interface that matches what we actually use
interface CountryData {
  name: string;
  iso2: string;
  dialCode: string;
}

export function PhoneInput({
  value = '',
  onChange,
  disabled = false,
  className = '',
  placeholder = '(*************',
}: PhoneInputProps) {
  const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false);
  const countrySelectRef = useRef<HTMLDivElement>(null);
  
  const phoneInput = usePhoneInput({
    defaultCountry: 'us',
    value: value || '',
    countries: defaultCountries,
    onChange: (data) => {
      onChange(data.phone);
    },
  });



  // Transform the country data to our expected format
  const countries = (defaultCountries as unknown as LibCountryData[]).map(country => ({
    name: country[0],
    iso2: country[1],
    dialCode: country[2].replace(/\D/g, ''), // Remove any non-digit characters
  }));

  // Close country select when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (countrySelectRef.current && !countrySelectRef.current.contains(event.target as Node)) {
        setIsCountrySelectOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`} ref={countrySelectRef}>
      <div className="flex">
        <button
          type="button"
          onClick={() => !disabled && setIsCountrySelectOpen(!isCountrySelectOpen)}
          className={`flex items-center px-3 border border-r-0 rounded-l-md bg-background text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
            disabled ? 'cursor-default' : 'cursor-pointer'
          }`}
          disabled={disabled}
        >
          <ReactCountryFlag 
            countryCode={phoneInput.country?.iso2 ? (phoneInput.country.iso2 as string).toUpperCase() : 'US'} 
            svg 
            style={{
              width: '1.5em',
              height: '1.5em',
              borderRadius: '2px',
              marginRight: '4px'
            }}
            title={phoneInput.country?.iso2 ? phoneInput.country.iso2.toUpperCase() : 'US'}
          />
          {!disabled && (
            isCountrySelectOpen ? 
              <ChevronUp className="w-4 h-4 ml-1" /> : 
              <ChevronDown className="w-4 h-4 ml-1" />
          )}
        </button>
        <Input
          type="tel"
          placeholder={placeholder}
          className="rounded-l-none"
          value={phoneInput.inputValue}
          onChange={phoneInput.handlePhoneValueChange}
          disabled={disabled}
        />
      </div>
      
      {isCountrySelectOpen && !disabled && (
        <div className="absolute z-10 w-64 mt-1 overflow-auto bg-background border rounded-md shadow-lg max-h-60">
          {countries.map((country) => (
            <div
              key={country.iso2}
                className={`px-4 py-2 text-sm cursor-pointer hover:bg-accent ${
                phoneInput.country?.iso2 === country?.iso2 ? 'bg-accent font-medium' : ''
              }`}
              onClick={() => {
                if (country?.iso2) {
                  phoneInput.setCountry(country.iso2);
                  setIsCountrySelectOpen(false);
                }
              }}
            >
              <div className="flex items-center">
                <ReactCountryFlag 
                  countryCode={country.iso2} 
                  svg 
                  style={{
                    width: '1.25em',
                    height: '1.25em',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}
                  title={country.iso2}
                />
                <span className="ml-2">{country.name} <span className="text-muted-foreground">+{country.dialCode}</span></span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

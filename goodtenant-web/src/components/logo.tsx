import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps extends React.HTMLAttributes<HTMLDivElement> {
  withText?: boolean;
  children?: React.ReactNode;
}

export function Logo({ className, withText = true, ...props }: LogoProps) {
  return (
    <div 
      className={cn("flex items-center gap-3 cursor-pointer group", className)}
      {...props}
    >
      <div className="relative">
        <svg
          width="40"
          height="40"
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-10 h-10"
        >
          {/* House shape */}
          <rect width="40" height="40" rx="8" className="fill-primary" />
          <path 
            d="M30 30V16.5L20 9L10 16.5V30H15V23H25V30H30Z" 
            className="fill-primary-foreground"
          />
          {/* Windows */}
          <rect 
            x="12" 
            y="20" 
            width="3" 
            height="3" 
            rx="0.5" 
            className="fill-primary-foreground"
          />
          <rect 
            x="25" 
            y="20" 
            width="3" 
            height="3" 
            rx="0.5" 
            className="fill-primary-foreground"
          />
        </svg>
      </div>
      {withText && (
        <div className="flex flex-col">
          <div className="flex items-baseline">
            <span className="text-xl font-bold tracking-tight">GoodTenant</span>
          </div>
          <span className="text-xs text-muted-foreground -mt-1">Property Management</span>
        </div>
      )}
    </div>
  );
}

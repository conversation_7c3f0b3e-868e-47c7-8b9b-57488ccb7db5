// LocaleProvider.tsx
'use client';

import { NextIntlClientProvider } from 'next-intl';
import { ReactNode, useMemo } from 'react';
import { getMessagesSync } from '@/i18n/utils';

export default function LocaleProvider({ 
  children, 
  locale 
}: { 
  children: ReactNode; 
  locale: string;
}) {
  const messages = useMemo(() => getMessagesSync(locale), [locale]);

  return (
    <NextIntlClientProvider
      locale={locale}
      messages={messages}
    >
      {children}
    </NextIntlClientProvider>
  );
}
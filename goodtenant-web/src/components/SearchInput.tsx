'use client';

import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';
import { useEffect, useState, useRef } from 'react';

type SearchInputProps = {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  debounce?: number;
  isLoading?: boolean;
  disabled?: boolean;
};

export function SearchInput({ 
  value, 
  onChange, 
  className = '',
  placeholder,
  debounce = 300,
  isLoading = false,
  disabled = false,
}: SearchInputProps) {
  const t = useTranslations('common');
  const [inputValue, setInputValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Sync input value with external value when it changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);
  
  // Handle debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputValue !== value) {
        onChange(inputValue);
      }
    }, debounce);

    return () => clearTimeout(timer);
  }, [inputValue, debounce, onChange, value]);

  const handleClear = () => {
    setInputValue('');
    onChange('');
  };
  
  // Focus input when container is clicked
  const handleContainerClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };
  
  return (
    <div className={cn('space-y-2 w-full', className)}>
      <Label htmlFor="search" className="text-sm font-medium sr-only">
        {t('search')}
      </Label>
      <div 
        className="relative"
        onClick={handleContainerClick}
      >
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          ref={inputRef}
          id="search"
          placeholder={placeholder || t('searchPlaceholder')}
          className={cn(
            'pl-9 pr-9 w-full',
            'bg-white dark:bg-muted/30 backdrop-blur-sm border border-input',
            'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-muted/50',
            'shadow-sm',
            'text-foreground'
          )}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          disabled={disabled || isLoading}
          aria-busy={isLoading}
        />
        <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
          {isLoading ? (
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-muted-foreground border-t-transparent" />
          ) : inputValue ? (
            <button
              type="button"
              onClick={handleClear}
              className="rounded-full p-0.5 hover:bg-muted text-muted-foreground hover:text-foreground transition-colors"
              aria-label={t('clearSearch')}
            >
              <X className="h-3.5 w-3.5" />
            </button>
          ) : null}
        </div>
      </div>
    </div>
  );
}

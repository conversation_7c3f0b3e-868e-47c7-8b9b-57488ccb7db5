'use client';

import { useState, useEffect } from 'react';
import { Bell, AlertTriangle, CheckCircle2, Clock, AlertCircle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { maintenanceService } from '@/app/services/maintenanceService';
import { MaintenanceTicket, MaintenanceStatus } from '@/types/maintenance';
import { useRouter, usePathname } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';

export function MaintenanceNotifications() {
  const [open, setOpen] = useState(false);
  const [tickets, setTickets] = useState<MaintenanceTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();
  
  // Extract locale from pathname
  const getLocale = () => {
    const pathParts = pathname.split('/');
    // The locale is typically the first part after the initial slash
    return pathParts[1] || 'en';
  };
  
  // Debug log when component mounts and updates
  useEffect(() => {
    console.log('MaintenanceNotifications mounted, open state:', open);
    
    // Add simple test data for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('Adding test data for development');
      
      // Create a minimal test ticket with only required fields
      const testTicket: any = {
        id: 'test-1',
        title: 'Test Maintenance Ticket',
        description: 'This is a test ticket',
        status: 'open',
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        property: {
          id: 'test-prop-1',
          name: 'Test Property'
        }
      };
      
      if (!tickets.length) {
        setTickets([testTicket]);
        setLoading(false);
      }
    }
    
    return () => console.log('MaintenanceNotifications unmounting');
  }, [open, tickets.length]);
  
  // Fetch notifications when component mounts or page refreshes
  useEffect(() => {
    console.log('Initial fetch of maintenance tickets on mount');
    fetchOpenTickets();
    
    // Setup event listener for page visibility changes to refresh data when returning to tab
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('Page became visible, refreshing tickets');
        fetchOpenTickets();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const fetchOpenTickets = async () => {
    try {
      setLoading(true);
      console.log('Fetching open maintenance tickets...');
      const response = await maintenanceService.getAllMaintenanceTickets(
        1, // page
        5, // limit
        'open' as MaintenanceStatus // only show open tickets
      );
      console.log('API Response:', response);
      if (response && response.success) {
        setTickets(response.data || []);
      } else {
        console.error('Error in response:', response);
        setError('Failed to load tickets');
      }
    } catch (err) {
      console.error('Error fetching maintenance tickets:', err);
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('useEffect triggered, open:', open);
    if (open) {
      console.log('Fetching tickets because dropdown was opened');
      fetchOpenTickets();
    } else {
      console.log('Dropdown closed, not fetching tickets');
    }
  }, [open]);

  const getPriorityBadge = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return <Badge variant="destructive">High</Badge>;
      case 'medium':
        return <Badge className="bg-amber-500 hover:bg-amber-600">Medium</Badge>;
      default:
        return <Badge variant="outline">Low</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const handleTicketClick = (ticketId: string) => {
    setOpen(false);
    const locale = getLocale();
    router.push(`/${locale}/maintenance/${ticketId}`);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="relative h-10 w-10"
          onClick={() => setOpen(!open)}
        >
          <Bell className="h-5 w-5" />
          {tickets.length > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {tickets.length > 9 ? '9+' : tickets.length}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        className="w-80 z-[100] bg-background border border-border shadow-lg rounded-md p-0 overflow-hidden animate-in fade-in-80"
        align="end"
        sideOffset={8}
        onCloseAutoFocus={(e) => e.preventDefault()}
        onEscapeKeyDown={() => setOpen(false)}
      >
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>Maintenance Notifications</span>
          <div className="flex items-center gap-2">
            {loading && <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={(e) => {
                e.stopPropagation();
                fetchOpenTickets();
              }}
              disabled={loading}
              className="h-8 px-2 text-xs"
            >
              Refresh
            </Button>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {loading ? (
          <div className="p-4 space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-start space-x-3 p-2 rounded-md hover:bg-muted/50 transition-colors">
                <Skeleton className="h-4 w-4 rounded-full mt-1 flex-shrink-0" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="p-4 text-sm text-destructive flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {error}
          </div>
        ) : tickets.length === 0 ? (
          <div className="p-6 flex flex-col items-center justify-center text-center">
            <Bell className="h-10 w-10 text-muted-foreground/50 mb-2" />
            <h4 className="text-sm font-medium text-foreground">No notifications</h4>
            <p className="text-xs text-muted-foreground mt-1">You're all caught up!</p>
          </div>
        ) : (
          <>
            {tickets.map((ticket) => (
              <DropdownMenuItem 
                key={ticket.id} 
                className="flex flex-col items-start gap-1 p-3 cursor-pointer hover:bg-accent"
                onClick={() => handleTicketClick(ticket.id)}
              >
                <div className="flex justify-between w-full items-start">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(ticket.status)}
                    <span className="font-medium line-clamp-1">{ticket.title}</span>
                  </div>
                  {getPriorityBadge(ticket.priority)}
                </div>
                <div className="text-xs text-muted-foreground line-clamp-2">
                  {ticket.description}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {ticket.property?.name && (
                    <span className="mr-2">{ticket.property.name}</span>
                  )}
                  <span>{formatDistanceToNow(new Date(ticket.createdAt), { addSuffix: true })}</span>
                </div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuItem 
              className="justify-center text-sm font-medium text-primary cursor-pointer"
              onClick={() => {
                setOpen(false);
                const locale = getLocale();
                router.push(`/${locale}/maintenance`);
              }}
            >
              View all maintenance tickets
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

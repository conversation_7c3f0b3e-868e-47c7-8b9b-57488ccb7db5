"use client";

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Bell,  AlertCircle, Clock, FileText, DollarSign, Home, MessageSquare } from 'lucide-react';
import { But<PERSON> } from '../../components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { ScrollArea } from '../../components/ui/scroll-area';
import { cn } from '../../lib/utils';
import { Notification, NotificationType } from '../../types/notification';
import { useNotifications } from '../../app/context/NotificationContext';
import { formatDistanceToNow } from 'date-fns';
import { useRouter, usePathname } from 'next/navigation';

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'ALERT':
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    case 'MAINTENANCE':
      return <Home className="h-4 w-4 text-blue-500" />;
    case 'PAYMENT':
      return <DollarSign className="h-4 w-4 text-green-500" />;
    case 'LEASE':
      return <FileText className="h-4 w-4 text-purple-500" />;
    case 'MESSAGE':
      return <MessageSquare className="h-4 w-4 text-cyan-500" />;
    default:
      return <Bell className="h-4 w-4 text-gray-500" />;
  }
};

// Error boundary fallback component
const ErrorFallback = ({ resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="p-4 text-center text-sm text-destructive">
    <p>Something went wrong with notifications</p>
    <button 
      onClick={resetErrorBoundary}
      className="mt-2 text-sm text-primary hover:underline"
    >
      Try again
    </button>
  </div>
);

export const NotificationDropdown: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname?.split('/')[1] || 'en';
  const { state, markAsRead, markAllAsRead, fetchNotifications } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Safely get notifications with fallback to empty array
  const notifications = useMemo(() => {
    try {
      return Array.isArray(state?.notifications) ? state.notifications : [];
    } catch (error) {
      console.error('Error accessing notifications:', error);
      return [];
    }
  }, [state?.notifications]);
  
  const unreadNotifications = useMemo(() => 
    notifications.filter(n => n && !n.isRead), 
    [notifications]
  );
  
  const hasUnread = unreadNotifications.length > 0;

  // Load notifications when dropdown opens
  const handleOpenChange = async (open: boolean) => {
    setIsOpen(open);
    if (open) {
      try {
        setIsLoading(true);
        setError(null);
        await fetchNotifications();
      } catch (err) {
        console.error('Failed to fetch notifications:', err);
        setError('Failed to load notifications');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      router.push(notification.actionUrl);
    }
    
    setIsOpen(false);
  };

  const handleMarkAllAsRead = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasUnread) {
      await markAllAsRead();
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // Reset the error boundary and refetch notifications
        fetchNotifications().catch(console.error);
      }}
    >
      <div className="relative" ref={dropdownRef}>
      <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="relative rounded-full"
            aria-label="Notifications"
          >
            <Bell className="h-5 w-5" />
            {hasUnread && (
              <span className="absolute -right-1 -top-1 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
                {unreadNotifications.length > 9 ? '9+' : unreadNotifications.length}
              </span>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-80 p-0" align="end" forceMount>
          <DropdownMenuLabel className="flex items-center justify-between px-4 py-3">
            <span>Notifications</span>
            <button
              onClick={handleMarkAllAsRead}
              disabled={!hasUnread || notifications.length === 0}
              className={cn(
                "text-xs font-medium focus:outline-none",
                hasUnread && notifications.length > 0
                  ? "text-primary hover:underline"
                  : "text-muted-foreground cursor-not-allowed"
              )}
            >
              Mark all as read
            </button>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div className="max-h-[400px] overflow-hidden">
            <ScrollArea className="h-[400px] w-full">
              <div className="pr-4">
                {isLoading ? (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    Loading notifications...
                  </div>
                ) : error ? (
                  <div className="p-4 text-center text-sm text-destructive">
                    {error}
                  </div>
                ) : !notifications.length ? (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    No notifications
                  </div>
                ) : (
                  <div className="divide-y">
                    {notifications.map((notification) => (
                      <DropdownMenuItem
                        key={notification.id}
                        className={cn(
                          'flex flex-col items-start gap-1 p-3 cursor-pointer',
                          !notification.isRead && 'bg-muted/50',
                          'hover:bg-muted/80 transition-colors rounded-none'
                        )}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex items-start w-full gap-3">
                          <div className="mt-0.5">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between w-full">
                              <h4 className="text-sm font-medium leading-none">
                                {notification.title}
                              </h4>
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {notification.message}
                            </p>
                            {!notification.isRead && (
                              <div className="flex items-center mt-1">
                                <span className="inline-flex items-center text-xs text-blue-500">
                                  <Clock className="h-3 w-3 mr-1" /> New
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
          <DropdownMenuItem
            className="text-center justify-center text-sm font-medium cursor-pointer hover:bg-muted/50 py-2 border-t"
            onClick={() => {
              router.push(`/${locale}/notifications`);
              setIsOpen(false);
            }}
          >
            View all notifications
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      </div>
    </ErrorBoundary>
  );
};

"use client";

import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/app/context/AuthContext";
import { createNotification } from "@/app/services/notificationService";
// Define notification types that match the backend
const NOTIFICATION_TYPES = [
  'task_assigned',
  'task_updated',
  'task_due_soon',
  'task_overdue',
  'task_completed',
  'document_expiring',
  'document_expired',
  'payment_received',
  'payment_due',
  'payment_overdue',
  'system_announcement',
  'maintenance_request',
  'maintenance_update',
  'lease_renewal'
] as const;

type NotificationType = typeof NOTIFICATION_TYPES[number];
import { Loader2, BellPlus } from "lucide-react";
import { useState } from "react";

export default function TestNotificationButton() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const createTestNotification = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create test notifications",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const notificationTypes = [
        'task_assigned',
        'task_updated',
        'task_due_soon',
        'task_overdue',
        'task_completed',
        'document_expiring',
        'document_expired',
        'payment_received',
        'payment_due',
        'payment_overdue',
        'system_announcement',
        'maintenance_request',
        'maintenance_update',
        'lease_renewal'
      ] as const;

      const randomType = notificationTypes[
        Math.floor(Math.random() * notificationTypes.length)
      ] as NotificationType;

      // Map notification types to more readable titles and messages
      const notificationTitles = {
        task_assigned: 'New Task Assigned',
        task_updated: 'Task Updated',
        task_due_soon: 'Task Due Soon',
        task_overdue: 'Task Overdue',
        task_completed: 'Task Completed',
        document_expiring: 'Document Expiring Soon',
        document_expired: 'Document Expired',
        payment_received: 'Payment Received',
        payment_due: 'Payment Due',
        payment_overdue: 'Payment Overdue',
        system_announcement: 'System Announcement',
        maintenance_request: 'New Maintenance Request',
        maintenance_update: 'Maintenance Update',
        lease_renewal: 'Lease Renewal Reminder'
      };

      const notificationMessages = {
        task_assigned: 'A new task has been assigned to you.',
        task_updated: 'A task you are assigned to has been updated.',
        task_due_soon: 'A task is due soon. Please complete it before the deadline.',
        task_overdue: 'A task is now overdue. Please complete it as soon as possible.',
        task_completed: 'A task has been marked as completed.',
        document_expiring: 'A document is about to expire. Please review and renew if necessary.',
        document_expired: 'A document has expired. Please update it as soon as possible.',
        payment_received: 'Your payment has been received. Thank you!',
        payment_due: 'A payment is due soon. Please submit your payment.',
        payment_overdue: 'A payment is now overdue. Please submit it as soon as possible.',
        system_announcement: 'There is an important system announcement.',
        maintenance_request: 'A new maintenance request has been submitted.',
        maintenance_update: 'There is an update to your maintenance request.',
        lease_renewal: 'Your lease is up for renewal soon. Please contact us for more information.'
      };

      const notification = await createNotification({
        userId: user.id,
        type: randomType,
        title: notificationTitles[randomType] || 'New Notification',
        message: notificationMessages[randomType] || 'You have a new notification.',
        entityType: 'USER',
        entityId: user.id,
        actionUrl: '/notifications'
      } as any);
      
      // Dispatch a custom event to refresh the notifications list
      window.dispatchEvent(new Event('notifications:refresh'));

      toast({
        title: "Success",
        description: `Created test ${randomType.toLowerCase()} notification`,
      });

      // Refresh notifications
      window.dispatchEvent(new Event("notifications:refresh"));
    } catch (error) {
      console.error("Failed to create test notification:", error);
      toast({
        title: "Error",
        description: "Failed to create test notification",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={createTestNotification}
      disabled={isLoading}
      className="flex items-center gap-2"
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          Creating...
        </>
      ) : (
        <>
          <BellPlus className="h-4 w-4" />
          Create Test Notification
        </>
      )}
    </Button>
  );
}

"use client";

import React, { useCallback, useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import { format } from 'date-fns';
import { Bell, Check, AlertCircle, FileText, DollarSign, Home, MessageSquare, Trash2 } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { ScrollArea } from '../../components/ui/scroll-area';
import { Skeleton } from '../../components/ui/skeleton';
import { useToast } from '../../components/ui/use-toast';
import { useNotifications } from '../../app/context/NotificationContext';
import { cn } from '../../lib/utils';
import { Notification, NotificationType } from '../../types/notification';

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'ALERT':
      return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    case 'MAINTENANCE':
      return <Home className="h-5 w-5 text-blue-500" />;
    case 'PAYMENT':
      return <DollarSign className="h-5 w-5 text-green-500" />;
    case 'LEASE':
      return <FileText className="h-5 w-5 text-purple-500" />;
    case 'MESSAGE':
      return <MessageSquare className="h-5 w-5 text-cyan-500" />;
    default:
      return <Bell className="h-5 w-5 text-gray-500" />;
  }
};

const NotificationItem: React.FC<{ notification: Notification }> = ({ notification }) => {
  const { markAsRead, deleteNotification } = useNotifications();
  const [isHovered, setIsHovered] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleMarkAsRead = async () => {
    if (!notification.isRead) {
      try {
        await markAsRead(notification.id);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
        toast({
          title: 'Error',
          description: 'Failed to mark notification as read',
          variant: 'destructive',
        });
      }
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteNotification(notification.id);
    } catch (error) {
      console.error('Failed to delete notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete notification',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div 
      className={cn(
        'relative p-4 hover:bg-muted/50 transition-colors',
        !notification.isRead && 'bg-muted/30',
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start gap-3">
        <div className="mt-1">
          {getNotificationIcon(notification.type as NotificationType)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm">{notification.title}</h3>
            <div className="text-xs text-muted-foreground">
              {format(new Date(notification.createdAt), 'MMM d, yyyy')}
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            {notification.message}
          </p>
          {notification.actionUrl && (
            <Button variant="link" size="sm" className="h-auto p-0 mt-1" asChild>
              <a href={notification.actionUrl} onClick={handleMarkAsRead}>
                View details
              </a>
            </Button>
          )}
        </div>
        <div className="flex gap-2">
          {!notification.isRead && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleMarkAsRead}
              title="Mark as read"
            >
              <Check className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-destructive hover:text-destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            title="Delete notification"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const NotificationsList: React.FC = () => {
  const { state, fetchNotifications } = useNotifications();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0,
    triggerOnce: false,
  });
  const { toast } = useToast();

  const refreshNotifications = useCallback(async () => {
    try {
      await fetchNotifications({ page: 1, limit: state.pagination.limit });
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      toast({
        title: 'Error',
        description: 'Failed to load notifications',
        variant: 'destructive',
      });
    }
  }, [fetchNotifications, state.pagination.limit, toast]);

  useEffect(() => {
    // Initial load
    refreshNotifications();

    // Add event listener for refresh events
    const handleRefresh = () => {
      refreshNotifications();
    };

    window.addEventListener('notifications:refresh', handleRefresh);
    
    // Cleanup
    return () => {
      window.removeEventListener('notifications:refresh', handleRefresh);
    };
  }, [refreshNotifications]);

  // Load more when scrolled to bottom
  useEffect(() => {
    if (inView && !isLoadingMore && state.pagination.page < state.pagination.totalPages) {
      const loadMore = async () => {
        try {
          setIsLoadingMore(true);
          await fetchNotifications({
            page: state.pagination.page + 1,
            limit: state.pagination.limit,
          });
        } catch (error) {
          console.error('Failed to load more notifications:', error);
          toast({
            title: 'Error',
            description: 'Failed to load more notifications',
            variant: 'destructive',
          });
        } finally {
          setIsLoadingMore(false);
        }
      };

      loadMore();
    }
  }, [inView, isLoadingMore, state.pagination, fetchNotifications, toast]);


  if (state.isLoading && state.notifications.length === 0) {
    return (
      <div className="p-4 space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-start space-x-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="p-4 border-b">
        <h2 className="text-lg font-medium">Notifications</h2>
        <p className="text-sm text-muted-foreground">
          {state.pagination.total} total • {state.notifications.filter(n => !n.isRead).length} unread
        </p>
      </div>
      
      {state.notifications.length === 0 ? (
        <div className="p-8 text-center">
          <Bell className="h-12 w-12 mx-auto text-muted-foreground/30 mb-4" />
          <h3 className="text-lg font-medium">No notifications yet</h3>
          <p className="text-muted-foreground mt-1">
            When you get notifications, they'll appear here.
          </p>
        </div>
      ) : (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="divide-y">
            {state.notifications.map((notification) => (
              <NotificationItem key={notification.id} notification={notification} />
            ))}
            {isLoadingMore && (
              <div className="flex justify-center p-4">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            )}
            <div ref={ref} className="h-1" />
          </div>
        </ScrollArea>
      )}
    </>
  );
};

export default NotificationsList;

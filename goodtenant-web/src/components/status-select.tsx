'use client';

import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Check, Circle, X } from 'lucide-react'; 

// Status icons with colors
const statusIcons = {
  active: <Circle className="h-3 w-3 fill-green-500 text-green-500 mr-2" />,
  inactive: <X className="h-3 w-3 text-red-500 mr-2" />,
  all: <Circle className="h-3 w-3 fill-muted-foreground/20 text-muted-foreground mr-2" />
};

export interface StatusSelectProps {
  /** Current value of the select */
  value?: string;
  /** Callback when value changes */
  onValueChange: (value: string | undefined) => void;
  /** Label to display above the select */
  label?: string;
  /** Placeholder text when no value is selected */
  placeholder?: string;
  /** Whether the select is disabled */
  disabled?: boolean;
  /** Additional class name */
  className?: string;
  /** Whether to show the "All Statuses" option */
  showAllOption?: boolean;
  /** Whether to show the label */
  showLabel?: boolean;
  /** Custom label class name */
  labelClassName?: string;
}

/**
 * A reusable select input for selecting status values (active/inactive).
 * Can be used both in forms and as a filter component.
 */
export function StatusSelect({
  value,
  onValueChange,
  label,
  placeholder,
  disabled = false,
  className,
  showAllOption = false,
  showLabel = true,
  labelClassName,
}: StatusSelectProps) {
  const t = useTranslations();

  const handleChange = (val: string) => {
    onValueChange(val === 'all' ? undefined : val);
  };

  return (
    <div className={cn('space-y-2 w-full', className)}>
      {showLabel && label && (
        <Label className={cn('text-sm font-medium text-foreground', labelClassName)}>
          {label}
        </Label>
      )}
      <Select
        value={value || (showAllOption ? 'all' : '')}
        onValueChange={handleChange}
        disabled={disabled}
      >
        <SelectTrigger className={cn(
          'flex items-center justify-between w-full',
          'h-10',
          disabled && 'opacity-50 cursor-not-allowed',
          !value && showAllOption && 'text-muted-foreground',
          'bg-transparent' // Let parent handle background and border
        )}>
          <SelectValue>
            {value ? (
              <div className="flex items-center">
                {statusIcons[value as keyof typeof statusIcons] || statusIcons.all}
                <span>{value === 'active' ? t('active') : t('inactive')}</span>
              </div>
            ) : (
              <span className="text-muted-foreground">
                {placeholder || t('selectStatus')}
              </span>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {showAllOption && (
            <SelectItem value="all">
              <div className="flex items-center">
                {statusIcons.all}
                <span>{t('allStatuses')}</span>
              </div>
            </SelectItem>
          )}
          <SelectItem value="active">
            <div className="flex items-center">
              {statusIcons.active}
              <span>Active</span>
            </div>
          </SelectItem>
          <SelectItem value="inactive">
            <div className="flex items-center">
              {statusIcons.inactive}
              <span>Inactive</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}

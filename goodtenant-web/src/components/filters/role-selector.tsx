'use client';

import * as React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RoleFilter } from '@/types/user';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, ChevronDown, X, LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RoleSelectorProps {
  selectedRole: RoleFilter;
  onRoleChange: (role: RoleFilter) => void;
  roles: { 
    value: RoleFilter; 
    label: string; 
    icon?: React.ReactElement<{ className?: string }> 
  }[];
  isLoading?: boolean;
  className?: string;
  triggerClassName?: string;
  placeholder?: string;
  disabled?: boolean;
  showClear?: boolean;
}

const getRoleVariant = (role: RoleFilter) => {
  switch (role) {
    case 'account_owner':
      return 'bg-purple-500';
    case 'property_manager':
      return 'bg-blue-500';
    case 'leasing_agent':
      return 'bg-green-500';
    case 'maintenance_staff':
      return 'bg-amber-500';
    case 'tenant':
      return 'bg-gray-500';
    default:
      return 'bg-gray-500';
  }
};

const getRoleIconColor = (role: RoleFilter) => {
  switch (role) {
    case 'account_owner':
      return 'text-purple-500 dark:text-purple-400';
    case 'property_manager':
      return 'text-blue-500 dark:text-blue-400';
    case 'leasing_agent':
      return 'text-green-500 dark:text-green-400';
    case 'maintenance_staff':
      return 'text-amber-500 dark:text-amber-400';
    case 'tenant':
      return 'text-gray-500 dark:text-gray-400';
    default:
      return 'text-muted-foreground';
  }
};

export function RoleSelector({
  selectedRole,
  onRoleChange,
  roles,
  isLoading = false,
  className = '',
  triggerClassName = '',
  placeholder = 'Select role',
  disabled = false,
  showClear = true,
}: RoleSelectorProps) {
  const selectedRoleData = roles.find(role => role.value === selectedRole);
  const isClearable = showClear && selectedRole !== 'all' && !disabled;
  
  // Helper function to render icon with proper typing
  const renderIcon = (icon: React.ReactElement, role: RoleFilter) => {
    const iconProps = {
      className: cn('h-4 w-4', getRoleIconColor(role)),
    };
    
    return React.cloneElement(icon, iconProps);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRoleChange('all');
  };

  const handleValueChange = (value: string) => {
    if (value === 'all' || roles.some(role => role.value === value)) {
      onRoleChange(value as RoleFilter);
    }
  };

  return (
    <div className={cn('relative', className)}>
      <Select
        value={selectedRole}
        onValueChange={handleValueChange}
        disabled={disabled || isLoading}
      >
        <SelectTrigger 
          className={cn(
            'w-[180px] bg-card',
            'focus:ring-2 focus:ring-ring focus:ring-offset-0',
            'relative',
            {
              'opacity-70': disabled,
              'cursor-not-allowed': disabled,
              'bg-muted/50': disabled,
            },
            triggerClassName
          )}
        >
          <div className="flex items-center w-full">
            <Badge 
              variant="outline" 
              className={cn(
                'mr-2 h-2 w-2 rounded-full p-0',
                getRoleVariant(selectedRole as RoleFilter)
              )} 
            />
            <SelectValue asChild>
              <span className="truncate">
                {selectedRoleData?.label || placeholder}
              </span>
            </SelectValue>
            
            {isLoading && (
              <span className="absolute right-8">
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              </span>
            )}
          </div>
        </SelectTrigger>
        
        <SelectContent>
          {roles.map((role) => (
            <SelectItem 
              key={role.value} 
              value={role.value}
              className="cursor-pointer"
            >
              <div className="flex items-center">
                <Badge 
                  variant="outline" 
                  className={cn(
                    'mr-2 h-2 w-2 rounded-full p-0',
                    getRoleVariant(role.value as RoleFilter)
                  )} 
                />
                {role.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {isClearable && !isLoading && (
        <button
          type="button"
          onClick={handleClear}
          className={cn(
            'absolute right-7 top-1/2 -translate-y-1/2',
            'flex items-center justify-center',
            'h-5 w-5 rounded-full',
            'text-muted-foreground hover:text-foreground',
            'hover:bg-muted',
            'transition-colors duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'dark:focus:ring-primary/30',
            'disabled:opacity-50 disabled:pointer-events-none'
          )}
          disabled={disabled}
          aria-label="Clear selection"
        >
          <X className="h-3.5 w-3.5" />
        </button>
      )}
    </div>
  );
}

const TOKEN_KEYS = {
  ACCESS_TOKEN: 'gt_access_token',
  REFRESH_TOKEN: 'gt_refresh_token',
} as const;

type TokenType = 'access' | 'refresh';

export interface Tokens {
  accessToken: string;
  refreshToken: string;
}

export const tokenManager = {
  /**
   * Get token from storage
   */
  getToken(type: TokenType): string | null {
    if (typeof window === 'undefined') return null;
    const key = type === 'access' ? TOKEN_KEYS.ACCESS_TOKEN : TOKEN_KEYS.REFRESH_TOKEN;
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.error(`Failed to get ${type} token:`, error);
      return null;
    }
  },

  /**
   * Set token in storage
   */
  setToken(type: TokenType, token: string): void {
    if (typeof window === 'undefined') return;
    const key = type === 'access' ? TOKEN_KEYS.ACCESS_TOKEN : TOKEN_KEYS.REFRESH_TOKEN;
    try {
      localStorage.setItem(key, token);
    } catch (error) {
      console.error(`Failed to set ${type} token:`, error);
    }
  },

  /**
   * Store both access and refresh tokens
   */
  storeTokens(tokens: Tokens): void {
    if (!tokens?.accessToken || !tokens?.refreshToken) {
      console.error('Invalid tokens provided');
      return;
    }
    this.setToken('access', tokens.accessToken);
    this.setToken('refresh', tokens.refreshToken);
  },

  /**
   * Remove tokens from storage
   */
  clearTokens(): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.getToken('access');
  },

  /**
   * Get all stored tokens
   */
  getTokens(): Tokens | null {
    const accessToken = this.getToken('access');
    const refreshToken = this.getToken('refresh');
    
    if (!accessToken || !refreshToken) {
      return null;
    }
    
    return { accessToken, refreshToken };
  }
};

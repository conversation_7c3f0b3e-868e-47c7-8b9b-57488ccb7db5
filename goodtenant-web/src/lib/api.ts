import axios, { AxiosError, InternalAxiosRequestConfig, AxiosRequestConfig } from 'axios';
import { tokenManager } from './tokenManager';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api';

// Extend the AxiosRequestConfig to include _retry
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  _retry?: boolean;
}

// Helper function to handle logout
function handleLogout(error?: any) {
  // Only clear tokens and redirect if this is a client-side request
  if (typeof window !== 'undefined') {
    console.log('Clearing tokens and redirecting to login...');
    tokenManager.clearTokens();
    
    // Get current path and locale
    const currentPath = window.location.pathname + window.location.search;
    const pathParts = window.location.pathname.split('/').filter(Boolean);
    const possibleLocale = pathParts[0];
    const supportedLocales = ['en', 'fr', 'es'];
    const locale = supportedLocales.includes(possibleLocale) ? possibleLocale : 'en';
    
    // Store the current path to redirect back after login (without locale)
    const pathWithoutLocale = pathParts.slice(1).join('/');
    const returnTo = pathWithoutLocale ? `/${pathWithoutLocale}${window.location.search}` : '/';
    
    console.log('Redirecting to login, will return to:', returnTo);
    window.location.replace(`/${locale}/auth/login?returnTo=${encodeURIComponent(returnTo)}`);
  }
  
  return Promise.reject({
    message: 'Your session has expired. Please log in again.',
    isAuthError: true,
    code: 'SESSION_EXPIRED',
    originalError: error
  });
}

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Important for cookies if using them in the future
});

// Create a separate API client for Stripe webhooks that doesn't include auth headers
export const stripeApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = tokenManager.getToken('access');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

interface ApiErrorResponse {
  message?: string;
  [key: string]: any; // For any additional properties
}

interface ApiError extends Error {
  response?: {
    status: number;
    data: ApiErrorResponse;
  };
  request?: any;
  config?: any;
  isAxiosError: boolean;
}

// Helper function to check if the request is for user creation
const isUserCreationRequest = (url: string | undefined) => {
  return url?.endsWith('/users') || url?.includes('/users/');
};

// Add a response interceptor to handle common errors and token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;
    const errorData = error.response?.data as any;
    
    // Skip token refresh for user creation endpoint
    if (isUserCreationRequest(originalRequest.url)) {
      return Promise.reject(error);
    }
    
    // Skip handling for login requests to prevent redirect loops
    if (originalRequest.url?.includes('/auth/login')) {
      return Promise.reject(error);
    }
    
    // Handle 401 Unauthorized responses or 500 with Invalid token message
    if (error.response?.status === 401 || 
        (error.response?.status === 500 && errorData?.message === 'Invalid token')) {
      // Check if this is a retry attempt
      if (originalRequest._retry) {
        console.log('Already attempted token refresh, redirecting to login');
        return handleLogout();
      }

      console.log('Attempting to refresh token...');
      originalRequest._retry = true;
      
      try {
        const refreshToken = tokenManager.getToken('refresh');
        const accessToken = tokenManager.getToken('access');
        console.log('Current tokens - Access:', !!accessToken, 'Refresh:', !!refreshToken);
        
        if (!refreshToken) {
          console.error('No refresh token available');
          return handleLogout();
        }
        
        // Create a new axios instance without interceptors to prevent infinite loops
        console.log('Sending refresh token request to /auth/refresh-token...');
        
        // Try with Bearer token first
        const refreshApi = axios.create({
          baseURL: API_BASE_URL,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${refreshToken}`
          }
        });
        
        let response;
        try {
          response = await refreshApi.post<{
            success: boolean;
            message?: string;
            data?: {
              accessToken?: string;
              refreshToken?: string;
            };
          }>('/auth/refresh-token', {});
          
          console.log('Refresh token response (Bearer):', {
            status: response.status,
            success: response.data.success,
            message: response.data.message,
            hasAccessToken: !!response.data.data?.accessToken,
            hasRefreshToken: !!response.data.data?.refreshToken
          });
        } catch (error) {
          console.error('Failed to refresh token with Bearer:', error);
          
          // Fallback to sending refresh token in request body
          try {
            console.log('Trying to refresh token with token in request body...');
            const fallbackApi = axios.create({
              baseURL: API_BASE_URL,
              headers: {
                'Content-Type': 'application/json'
              }
            });
            
            response = await fallbackApi.post<{
              success: boolean;
              message?: string;
              data?: {
                accessToken?: string;
                refreshToken?: string;
              };
            }>('/auth/refresh-token', { refreshToken });
            
            console.log('Refresh token response (Body):', {
              status: response.status,
              success: response.data.success,
              message: response.data.message,
              hasAccessToken: !!response.data.data?.accessToken,
              hasRefreshToken: !!response.data.data?.refreshToken
            });
          } catch (fallbackError) {
            console.error('Failed to refresh token with token in body:', fallbackError);
            throw fallbackError;
          }
        }

        if (response.data.success && response.data.data?.accessToken) {
          const { accessToken, refreshToken: newRefreshToken } = response.data.data;
          
          // Store the new tokens
          tokenManager.setToken('access', accessToken);
          if (newRefreshToken) {
            tokenManager.setToken('refresh', newRefreshToken);
          }
          
          console.log('Successfully refreshed tokens');
          
          // Update the original request with the new token
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          }
          
          // Retry the original request
          console.log('Retrying original request with new token...');
          return api(originalRequest);
        }
        
        console.error('Invalid response format from refresh endpoint');
        return handleLogout();
      } catch (refreshError) {
        console.error('Failed to refresh token:', refreshError);
        return handleLogout(refreshError);
      }
    }

    // Handle other errors
    if ((error as ApiError).response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const apiError = error as ApiError;
      console.error('API Error:', apiError.response?.data);
      return Promise.reject({
        message: apiError.response?.data?.message || 'An error occurred',
        status: apiError.response?.status,
        data: apiError.response?.data,
      });
    } else if ((error as ApiError).request) {
      // The request was made but no response was received
      console.error('API Error: No response received');
      return Promise.reject({
        message: 'No response from server. Please check your connection.',
      });
    }
    
    // Something happened in setting up the request that triggered an Error
    console.error('API Error:', error.message);
    return Promise.reject({
      message: error.message || 'An unexpected error occurred',
    });
  }
);

// Helper function to handle API errors
export const handleApiError = (error: ApiError): string => {
  if (error.response) {
    // Server responded with an error status code (4xx, 5xx)
    return error.response.data?.message || 'An error occurred';
  } else if (error.request) {
    // Request was made but no response was received
    return 'Unable to connect to the server. Please check your internet connection.';
  } else {
    // Something else happened while setting up the request
    return error.message || 'An unexpected error occurred';
  }
};

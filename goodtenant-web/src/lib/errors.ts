/**
 * Custom API error class that extends the native Error class
 * to include HTTP status codes and additional error details
 */
export class ApiError extends Error {
  statusCode: number;
  details?: Record<string, unknown>;

  constructor(
    message: string,
    statusCode: number = 500,
    details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.details = details;
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }

  /**
   * Creates an ApiError from an Axios error
   * @param error The error object from Axios
   * @param defaultMessage Default message if none is provided in the error response
   * @returns A new ApiError instance
   */
  static fromAxiosError(
    error: any,
    defaultMessage: string = 'An API error occurred'
  ): ApiError {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      return new ApiError(
        error.response.data?.message || defaultMessage,
        error.response.status,
        error.response.data
      );
    } else if (error.request) {
      // The request was made but no response was received
      return new ApiError('No response received from server', 0);
    } else {
      // Something happened in setting up the request that triggered an Error
      return new ApiError(error.message || defaultMessage, 0);
    }
  }
}

/**
 * Error class for validation errors (status 400)
 */
export class ValidationError extends ApiError {
  constructor(message: string = 'Validation failed', details?: Record<string, unknown>) {
    super(message, 400, details);
    this.name = 'ValidationError';
  }
}

/**
 * Error class for unauthorized access (status 401)
 */
export class UnauthorizedError extends ApiError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
    this.name = 'UnauthorizedError';
  }
}

/**
 * Error class for forbidden access (status 403)
 */
export class ForbiddenError extends ApiError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
    this.name = 'ForbiddenError';
  }
}

/**
 * Error class for not found resources (status 404)
 */
export class NotFoundError extends ApiError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

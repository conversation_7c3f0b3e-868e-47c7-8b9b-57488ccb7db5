// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './src/i18n/request';

console.log('[Middleware] Middleware module loaded');

// Define public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/tenant-onboarding',
  '/_next',
  '/api',
  '/favicon.ico',
  // Add other public routes here
];

// Configure next-intl middleware
const intlMiddleware = createIntlMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always'
});

export default function middleware(request: NextRequest) {
  console.log(`[Middleware] Request for: ${request.url}`);
  const { pathname } = request.nextUrl;
  
  // Special case for root path - always redirect to default locale
  if (pathname === '/') {
    console.log(`[Middleware] Root path detected, redirecting to /${defaultLocale}`);
    return NextResponse.redirect(new URL(`/${defaultLocale}`, request.url));
  }

  // Check if path doesn't start with a valid locale
  const pathSegments = pathname.split('/');
  const firstSegment = pathSegments[1];
  
  // Handle paths that don't start with a valid locale
  if (firstSegment && !locales.includes(firstSegment as 'en' | 'es')) {
    console.log(`[Middleware] Redirecting non-localized path to default locale: ${pathname}`);
    const redirectUrl = new URL(`/${defaultLocale}${pathname}`, request.url);
    return NextResponse.redirect(redirectUrl);
  }
  
  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => {
    // Check for exact matches or path starts with the public route
    const isMatch = 
      pathname.startsWith(`/${defaultLocale}${route}`) || 
      pathname === `/${defaultLocale}` ||
      pathname.startsWith(route) ||
      // Special case for tenant-onboarding with dynamic segments
      (route === '/tenant-onboarding' && pathname.startsWith(`/${defaultLocale}/tenant-onboarding/`)) ||
      pathname.startsWith('/tenant-onboarding/');
    
    return isMatch;
  });

  // Handle public routes
  if (isPublicRoute) {
    const response = intlMiddleware(request);
    // Add a header to indicate this is a public route
    response.headers.set('x-public-route', 'true');
    return response;
  }

  // Get the token from cookies
  const token = request.cookies.get('auth-token')?.value;
  
  // If no token and not a public route, redirect to login
  if (!token) {
    const loginUrl = new URL(`/${defaultLocale}/login`, request.url);
    loginUrl.searchParams.set('callbackUrl', encodeURI(request.url));
    return NextResponse.redirect(loginUrl);
  }

  // Continue with i18n middleware for authenticated routes
  return intlMiddleware(request);
}

export const config = {
  // Match all pathnames except for
  // - API routes: /api/...
  // - Static files: /_next/...
  // - Files with extensions: /file.txt
  // But explicitly include the root path
  matcher: ['/', '/((?!api|_next|.*\\..*).*)']
};
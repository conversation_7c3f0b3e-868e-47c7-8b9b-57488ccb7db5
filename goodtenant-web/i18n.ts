import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';
import { locales } from './src/i18n/request';

type Locale = (typeof locales)[number];

// Validate that the incoming `locale` parameter is valid
function isValidLocale(locale: string | undefined): locale is Locale {
  return locale ? locales.includes(locale as Locale) : false;
}

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!isValidLocale(locale)) {
    notFound();
    return { locale: 'en', messages: {} };
  }

  const messages = (await import(`./src/messages/${locale}/index.ts`)).default;
  
  return {
    locale,
    messages,
    timeZone: 'UTC',
    now: new Date()
  };
});

# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY .npmrc* ./

# Install dependencies (including dev dependencies for building)
RUN npm ci || npm install

# Copy source code
COPY . .

# Production stage
FROM node:18-alpine AS production

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY .npmrc* ./

# Install only production dependencies
RUN npm ci --only=production || npm install --only=production

# Copy built application from builder
COPY --from=builder /app .

# Set environment to production
ENV NODE_ENV=production
ENV NODE_OPTIONS=--experimental-modules

# Expose port
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node healthcheck.js || exit 1

# Start the application
CMD ["npm", "start"]

# Development stage
FROM node:18-alpine AS development

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY .npmrc* ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Enable ES modules and watch for changes
ENV NODE_OPTIONS=--experimental-modules
ENV NODE_ENV=development

# Expose port
EXPOSE 4000

# Start the application with nodemon for development
CMD ["npm", "run", "dev"]

{"name": "goodtenant-server", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/server.js", "dev": "nodemon -r dotenv/config src/server.js dotenv_config_path=.env"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.821.0", "@aws-sdk/node-http-handler": "^3.370.0", "@aws-sdk/s3-request-presigner": "^3.821.0", "@date-fns/tz": "^1.2.0", "@pdf-lib/fontkit": "^1.1.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mailgun.js": "^12.0.2", "morgan": "^1.10.0", "multer": "^2.0.0", "mysql2": "^3.14.1", "node-cron": "^4.1.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "sequelize": "^6.37.7", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "yamljs": "^0.3.0"}, "devDependencies": {"eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "supertest": "^7.1.1"}}
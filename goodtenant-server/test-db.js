require('dotenv').config();
const mysql = require('mysql2/promise');

// Log the connection details (without password)
console.log('Attempting to connect with:', {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD ? '***' : 'not set'
});

async function testConnection() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT, 10),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      // Add these options for better error handling
      connectTimeout: 10000,
      ssl: false,
      // Try different auth methods
      authPlugins: {
        mysql_clear_password: () => () => {
          return Buffer.from(process.env.DB_PASSWORD + '\0');
        }
      }
    });

    console.log('Successfully connected to MySQL!');
    const [rows] = await connection.execute('SELECT 1+1 as result');
    console.log('Test query result:', rows[0].result);
  } catch (error) {
    console.error('Connection failed:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      fatal: error.fatal
    });
  } finally {
    if (connection) await connection.end();
  }
}

testConnection();
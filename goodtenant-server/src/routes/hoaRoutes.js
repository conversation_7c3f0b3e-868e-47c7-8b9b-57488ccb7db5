const express = require('express');
const router = express.Router();
const hoaController = require('../controllers/hoaController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/hoas - Get all HOAs
router.get('/', hoaController.getHOAs);

// GET /api/hoas/:id - Get HOA by ID
router.get('/:id', hoaController.getHOAById);

// POST /api/hoas - Create a new HOA
router.post('/', hoaController.createHOA);

// PUT /api/hoas/:id - Update an HOA
router.put('/:id', hoaController.updateHOA);

// DELETE /api/hoas/:id - Delete an HOA
router.delete('/:id', hoaController.deleteHOA);

// GET /api/hoas/:id/properties - Get properties for an HOA
router.get('/:id/properties', hoaController.getHOAProperties);

module.exports = router;

// src/routes/inventoryRoutes.js
const express = require('express');
const router = express.Router();
const inventoryController = require('../controllers/inventoryController');
const { authenticate, authorize } = require('../middlewares/auth');
const { ROLES } = require('../constants/roles');

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route   POST /api/inventory
 * @desc    Create a new inventory item
 * @access  Private
 */
router.post('/', inventoryController.createInventoryItem);

/**
 * @route   GET /api/inventory
 * @desc    Get all inventory items with optional filtering
 * @access  Private
 */
router.get('/', inventoryController.getInventoryItems);

/**
 * @route   GET /api/inventory/:id
 * @desc    Get a single inventory item by ID
 * @access  Private
 */
router.get('/:id', inventoryController.getInventoryItemById);

/**
 * @route   PUT /api/inventory/:id
 * @desc    Update an inventory item
 * @access  Private
 */
router.put('/:id', inventoryController.updateInventoryItem);

/**
 * @route   DELETE /api/inventory/:id
 * @desc    Delete an inventory item (admin only)
 * @access  Private/Admin
 */
router.delete('/:id', authorize(ROLES.ADMIN), inventoryController.deleteInventoryItem);

// Nested route: Get inventory items by property ID
/**
 * @route   GET /api/properties/:propertyId/inventory
 * @desc    Get inventory items for a specific property
 * @access  Private
 */
router.get('/properties/:propertyId/inventory', inventoryController.getInventoryItemsByPropertyId);

module.exports = router;
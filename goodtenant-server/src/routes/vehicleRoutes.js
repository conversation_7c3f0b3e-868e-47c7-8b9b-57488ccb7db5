const express = require('express');
const router = express.Router();
const vehicleController = require('../controllers/vehicleController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// Create a new vehicle for the authenticated user
router.post('/', vehicleController.createVehicle);

// Get all vehicles for the authenticated user with pagination and search
router.get('/', vehicleController.getUserVehicles);

// Get a single vehicle by ID (must belong to the authenticated user)
router.get('/:id', vehicleController.getVehicleById);

// Update a vehicle (must belong to the authenticated user)
router.put('/:id', vehicleController.updateVehicle);

// Delete a vehicle (must belong to the authenticated user)
router.delete('/:id', vehicleController.deleteVehicle);


module.exports = router;

const express = require('express');
const router = express.Router();
const insuranceController = require('../controllers/insuranceController');
const { authenticate, authorize } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/insurances - Get all insurances with optional filtering
router.get('/', insuranceController.getInsurances);

// GET /api/insurances/:id - Get insurance by ID
router.get('/:id', insuranceController.getInsuranceById);

// POST /api/insurances - Create a new insurance
router.post('/', insuranceController.createInsurance);

// PUT /api/insurances/:id - Update an insurance
router.put('/:id', insuranceController.updateInsurance);

// DELETE /api/insurances/:id - Delete an insurance (soft delete)
router.delete('/:id', insuranceController.deleteInsurance);

// Nested route under properties
// GET /api/properties/:propertyId/insurances - Get insurances for a specific property
router.get('/properties/:propertyId', insuranceController.getPropertyInsurances);

module.exports = router;

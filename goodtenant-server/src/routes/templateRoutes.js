const express = require('express');
const router = express.Router();
const templateController = require('../controllers/templateController');
const { authenticate, authorize } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/templates - Get all templates (with optional query params: page, limit, search, type, isActive)
// Automatically filters by the user's account
router.get('/', templateController.getTemplates);

// GET /api/templates/variables - Get all available system variables
router.get('/variables', templateController.getSystemVariables);

// POST /api/templates - Create a new template
router.post('/', templateController.createTemplate);

// GET /api/templates/:id - Get template by ID
router.get('/:id', templateController.getTemplateById);

// PUT /api/templates/:id - Update a template
router.put('/:id', templateController.updateTemplate);

// DELETE /api/templates/:id - Delete a template
router.delete('/:id', templateController.deleteTemplate);

// POST /api/templates/:id/generate - Generate a document from a template
router.post('/:id/generate', templateController.generateDocument);

module.exports = router;

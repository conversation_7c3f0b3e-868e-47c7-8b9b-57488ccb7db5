const express = require('express');
const router = express.Router();
const { authenticate } = require('../middlewares/auth');
const accountStatsController = require('../controllers/accountStatsController');

// Protect all routes with authentication
router.use(authenticate);

/**
 * @route   GET /api/account/stats
 * @desc    Get account statistics
 * @access  Private
 */
router.get('/stats', accountStatsController.getAccountStats);

module.exports = router;

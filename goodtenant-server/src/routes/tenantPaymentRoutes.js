const express = require('express');
const router = express.Router();
const tenantPaymentController = require('../controllers/tenantPaymentController');
const { authenticate } = require('../middlewares/auth');

/**
 * @desc    Tenant payment status routes
 * @route   /api/tenant-payment
 */

// Apply authentication to all routes
router.use(authenticate);

// GET /api/tenant-payment/status - Get payment status for current user (Tenant)
router.get(
  '/status',
  tenantPaymentController.getMyPaymentStatus
);

// GET /api/tenant-payment/tenant/:tenantId - Get payment status for a specific tenant (Admin/Manager)
router.get(
  '/tenant/:tenantId',
  tenantPaymentController.getTenantPaymentStatus
);

module.exports = router;

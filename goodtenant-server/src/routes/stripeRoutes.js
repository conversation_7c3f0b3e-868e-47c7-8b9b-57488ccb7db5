const express = require('express');
const router = express.Router();
const stripeController = require('../controllers/stripeController');
const { authenticate } = require('../middlewares/auth');
/**
 * @route GET /api/stripe/config
 * @desc Get the Stripe publishable key for an account
 * @access Private
 */
// Apply authentication middleware to all routes
router.use(authenticate);

router.get('/config', stripeController.getStripePublishableKey);

/**
 * @route PUT /api/stripe/config/:accountId
 * @desc Update Stripe configuration for an account
 * @access Admin only
 */
router.put('/config/:accountId', stripeController.updateStripeConfig
);

module.exports = router;

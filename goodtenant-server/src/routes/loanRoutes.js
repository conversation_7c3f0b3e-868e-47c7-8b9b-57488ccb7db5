const express = require('express');
const router = express.Router();
const loanController = require('../controllers/loanController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/loans - Get all loans (with optional query params: page, limit, search, propertyId)
router.get('/', loanController.getLoans);

// POST /api/loans - Create a new loan
router.post('/', loanController.createLoan);

// GET /api/loans/:id - Get loan by ID
router.get('/:id', loanController.getLoanById);

// PUT /api/loans/:id - Update a loan
router.put('/:id', loanController.updateLoan);

// DELETE /api/loans/:id - Delete a loan
router.delete('/:id', loanController.deleteLoan);

// GET /api/properties/:propertyId/loans - Get loans by property ID (with optional query params: page, limit)
router.get('/properties/:propertyId', loanController.getLoansByPropertyId);

module.exports = router;
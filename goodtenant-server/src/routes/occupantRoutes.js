const express = require('express');
const router = express.Router();
const occupantController = require('../controllers/occupantController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// POST /api/leases/:leaseId/occupants - Create a new occupant for a lease
router.post('/leases/:leaseId/occupants', occupantController.createOccupant);

// GET /api/leases/:leaseId/occupants - Get all occupants for a lease
router.get('/leases/:leaseId/occupants', occupantController.getOccupantsByLease);

// GET /api/occupants/:id - Get a single occupant by ID
router.get('/occupants/:id', occupantController.getOccupantById);

// PUT /api/occupants/:id - Update an occupant
router.put('/occupants/:id', occupantController.updateOccupant);

// DELETE /api/occupants/:id - Delete an occupant
router.delete('/occupants/:id', occupantController.deleteOccupant);

module.exports = router;

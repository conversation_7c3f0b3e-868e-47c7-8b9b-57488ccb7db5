const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const UserController = require('../controllers/userController');
const { authenticate, authorize } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// Get current user profile
router.get('/me', UserController.getCurrentUser);

// Update current user profile
router.put(
  '/me',
  [
    body('firstName').optional().trim().notEmpty(),
    body('lastName').optional().trim().notEmpty(),
    body('phone').optional().trim().isMobilePhone(),
    body('avatar').optional().trim().isURL()
  ],
  UserController.updateProfile
);

// Change password
router.post(
  '/me/change-password',
  [
    body('currentPassword').notEmpty(),
    body('newPassword').isLength({ min: 8 })
  ],
  UserController.changePassword
);

// Protected routes (admin only)
router.use(authorize(['account_owner']));

// Get all users (with pagination)
router.get(
  '/',
  [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('role').optional().isInt().toInt(),
    query('status').optional().isString(),
    query('search').optional().trim()
  ],
  UserController.getAllUsers
);

// Create new user
router.post(
  '/',
  [
    body('firstName').trim().notEmpty(),
    body('lastName').trim().notEmpty(),
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }),
    body('phone').optional().trim().isMobilePhone(),
    body('roleIds').optional().isArray(),
    body('roleIds.*').isInt().toInt()
  ],
  UserController.createUser
);

// Get user by ID
router.get(
  '/:id',
  [param('id').isInt().toInt()],
  UserController.getUserById
);

// Update user
router.put(
  '/:id',
  [
    param('id').isInt().toInt(),
    body('firstName').optional().trim().notEmpty(),
    body('lastName').optional().trim().notEmpty(),
    body('email').optional().isEmail().normalizeEmail(),
    body('phone').optional().trim().isMobilePhone(),
    body('roleIds').optional().isArray(),
    body('roleIds.*').optional().isInt().toInt()
  ],
  UserController.updateUser
);

// Delete user
router.delete(
  '/:id',
  [param('id').isInt().toInt()],
  UserController.deleteUser
);

module.exports = router;

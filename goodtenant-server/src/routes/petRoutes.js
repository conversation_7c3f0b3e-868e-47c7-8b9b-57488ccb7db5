const express = require('express');
const router = express.Router();
const petController = require('../controllers/petController');
const { authenticate } = require('../middlewares/auth');



// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/pets - Get all pets for the authenticated user
router.get('/', petController.getPets);

// GET /api/pets/:id - Get a pet by ID
router.get('/:id', petController.getPetById);

// POST /api/pets - Create a new pet
router.post('/', petController.createPet);

// PUT /api/pets/:id - Update a pet
router.put('/:id', petController.updatePet);

// DELETE /api/pets/:id - Delete a pet
router.delete('/:id', petController.deletePet);

module.exports = router;

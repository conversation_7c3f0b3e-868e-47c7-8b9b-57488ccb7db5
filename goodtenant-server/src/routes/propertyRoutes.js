const express = require('express');
const router = express.Router();
const propertyController = require('../controllers/propertyController');

// Apply authentication middleware to all routes
const { authenticate } = require('../middlewares/auth');
router.use(authenticate);

// Property routes
router.post('/', propertyController.createProperty);
router.get('/user/me', propertyController.getUserProperties);
router.get('/account', propertyController.getPropertiesByAccount);
router.get('/:id', propertyController.getPropertyById);
router.put('/:id', propertyController.updateProperty);
router.delete('/:id', propertyController.deleteProperty);

// Property user management routes
router.get('/:propertyId/users', propertyController.getPropertyUsers);
router.post('/:propertyId/users', propertyController.addUserToProperty);
router.delete('/:propertyId/users/:userId', propertyController.removeUserFromProperty);

module.exports = router;

const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController');
const { authenticate, authorize } = require('../middlewares/auth');
const { upload, handleUploadErrors } = require('../middlewares/upload');

// Upload a file for a model (HOA, Insurance, Loan, Tax)
router.post(
  '/:modelName/:modelId/upload',
  authenticate,
  upload.single('file'),
  handleUploadErrors,
  fileController.uploadModelFile
);

// Get all files for a model instance
router.get(
  '/:modelName/:modelId',
  authenticate,
  fileController.getModelFiles
);

// Delete a file
router.delete(
  '/:modelName/:fileId',
  authenticate,
  fileController.deleteModelFile
);

// Get a signed URL for downloading a file
router.get(
  '/download/:modelName/:fileKey',
  authenticate,
  fileController.downloadFile
);

module.exports = router;
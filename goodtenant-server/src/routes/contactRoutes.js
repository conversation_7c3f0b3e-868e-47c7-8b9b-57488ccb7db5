const express = require('express');
const router = express.Router();
const contactController = require('../controllers/contactController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route   POST /api/contacts
 * @desc    Create a new contact
 * @access  Private
 */
router.post('/', contactController.createContact);

/**
 * @route   GET /api/contacts
 * @desc    Get all contacts with optional filtering
 * @access  Private
 */
router.get('/', contactController.getContacts);

/**
 * @route   GET /api/contacts/:id
 * @desc    Get a single contact by ID
 * @access  Private
 */
router.get('/:id', contactController.getContactById);

/**
 * @route   PUT /api/contacts/:id
 * @desc    Update a contact
 * @access  Private
 */
router.put('/:id', contactController.updateContact);

/**
 * @route   DELETE /api/contacts/:id
 * @desc    Delete a contact (admin only)
 * @access  Private/Admin
 */
router.delete('/:id', contactController.deleteContact);

// Nested route: Get contacts by property ID
/**
 * @route   GET /api/properties/:propertyId/contacts
 * @desc    Get contacts for a specific property
 * @access  Private
 */
router.get('/properties/:propertyId/contacts', contactController.getContactsByPropertyId);

module.exports = router;
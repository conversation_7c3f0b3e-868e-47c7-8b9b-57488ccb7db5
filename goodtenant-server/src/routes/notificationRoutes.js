const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/notifications - Get all notifications (with optional filters)
router.get('/', notificationController.getNotifications);

// GET /api/notifications/:id - Get notification by ID
router.get('/:id', notificationController.getNotificationById);

// POST /api/notifications - Create a new notification
router.post('/', notificationController.createNotification);

// PATCH /api/notifications/:id/read - Mark notification as read
router.patch('/:id/read', notificationController.markAsRead);

// PATCH /api/notifications/read-all - Mark all user's notifications as read
router.patch('/read-all', notificationController.markAllAsRead);

// DELETE /api/notifications/:id - Delete a notification
router.delete('/:id', notificationController.deleteNotification);

module.exports = router;

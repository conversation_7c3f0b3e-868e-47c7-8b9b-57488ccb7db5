const express = require('express');
const router = express.Router();
const leaseController = require('../controllers/leaseController');
const hoaController = require('../controllers/hoaController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// Create a new lease
router.post('/',  leaseController.createLease);

// Get all leases with optional filtering
router.get('/', leaseController.getLeases);

// Get a single lease by ID
router.get('/:id', leaseController.getLeaseById);

// Update a lease
router.put('/:id', leaseController.updateLease);

// Delete a lease
router.delete('/:id', leaseController.deleteLease);

// Generate HOA document for a lease
router.get('/:leaseId/hoa-document', hoaController.generateHOADocument);

module.exports = router;

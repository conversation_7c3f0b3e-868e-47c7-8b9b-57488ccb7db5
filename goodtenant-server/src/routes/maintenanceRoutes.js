// routes/maintenanceRoutes.js
const express = require('express');
const router = express.Router();
const maintenanceController = require('../controllers/maintenanceController');
const { authenticate } = require('../middlewares/auth');

// All routes are protected by auth middleware
router.use(authenticate);

// Create a ticket for a specific property - Using the new endpoint structure
router.post('/property/:propertyId', maintenanceController.createTicket);

// Get all tickets (with optional filtering)
router.get('/', maintenanceController.getTickets);

// Ticket by ID operations
router.route('/:id')
  .get(maintenanceController.getTicket)
  .put(maintenanceController.updateTicket);

// Ticket management endpoints
router.patch('/:id/assign', maintenanceController.assignTicket);
router.patch('/:id/status', maintenanceController.updateStatus);

module.exports = router;
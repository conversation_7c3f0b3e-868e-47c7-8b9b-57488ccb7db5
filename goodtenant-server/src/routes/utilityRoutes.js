const express = require('express');
const router = express.Router();
const utilityController = require('../controllers/utilityController');
const { authenticate, authorize } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/utilities - Get all utilities (with optional filtering)
router.get('/', utilityController.getUtilities);

// GET /api/utilities/:id - Get utility by ID
router.get('/:id', utilityController.getUtilityById);

// POST /api/utilities - Create a new utility
router.post('/', utilityController.createUtility);

// PUT /api/utilities/:id - Update a utility
router.put('/:id', utilityController.updateUtility);

// DELETE /api/utilities/:id - Delete a utility (admin only)
router.delete('/:id', utilityController.deleteUtility);

// Property-specific utility routes
const propertyUtilityRouter = express.Router({ mergeParams: true });

// GET /api/properties/:propertyId/utilities - Get utilities for a property
propertyUtilityRouter.get('/', utilityController.getUtilitiesByPropertyId);

// Mount the property utility routes
router.use('/properties/:propertyId', propertyUtilityRouter);

module.exports = router;
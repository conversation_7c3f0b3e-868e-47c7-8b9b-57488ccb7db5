const express = require('express');
const paymentController = require('../controllers/paymentController');
const logger = require('../utils/logger');

// Export a function that creates a new router with the webhook route
module.exports = function(router) {
  if (!router) {
    router = express.Router();
  }

  // Webhook endpoint must be public (no auth)
  // Use full path here since we're mounting at root
  router.post('/api/stripe/webhook',
    // Raw body parser - must be first
    express.raw({ type: 'application/json' }),
    
    // Log the raw body for debugging
    (req, res, next) => {
      try {
        // Store the raw body for signature verification
        req.rawBody = req.body.toString('utf8');
        logger.info(`Received webhook request at ${new Date().toISOString()}`);
        logger.debug('Raw body length:', req.rawBody.length);
        next();
      } catch (error) {
        logger.error('Error processing webhook body:', error);
        return res.status(400).json({ success: false, message: 'Invalid request body' });
      }
    },
    
    // Parse JSON body for easier access in the controller
    express.json(),
    
    // Handle the webhook
    paymentController.handleWebhook
  );

  return router;
};

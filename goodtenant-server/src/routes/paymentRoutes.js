const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// Stripe Checkout endpoints
router.post('/checkout', paymentController.createCheckoutSession);  // Create a new checkout session
router.get('/session/:sessionId', paymentController.getPaymentBySession);  // Get payment by session ID
router.get('/success', paymentController.handleSuccess);  // Handle successful payment redirect
router.get('/cancel', paymentController.handleCancel);  // Handle cancelled payment

// Payment management endpoints
router.post('/', paymentController.createPayment);  // Create a new manual payment
router.get('/', paymentController.getPayments);  // Get all payments with optional filters
router.get('/:id', paymentController.getPaymentById);  // Get payment by ID
router.put('/:id', paymentController.updatePayment);  // Update a payment
router.delete('/:id', paymentController.deletePayment);  // Delete a payment (soft delete)

// Filter endpoints
router.get('/account/:accountId', paymentController.getPayments);  // Get payments for a specific account
router.get('/user/:userId', paymentController.getPayments);  // Get payments for a specific user

module.exports = router;

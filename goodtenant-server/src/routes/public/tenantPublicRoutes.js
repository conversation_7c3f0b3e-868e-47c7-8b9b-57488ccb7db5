const express = require('express');
const router = express.Router();
const tenantController = require('../../controllers/tenantController');
const fileController = require('../../controllers/fileController');
const leaseController = require('../../controllers/leaseController');
const petController = require('../../controllers/petController');
const hoaController = require('../../controllers/hoaController');
const { upload } = require('../../middlewares/upload');

// Verify invitation token
router.get('/invitations/:token/verify', tenantController.verifyInvitationToken);

// Complete tenant onboarding
router.post('/invitations/:token/complete', tenantController.completeTenantOnboarding);

// Finalize tenant onboarding (public endpoint)
router.patch('/invitations/:token/finalize', tenantController.finalizeTenantOnboarding);

// Lease document generation with token
router.get('/leases/:id/document/:token', leaseController.generateLeaseDocument);

// Pet document generation with token
router.get('/leases/:id/pet-document/:token', petController.generatePetDocument);

// HOA document generation with token
router.get('/leases/:leaseId/hoa-document/:token', hoaController.generatePublicHOADocument);

// Get landlord signature by lease (public with token)
router.get('/leases/:leaseId/landlord-signature/:token', tenantController.getLandlordSignatureByLease);

// Public file upload with token validation
router.post(
  '/upload/:token',
  upload.single('file'), 
  // Handle single file upload
  fileController.uploadFileWithToken
);

module.exports = router;

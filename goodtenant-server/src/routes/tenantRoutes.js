const express = require('express');
const router = express.Router();
const tenantController = require('../controllers/tenantController');
const { authenticate } = require('../middlewares/auth');

// All routes in this file are protected by authentication
router.use(authenticate);

// Create a new tenant invitation for a property
router.post('/properties/invitations/:propertyId', tenantController.createTenantInvitation);

// Get all invitations for a property
router.get('/properties/invitations/:propertyId', tenantController.getPropertyInvitations);

// Cancel a tenant invitation
router.delete('/invitations/:invitationId', tenantController.cancelInvitation);

// Resend a tenant invitation
router.post('/invitations/:invitationId/resend', tenantController.resendInvitation);



module.exports = router;
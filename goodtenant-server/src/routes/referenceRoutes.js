const express = require('express');
const router = express.Router();
const referenceController = require('../controllers/referenceController');
const { authenticate } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/references - Get all references (filterable)
// Query params: page, limit, search, status, isVerified, userId
router.get('/', referenceController.getReferences);

// GET /api/references/my-references - Get current user's references
// Query params: page, limit, status, isVerified
router.get('/my-references', referenceController.getMyReferences);

// GET /api/references/:id - Get a specific reference
router.get('/:id', referenceController.getReferenceById);

// POST /api/references - Create a new reference
router.post('/', referenceController.createReference);

// PUT /api/references/:id - Update a reference
router.put('/:id', referenceController.updateReference);

// DELETE /api/references/:id - Delete a reference
router.delete('/:id', referenceController.deleteReference);

module.exports = router;
const express = require('express');
const router = express.Router();
const taxController = require('../controllers/taxController');
const { authenticate, authorize } = require('../middlewares/auth');

// Apply authentication middleware to all routes
router.use(authenticate);

router.get('/', taxController.getTaxes);

router.get('/:id', taxController.getTaxById);

router.post('/', taxController.createTax);

router.put('/:id', taxController.updateTax);

router.delete('/:id', taxController.deleteTax);

router.get('/properties/:propertyId', taxController.getPropertyTaxes);

module.exports = router;
// routes/signatureRoutes.js
const express = require('express');
const router = express.Router();
const signatureController = require('../controllers/signatureController');
const { upload, handleUploadErrors } = require('../middlewares/upload');
const { authenticate } = require('../middlewares/auth');

router.post(
  '/upload',
  authenticate,
  upload.single('signature'),
  handleUploadErrors,
  signatureController.uploadSignature
);

router.delete(
  '/',
  authenticate,
  signatureController.deleteSignature
);

router.get(
  '/',
  authenticate,
  signatureController.getSignature
);

module.exports = router;
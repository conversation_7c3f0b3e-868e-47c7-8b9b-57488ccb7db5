const express = require('express');
const router = express.Router();
const { body, query } = require('express-validator');
const authController = require('../controllers/authController');
const { validateRequest } = require('../middlewares/validation');
const { authenticate } = require('../middlewares/auth');

// Validation rules and routes
router.post(
  '/register',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }),
    body('firstName').notEmpty().trim(),
    body('lastName').notEmpty().trim(),
    validateRequest
  ],
  authController.register
);

router.post(
  '/login',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
    validateRequest
  ],
  authController.login
);

router.post(
  '/refresh-token',
  authController.refreshToken
);

router.post(
  '/logout',
  authenticate,  
  authController.logout
);

// Request password reset
router.post(
  '/request-password-reset',
  [
    body('email').isEmail().normalizeEmail(),
    validateRequest
  ],
  authController.requestPasswordReset
);

// Reset password
router.post(
  '/reset-password',
  [
    body('token').notEmpty(),
    body('newPassword').isLength({ min: 8 }),
    validateRequest
  ],
  authController.resetPassword
);

// Verify email
router.get(
  '/verify-email',
  [
    query('token').notEmpty(),
    validateRequest
  ],
  authController.verifyEmail
);

module.exports = router;
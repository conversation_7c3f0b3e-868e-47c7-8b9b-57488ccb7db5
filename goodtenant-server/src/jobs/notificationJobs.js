// goodtenant-server/src/jobs/notificationJobs.js
const cron = require('node-cron');
const { Op } = require('sequelize');
const { Payment, Lease, Document, Maintenance } = require('../models');
const notificationService = require('../services/notificationService');
const logger = require('../utils/logger');
const eventEmitter = require('../utils/eventEmitter');

class NotificationJobs {
  constructor(sequelize) {
    this.sequelize = sequelize;
    this.initializeJobs();
  }

  initializeJobs() {
    // Run daily at 9 AM
    cron.schedule('0 9 * * *', this.checkDuePayments.bind(this));
    
    // Run daily at 10 AM
    cron.schedule('0 10 * * *', this.checkUpcomingLeaseRenewals.bind(this));
    
    // Run hourly
    cron.schedule('0 * * * *', this.checkOverdueMaintenanceTickets.bind(this));
  }

  async checkDuePayments() {
    try {
      // Get current date and calculate dates for notifications
      const now = new Date();
      const threeDaysFromNow = new Date(now);
      threeDaysFromNow.setDate(now.getDate() + 3);
      
      // 1. Check Lease Payments
      const dueLeasePayments = await this.checkLeasePayments(now, threeDaysFromNow);
      
      // 2. Check HOA Fees
      const dueHoaFees = await this.checkHoaFees(now, threeDaysFromNow);
      
      // 3. Check Insurance Premiums
      const dueInsurancePremiums = await this.checkInsurancePremiums(now, threeDaysFromNow);
      
      // 4. Check Tax Payments
      const dueTaxPayments = await this.checkTaxPayments(now, threeDaysFromNow);
      
      // 5. Check Utility Bills
      const dueUtilityBills = await this.checkUtilityBills(now, threeDaysFromNow);
      
      logger.info('Payment due check completed', {
        leasePayments: dueLeasePayments,
        hoaFees: dueHoaFees,
        insurancePremiums: dueInsurancePremiums,
        taxPayments: dueTaxPayments,
        utilityBills: dueUtilityBills
      });
      
    } catch (error) {
      logger.error('Error in checkDuePayments job:', error);
    }
  }
  
  async checkLeasePayments(now, threeDaysFromNow) {
    try {
      const dueLeases = await this.sequelize.models.Lease.findAll({
        where: {
          status: 'active',
          isActive: true,
          '$payments.dueDate$': {
            [Op.between]: [now, threeDaysFromNow]
          },
          '$payments.status$': 'pending'
        },
        include: [
          {
            model: this.sequelize.models.Payment,
            as: 'payments',
            required: true,
            where: {
              notificationSent: {
                [Op.or]: [null, false]
              }
            }
          },
          {
            model: this.sequelize.models.Property,
            as: 'property',
            required: true
          },
          {
            model: this.sequelize.models.User,
            as: 'tenants',
            required: true
          }
        ]
      });

      for (const lease of dueLeases) {
        for (const payment of lease.payments) {
          try {
            // Send notification to each tenant
            for (const tenant of lease.tenants) {
              await notificationService.createNotification({
                userId: tenant.id,
                type: 'payment_due',
                title: 'Rent Payment Due',
                message: `Rent payment of $${payment.amount} for ${lease.property.name} is due on ${new Date(payment.dueDate).toLocaleDateString()}`,
                entityType: 'PAYMENT',
                entityId: payment.id
              }, 'system');
            }
            
            // Mark payment as notified
            await payment.update({ notificationSent: true });
            
          } catch (error) {
            logger.error(`Error processing lease payment notification for payment ${payment.id}:`, error);
          }
        }
      }
      
      return dueLeases.length;
      
    } catch (error) {
      logger.error('Error in checkLeasePayments:', error);
      return 0;
    }
  }
  
  async checkHoaFees(now, threeDaysFromNow) {
    try {
      const dueHoaFees = await this.sequelize.models.HOA.findAll({
        where: {
          isActive: true,
          nextDueDate: {
            [Op.between]: [now, threeDaysFromNow]
          },
          notificationSent: {
            [Op.or]: [null, false]
          }
        },
        include: [
          {
            model: this.sequelize.models.Property,
            as: 'properties',
            required: true
          },
          {
            model: this.sequelize.models.Account,
            as: 'account',
            required: true,
            include: [{
              model: this.sequelize.models.User,
              as: 'users',
              required: true,
              through: { where: { role: 'account_owner' } }
            }]
          }
        ]
      });

      for (const hoa of dueHoaFees) {
        try {
          // Send notification to account admins
          for (const user of hoa.account.users) {
            await notificationService.createNotification({
              userId: user.id,
              type: 'hoa_fee_due',
              title: 'HOA Fee Due',
              message: `HOA fee of $${hoa.fee} for ${hoa.name} is due on ${new Date(hoa.nextDueDate).toLocaleDateString()}`,
              entityType: 'HOA',
              entityId: hoa.id
            }, 'system');
          }
          
          // Mark HOA fee as notified
          await hoa.update({ notificationSent: true });
          
        } catch (error) {
          logger.error(`Error processing HOA fee notification for HOA ${hoa.id}:`, error);
        }
      }
      
      return dueHoaFees.length;
      
    } catch (error) {
      logger.error('Error in checkHoaFees:', error);
      return 0;
    }
  }
  
  async checkInsurancePremiums(now, threeDaysFromNow) {
    try {
      const duePremiums = await this.sequelize.models.Insurance.findAll({
        where: {
          nextPremiumDue: {
            [Op.between]: [now, threeDaysFromNow]
          },
          notificationSent: {
            [Op.or]: [null, false]
          }
        },
        include: [
          {
            model: this.sequelize.models.Property,
            as: 'property',
            required: true
          }
        ]
      });

      for (const insurance of duePremiums) {
        try {
          // Send notification to account admins
          const accountAdmins = await this.sequelize.models.AccountUser.findAll({
            where: {
              accountId: insurance.accountId,
              role: 'account_owner'
            },
            include: [{
              model: this.sequelize.models.User,
              as: 'user'
            }]
          });

          for (const admin of accountAdmins) {
            await notificationService.createNotification({
              userId: admin.user.id,
              type: 'insurance_premium_due',
              title: 'Insurance Premium Due',
              message: `Insurance premium of $${insurance.premiumAmount} for ${insurance.provider} is due on ${new Date(insurance.nextPremiumDue).toLocaleDateString()}`,
              entityType: 'INSURANCE',
              entityId: insurance.id
            }, 'system');
          }
          
          // Mark as notified
          await insurance.update({ notificationSent: true });
          
        } catch (error) {
          logger.error(`Error processing insurance premium notification for insurance ${insurance.id}:`, error);
        }
      }
      
      return duePremiums.length;
      
    } catch (error) {
      logger.error('Error in checkInsurancePremiums:', error);
      return 0;
    }
  }
  
  async checkTaxPayments(now, threeDaysFromNow) {
    try {
      const dueTaxes = await this.sequelize.models.Tax.findAll({
        where: {
          dueDate: {
            [Op.between]: [now, threeDaysFromNow]
          },
          isPaid: false,
          notificationSent: {
            [Op.or]: [null, false]
          }
        },
        include: [
          {
            model: this.sequelize.models.Property,
            as: 'property',
            required: true
          }
        ]
      });

      for (const tax of dueTaxes) {
        try {
          // Send notification to property managers/admins
          const managers = await this.sequelize.models.AccountUser.findAll({
            where: {
              accountId: tax.property.accountId,
              role: { [Op.in]: ['account_owner', 'property_manager'] }
            },
            include: [{
              model: this.sequelize.models.User,
              as: 'user'
            }]
          });

          for (const manager of managers) {
            await notificationService.createNotification({
              userId: manager.user.id,
              type: 'tax_payment_due',
              title: 'Tax Payment Due',
              message: `Tax payment of $${tax.amount} for ${tax.property.name} is due on ${new Date(tax.dueDate).toLocaleDateString()}`,
              entityType: 'TAX',
              entityId: tax.id
            }, 'system');
          }
          
          // Mark as notified
          await tax.update({ notificationSent: true });
          
        } catch (error) {
          logger.error(`Error processing tax payment notification for tax ${tax.id}:`, error);
        }
      }
      
      return dueTaxes.length;
      
    } catch (error) {
      logger.error('Error in checkTaxPayments:', error);
      return 0;
    }
  }
  
  async checkUtilityBills(now, threeDaysFromNow) {
    try {
      const dueUtilities = await this.sequelize.models.Utility.findAll({
        where: {
          nextBillingDate: {
            [Op.between]: [now, threeDaysFromNow]
          },
          isActive: true,
          notificationSent: {
            [Op.or]: [null, false]
          }
        },
        include: [
          {
            model: this.sequelize.models.Property,
            as: 'property',
            required: true
          }
        ]
      });

      for (const utility of dueUtilities) {
        try {
          // Send notification to property managers/admins
          const managers = await this.sequelize.models.AccountUser.findAll({
            where: {
              accountId: utility.accountId,
              role: { [Op.in]: ['account_owner', 'property_manager'] }
            },
            include: [{
              model: this.sequelize.models.User,
              as: 'user'
            }]
          });

          for (const manager of managers) {
            await notificationService.createNotification({
              userId: manager.user.id,
              type: 'utility_bill_due',
              title: `${utility.utilityType} Bill Due`,
              message: `${utility.utilityType} bill from ${utility.providerName} for ${utility.property?.name || 'property'} is due on ${new Date(utility.nextBillingDate).toLocaleDateString()}`,
              entityType: 'UTILITY',
              entityId: utility.id
            }, 'system');
          }
          
          // Mark as notified
          await utility.update({ notificationSent: true });
          
        } catch (error) {
          logger.error(`Error processing utility bill notification for utility ${utility.id}:`, error);
        }
      }
      
      return dueUtilities.length;
      
    } catch (error) {
      logger.error('Error in checkUtilityBills:', error);
      return 0;
    }
  }

  async checkUpcomingLeaseRenewals() {
    try {
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      
      const expiringLeases = await Lease.findAll({
        where: {
          endDate: {
            [Op.lte]: thirtyDaysFromNow,
            [Op.gte]: new Date()
          },
          status: 'active',
          renewalNotificationSent: {
            [Op.or]: [null, false]
          }
        },
        include: ['tenant', 'property']
      });

      for (const lease of expiringLeases) {
        try {
          const daysUntilExpiry = Math.ceil((lease.endDate - new Date()) / (1000 * 60 * 60 * 24));
          
          await notificationService.createNotification({
            userId: lease.tenantId,
            type: 'lease_renewal',
            title: 'Lease Renewal Reminder',
            message: `Your lease for ${lease.property.name} expires in ${daysUntilExpiry} days. Please contact management to discuss renewal options.`,
            entityType: 'LEASE',
            entityId: lease.id
          }, 'system');
          
          // Mark as notification sent
          await lease.update({ renewalNotificationSent: true });
        } catch (error) {
          logger.error(`Error processing lease renewal notification for lease ${lease.id}:`, error);
        }
      }
    } catch (error) {
      logger.error('Error in checkUpcomingLeaseRenewals job:', error);
    }
  }

 

  async checkOverdueMaintenanceTickets() {
    try {
      const overdueTickets = await Maintenance.findAll({
        where: {
          status: {
            [Op.notIn]: ['completed', 'cancelled']
          },
          dueDate: {
            [Op.lt]: new Date()
          },
          overdueNotificationSent: {
            [Op.or]: [null, false]
          }
        },
        include: ['reportedBy', 'property']
      });

      for (const ticket of overdueTickets) {
        try {
          await notificationService.createNotification({
            userId: ticket.reportedById,
            type: 'maintenance_overdue',
            title: 'Overdue Maintenance Ticket',
            message: `Maintenance ticket #${ticket.id} for ${ticket.property.name} is overdue. Please check the status.`,
            entityType: 'MAINTENANCE',
            entityId: ticket.id
          }, 'system');
          
          // Mark as notification sent
          await ticket.update({ overdueNotificationSent: true });
        } catch (error) {
          logger.error(`Error processing overdue maintenance notification for ticket ${ticket.id}:`, error);
        }
      }
    } catch (error) {
      logger.error('Error in checkOverdueMaintenanceTickets job:', error);
    }
  }
}

module.exports = NotificationJobs;
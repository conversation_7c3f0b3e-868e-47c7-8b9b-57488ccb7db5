// Load environment variables first
require('dotenv').config({ path: '../.env' });

const http = require('http');
const app = require('./app');
const logger = require('./utils/logger');
const { connectDB } = require('./config/database');

// Get port from environment and store in Express.
const port = process.env.PORT || 3000;
app.set('port', port);

// Create HTTP server.
const server = http.createServer(app);

// Connect to database and start server
const startServer = async () => {
  try {
    console.log('Environment:', process.env.NODE_ENV);
    console.log('DB Config:', {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      username: process.env.DB_USER
    });
    
    await connectDB();
    
    server.listen(port, () => {
      logger.info(`Server running on port ${port}`);
      logger.info(`API Documentation available at http://localhost:${port}/api-docs`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Rejection:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle SIGTERM
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
  });
});

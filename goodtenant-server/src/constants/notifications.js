// Notification types
const NOTIFICATION_TYPES = {
  TASK_ASSIGNED: 'task_assigned',
  TASK_UPDATED: 'task_updated',
  TASK_DUE_SOON: 'task_due_soon',
  TASK_OVERDUE: 'task_overdue',
  TASK_COMPLETED: 'task_completed',
  DOCUMENT_EXPIRING: 'document_expiring',
  DOCUMENT_EXPIRED: 'document_expired',
  PAYMENT_RECEIVED: 'payment_received',
  PAYMENT_DUE: 'payment_due',
  PAYMENT_OVERDUE: 'payment_overdue',
  SYSTEM_ANNOUNCEMENT: 'system_announcement',
  MAINTENANCE_REQUEST: 'maintenance_request',
  MAINTENANCE_UPDATE: 'maintenance_update',
  LEASE_RENEWAL: 'lease_renewal',
};

// Entity types that can be associated with notifications
const NOTIFICATION_ENTITIES = {
  TASK: 'Task',
  USER: 'User',
  PROPERTY: 'Property',
  LEASE: 'Lease',
  DOCUMENT: 'Document',
  PAYMENT: 'Payment',
  MAINTENANCE: 'Maintenance',
};

// Default notification settings for users
const DEFAULT_NOTIFICATION_SETTINGS = {
  email: {
    task_assigned: true,
    task_due_soon: true,
    task_overdue: true,
    document_expiring: true,
    document_expired: true,
    payment_due: true,
    payment_overdue: true,
    payment_received: true,
    system_announcement: true,
    maintenance_request: true,
    maintenance_update: true,
    lease_renewal: true,
  },
  push: {
    task_assigned: true,
    task_due_soon: true,
    task_overdue: true,
    document_expiring: true,
    document_expired: true,
    payment_due: true,
    payment_overdue: true,
    payment_received: false,
    system_announcement: true,
    maintenance_request: true,
    maintenance_update: true,
    lease_renewal: true,
  },
  in_app: {
    task_assigned: true,
    task_due_soon: true,
    task_overdue: true,
    document_expiring: true,
    document_expired: true,
    payment_due: true,
    payment_overdue: true,
    payment_received: true,
    system_announcement: true,
    maintenance_request: true,
    maintenance_update: true,
    lease_renewal: true,
  },
};

// Notification statuses
const NOTIFICATION_STATUS = {
  UNREAD: 'unread',
  READ: 'read',
  ARCHIVED: 'archived',
};

// Notification priorities
const NOTIFICATION_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent',
};

module.exports = {
  NOTIFICATION_TYPES,
  NOTIFICATION_ENTITIES,
  DEFAULT_NOTIFICATION_SETTINGS,
  NOTIFICATION_STATUS,
  NOTIFICATION_PRIORITY,
};

module.exports = (sequelize, DataTypes) => {
  const HOAFIle = sequelize.define('HOAFIle', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_key',
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_type',
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'hoa_files',
    underscored: true,
  });

  HOAFIle.associate = (models) => {
    HOAFIle.belongsTo(models.HOA, {
      foreignKey: 'hoaId',
      as: 'hoa',
    });
    HOAFIle.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader',
    });
  };

  return HOAFIle;
};

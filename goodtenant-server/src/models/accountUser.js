const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AccountUser = sequelize.define(
    'AccountUser',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      // Primary role for this account user
      primaryRole: {
        type: DataTypes.ENUM(
          'account_owner',
          'property_manager',
          'leasing_agent',
          'maintenance_staff',
          'tenant'
        ),
        defaultValue: 'tenant',
        allowNull: false,
      },
      // Store additional roles as JSON
      additionalRoles: {
        type: DataTypes.JSON,
        defaultValue: [],
        allowNull: false,
        get() {
          const rawValue = this.getDataValue('additionalRoles');
          return rawValue ? (Array.isArray(rawValue) ? rawValue : []) : [];
        },
        set(value) {
          this.setDataValue('additionalRoles', 
            Array.isArray(value) ? 
              value.filter(role => [
                'property_manager',
                'leasing_agent',
                'maintenance_staff',
                'tenant'
              ].includes(role)) : 
              []
          );
        },
      },
      isDefault: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      tableName: 'account_users',
      indexes: [
        {
          fields: ['accountId'],
        },
        {
          fields: ['userId'],
        },
        {
          unique: true,
          fields: ['accountId', 'userId'],
        },
      ],
    }
  );

  AccountUser.associate = (models) => {
    AccountUser.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
    });

    AccountUser.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return AccountUser;
};

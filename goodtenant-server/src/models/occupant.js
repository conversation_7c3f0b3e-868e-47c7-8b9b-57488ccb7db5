const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Occupant = sequelize.define('Occupant', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'first_name',
      validate: {
        notEmpty: { msg: 'First name is required' },
        len: {
          args: [1, 50],
          msg: 'First name must be between 1 and 50 characters',
        },
      },
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'last_name',
      validate: {
        notEmpty: { msg: 'Last name is required' },
        len: {
          args: [1, 50],
          msg: 'Last name must be between 1 and 50 characters',
        },
      },
    },
    age: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        isInt: { msg: 'Age must be an integer' },
        min: { args: [0], msg: 'Age must be 0 or greater' },
        max: { args: [120], msg: 'Age must be 120 or less' },
      },
    },
    relationship: {
      type: DataTypes.ENUM(
        'spouse',
        'child',
        'parent',
        'sibling',
        'other_relative',
        'roommate',
        'other'
      ),
      allowNull: false,
      validate: {
        notEmpty: { msg: 'Relationship is required' },
      },
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Any additional notes about the occupant',
    },
    leaseId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'lease_id',
      references: {
        model: 'leases',
        key: 'id',
      },
      comment: 'Reference to the lease this occupant is associated with',
    },
  }, {
    tableName: 'occupants',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  Occupant.associate = (models) => {
    // Many-to-one relationship with Lease
    Occupant.belongsTo(models.Lease, {
      foreignKey: 'leaseId',
      as: 'lease',
      onDelete: 'CASCADE',
    });
  };

  return Occupant;
};

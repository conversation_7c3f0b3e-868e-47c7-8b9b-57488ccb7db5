const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Reference = sequelize.define('Reference', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    // Reference provider's name (e.g., previous landlord)
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    // Reference provider's position/relationship (e.g., "Previous Landlord")
    position: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    // Reference provider's company/property name
    company: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    // Contact information
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        is: /^[\d\s\-().+]+$/,
      },
    },
    // Rental property address
    propertyAddress: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'property_address',
    },
    propertyCity: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'property_city',
    },
    propertyState: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'property_state',
    },
    propertyZip: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'property_zip',
    },
    // Rental period
    rentalStartDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'rental_start_date',
    },
    rentalEndDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'rental_end_date',
    },
    // Reference details
    referenceText: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'reference_text',
    },
    // Verification status
    isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_verified',
    },
    verificationDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'verification_date',
    },
    // Status
    status: {
      type: DataTypes.ENUM('pending', 'contacted', 'verified', 'rejected'),
      defaultValue: 'pending',
    },
    // Notes for internal use
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
    },
  }, {
    tableName: 'references',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  // Associations
  Reference.associate = function(models) {
    // Belongs to a user (the tenant)
    Reference.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'tenant',
    });
    // Belongs to a user who verified the reference (admin/landlord)
    Reference.belongsTo(models.User, {
      foreignKey: 'verifiedById',
      as: 'verifiedBy',
    });
  };

  return Reference;
};
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Property = sequelize.define(
    'Property',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Property name is required' },
          len: {
            args: [2, 100],
            msg: 'Property name must be between 2 and 100 characters',
          },
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      propertyType: {
        type: DataTypes.ENUM('apartment', 'house', 'condo', 'townhouse', 'commercial', 'other'),
        allowNull: false,
        defaultValue: 'apartment',
      },
      // Address fields
      addressLine1: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Address line 1 is required' },
          len: {
            args: [2, 255],
            msg: 'Address line 1 must be between 2 and 255 characters',
          },
        },
      },
      addressLine2: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'City is required' },
          len: {
            args: [2, 100],
            msg: 'City must be between 2 and 100 characters',
          },
        },
      },
      state: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'State/province is required' },
          len: {
            args: [2, 100],
            msg: 'State/province must be between 2 and 100 characters',
          },
        },
      },
      postalCode: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Postal/ZIP code is required' },
          len: {
            args: [2, 20],
            msg: 'Postal/ZIP code must be between 2 and 20 characters',
          },
        },
      },
      country: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'United States',
        validate: {
          notEmpty: { msg: 'Country is required' },
        },
      },
      hoaId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'hoas',  
          key: 'id',
        },
        onUpdate: 'SET NULL',
        onDelete: 'SET NULL',
      },
      // Location coordinates (for maps)
      latitude: {
        type: DataTypes.FLOAT,
        allowNull: true,
        validate: {
          min: -90,
          max: 90,
        },
      },
      longitude: {
        type: DataTypes.FLOAT,
        allowNull: true,
        validate: {
          min: -180,
          max: 180,
        },
      },
      // Property details
      yearBuilt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: 1500,
          max: new Date().getFullYear() + 1,
        },
      },
      sizeSquareFeet: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: 0,
        },
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      status: {
        type: DataTypes.ENUM('vacant', 'occupied', 'under_maintenance', 'renovating', 'off_market'),
        allowNull: false,
        defaultValue: 'vacant',
        validate: {
          notEmpty: { msg: 'Status is required' },
          isIn: {
            args: [['vacant', 'occupied', 'under_maintenance', 'renovating', 'off_market']],
            msg: 'Invalid status value',
          },
        },
      },
      priceAmount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
        validate: {
          min: {
            args: [0],
            msg: 'Price must be a positive number'
          }
        },
        comment: 'Stores the numeric price amount'
      },
      priceCurrency: {
        type: DataTypes.STRING(3),
        allowNull: true,
        defaultValue: 'USD',
        validate: {
          isUppercase: {
            msg: 'Currency must be in uppercase (e.g., USD, EUR)'
          },
          len: {
            args: [3, 3],
            msg: 'Currency code must be 3 characters long (e.g., USD, EUR)'
          }
        },
        comment: 'Stores the ISO 4217 currency code (e.g., USD, EUR, GBP)'
      },
      priceInterval: {
        type: DataTypes.ENUM('hour', 'day', 'week', 'month', 'year'),
        allowNull: true,
        comment: 'The time interval for the price (e.g., per month, per year)'
      },
      depositAmount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
        validate: {
          min: {
            args: [0],
            msg: 'Deposit must be a positive number'
          }
        },
        comment: 'Security deposit amount for the property'
      },
      bedrooms: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: {
            args: [0],
            msg: 'Number of bedrooms cannot be negative'
          }
        },
        comment: 'Number of bedrooms in the property'
      },
      bathrooms: {
        type: DataTypes.FLOAT,
        allowNull: true,
        validate: {
          min: {
            args: [0],
            msg: 'Number of bathrooms cannot be negative'
          }
        },
        comment: 'Number of bathrooms in the property (can be a half-bath)'
      },
    },
    {
      timestamps: true,
      paranoid: true, // Enable soft deletes
      tableName: 'properties' // Explicitly set lowercase table name
    }
  );

  // Define associations
  Property.associate = (models) => {
    // Belongs to Account
    Property.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
    });

    // Belongs to HOA
    Property.belongsTo(models.HOA, {
      foreignKey: 'hoaId',
      as: 'hoa',
    });

    // PropertyUser relationship (one-to-many)
    Property.hasMany(models.PropertyUser, {
      foreignKey: 'propertyId',
      as: 'propertyUsers',
      onDelete: 'CASCADE',
    });

    // Users relationship through PropertyUser
    Property.belongsToMany(models.User, {
      through: {
        model: models.PropertyUser,
        unique: false, // Allow multiple roles per user per property
      },
      foreignKey: 'propertyId',
      as: 'users',
    });

    // Note: Role association has been removed as part of the account-user refactoring
    // Roles are now managed through the AccountUser model
  };

  return Property;
};

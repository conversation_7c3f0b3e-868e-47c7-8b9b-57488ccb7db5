const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Loan = sequelize.define(
    'Loan',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      propertyId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'properties',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      lenderName: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Lender name is required' },
          len: {
            args: [2, 100],
            msg: 'Lender name must be between 2 and 100 characters',
          },
        },
      },
      lenderWebsite: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          isUrl: { msg: 'Please enter a valid URL' },
        },
      },
      loanNumber: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Loan number is required' },
        },
      },
      lenderAddressLine1: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Address line 1 is required' },
        },
      },
      lenderAddressLine2: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      lenderCity: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'City is required' },
        },
      },
      lenderState: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'State is required' },
        },
      },
      lenderPostalCode: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Postal code is required' },
        },
      },
      lenderCountry: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'USA',
        validate: {
          notEmpty: { msg: 'Country is required' },
        },
      },
      customerServicePhone: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          isValidPhone(value) {
            if (!value) return; // Allow null/empty
            
            // Remove all non-digit characters except leading +
            const digits = value.replace(/[^\d+]/g, '');
            
            // Check if we have at least 10 digits (US/Canada) or 7 digits (international)
            const digitCount = digits.replace(/\D/g, '').length;
            if (digitCount < 10 && !digits.startsWith('+')) {
              throw new Error('Phone number must have at least 10 digits');
            } else if (digitCount < 7) {
              throw new Error('Phone number is too short');
            }
            
            // Check total length (including + and any other allowed chars)
            if (value.length > 20) {
              throw new Error('Phone number cannot be longer than 20 characters');
            }
          }
        },
      },
      principal: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
        validate: {
          min: {
            args: [0],
            msg: 'Principal amount cannot be negative',
          },
        },
      },
      interestRate: {
        type: DataTypes.DECIMAL(5, 3),
        allowNull: false,
        validate: {
          min: {
            args: [0],
            msg: 'Interest rate cannot be negative',
          },
          max: {
            args: [100],
            msg: 'Interest rate cannot exceed 100%',
          },
        },
      },
      escrow: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        validate: {
          min: {
            args: [0],
            msg: 'Escrow amount cannot be negative',
          },
        },
      },
      loanAmount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
        validate: {
          min: {
            args: [0],
            msg: 'Loan amount cannot be negative',
          },
        },
      },
      comments: {
        type: DataTypes.TEXT,
        allowNull: true,
        validate: {
          len: {
            args: [0, 1000],
            msg: 'Comments cannot be longer than 1000 characters',
          },
        },
      },
    },
    {
      timestamps: true,
      paranoid: true,
      tableName: 'loans',
      indexes: [
        {
          unique: true,
          fields: ['accountId', 'loanNumber'],
          name: 'unique_loan_per_account',
        },
      ],
    }
  );

  // Add instance methods
  Loan.prototype.getFormattedAddress = function() {
    const parts = [
      this.lenderAddressLine1,
      this.lenderAddressLine2,
      this.lenderCity,
      this.lenderState,
      this.lenderPostalCode,
      this.lenderCountry
    ].filter(Boolean);
    return parts.join(', ');
  };

  // Add associations
  Loan.associate = (models) => {
    Loan.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
    });
    Loan.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
    });
  };

  return Loan;
};
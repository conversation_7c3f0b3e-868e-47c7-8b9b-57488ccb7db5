// src/models/inventory.js
module.exports = (sequelize, DataTypes) => {
  const Inventory = sequelize.define('Inventory', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    // Basic Information
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    // Categorization
    category: {
      type: DataTypes.ENUM(
        'appliances',
        'furniture',
        'electronics',
        'utilities',
        'tools',
        'fixtures',
        'other'
      ),
      allowNull: false,
      defaultValue: 'other',
    },
    // Quantity and Status
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 0,
      },
    },
    status: {
      type: DataTypes.ENUM(
        'new',
        'good',
        'needs_maintenance',
        'needs_replacement',
        'disposed'
      ),
      allowNull: false,
      defaultValue: 'good',
    },
    // Purchase Information
    purchaseDate: {
      type: DataTypes.DATEONLY,
      field: 'purchase_date',
      allowNull: true,
    },
    purchasePrice: {
      type: DataTypes.DECIMAL(10, 2),
      field: 'purchase_price',
      allowNull: true,
    },
    purchaseFrom: {
      type: DataTypes.STRING,
      field: 'purchase_from',
      allowNull: true,
    },
    // Warranty Information
    hasWarranty: {
      type: DataTypes.BOOLEAN,
      field: 'has_warranty',
      defaultValue: false,
    },
    warrantyExpiryDate: {
      type: DataTypes.DATEONLY,
      field: 'warranty_expiry_date',
      allowNull: true,
    },
    warrantyDetails: {
      type: DataTypes.TEXT,
      field: 'warranty_details',
      allowNull: true,
    },
    // Location Details
    location: {
      type: DataTypes.STRING, // e.g., "Master Bedroom", "Kitchen", "Garage"
      allowNull: true,
    },
    // Identification
    serialNumber: {
      type: DataTypes.STRING,
      field: 'serial_number',
      allowNull: true,
    },
    modelNumber: {
      type: DataTypes.STRING,
      field: 'model_number',
      allowNull: true,
    },
    brand: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    // Maintenance Information
    lastMaintenanceDate: {
      type: DataTypes.DATEONLY,
      field: 'last_maintenance_date',
      allowNull: true,
    },
    maintenanceFrequency: {
      type: DataTypes.ENUM(
        'weekly',
        'monthly',
        'quarterly',
        'biannually',
        'annually',
        'as_needed'
      ),
      field: 'maintenance_frequency',
      allowNull: true,
    },
    // Lifecycle
    expectedLifespan: {
      type: DataTypes.INTEGER, // in months
      field: 'expected_lifespan',
      allowNull: true,
    },
    disposalDate: {
      type: DataTypes.DATEONLY,
      field: 'disposal_date',
      allowNull: true,
    },
    disposalReason: {
      type: DataTypes.TEXT,
      field: 'disposal_reason',
      allowNull: true,
    },
    // Foreign Keys
    propertyId: {
      type: DataTypes.UUID,
      field: 'property_id',
      allowNull: false,
      references: {
        model: 'properties',
        key: 'id',
      },
    },
    vendorId: {
      type: DataTypes.UUID,
      field: 'vendor_id',
      allowNull: true,
      references: {
        model: 'contacts',
        key: 'id',
      },
    },
    createdBy: {
      type: DataTypes.UUID,
      field: 'created_by',
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    updatedBy: {
      type: DataTypes.UUID,
      field: 'updated_by',
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'inventory',
    timestamps: true,
    underscored: true,
    paranoid: true, // Enable soft deletes
  });

  // Define associations
  Inventory.associate = (models) => {
    // Belongs to Property
    Inventory.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    // Optional: Belongs to Vendor (Contact)
    Inventory.belongsTo(models.Contact, {
      foreignKey: 'vendorId',
      as: 'vendor',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    // Created by User
    Inventory.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
    });

    // Updated by User
    Inventory.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'CASCADE',
    });

    // Has many InventoryFiles
    Inventory.hasMany(models.InventoryFile, {
      foreignKey: 'inventoryId',
      as: 'files',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    // Has many Maintenance Records
    Inventory.hasMany(models.MaintenanceTicket, {
      foreignKey: 'inventoryId',
      as: 'maintenanceTickets',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  };

  return Inventory;
};
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Lease = sequelize.define('Lease', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    startDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'start_date',
    },
    endDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'end_date',
    },
    monthlyRent: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'monthly_rent',
    },
    securityDeposit: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'security_deposit',
    },
    status: {
      type: DataTypes.ENUM('draft', 'active', 'expired', 'terminated'),
      defaultValue: 'draft',
      allowNull: false,
    },
    leaseType: {
      type: DataTypes.ENUM('fixed', 'month-to-month', 'yearly'),
      allowNull: false,
      field: 'lease_type',
    },
    paymentDueDate: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      field: 'payment_due_date',
    },
    lateFee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      field: 'late_fee',
    },
    nsfFee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      field: 'nsf_fee',
      comment: 'Fee charged for returned payments due to non-sufficient funds'
    },
    earlyTerminationFee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'early_termination_fee',
      comment: 'Fixed fee for early lease termination'
    },
    numberOfPeople: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'number_of_people',
      comment: 'Number of people living in the property'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
    },
  }, {
    tableName: 'leases',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  Lease.associate = function(models) {
    // Many-to-many relationship with Users (tenants)
    Lease.belongsToMany(models.User, {
      through: 'lease_tenants', 
      foreignKey: 'lease_id',
      otherKey: 'user_id',
      as: 'tenants',
      timestamps: true,
    });

    // One-to-many relationship with Properties
    Lease.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
    });

    // One-to-many relationship with Users (landlord/owner)
    Lease.belongsTo(models.User, {
      foreignKey: 'landlordId',
      as: 'landlord',
    });

    // One-to-many relationship with LeaseFiles
    Lease.hasMany(models.LeaseFile, {
      foreignKey: 'leaseId',
      as: 'files',
    });

    // One-to-many relationship with Occupants
    Lease.hasMany(models.Occupant, {
      foreignKey: 'leaseId',
      as: 'occupants',
    });
  };

  return Lease;
};
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const User = sequelize.define(
    'User',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      firstName: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'First name is required' },
          len: {
            args: [2, 50],
            msg: 'First name must be between 2 and 50 characters',
          },
        },
      },
      lastName: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Last name is required' },
          len: {
            args: [2, 50],
            msg: 'Last name must be between 2 and 50 characters',
          },
        },
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: { msg: 'Please provide a valid email' },
          notEmpty: { msg: 'Email is required' },
        },
        set(value) {
          this.setDataValue('email', value.toLowerCase().trim());
        },
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Password is required' },
        },
      },
      phone: {
        type: DataTypes.STRING,
        allowNull: true,
        set(value) {
          // Log the raw input value for debugging
          if (value) {
            console.log('Phone input value:', value, 'Type:', typeof value);
          }
          this.setDataValue('phone', value);
        },
        validate: {
          isValidPhone(value) {
            if (!value) return; // Allow null/empty
            
            // Remove all non-digit characters except leading +
            const digits = value.replace(/[^\d+]/g, '');
            
            // Check if we have at least 10 digits
            const digitCount = digits.replace(/\D/g, '').length;
            if (digitCount < 10) {
              throw new Error('Phone number must have at least 10 digits');
            }
            
            // Check total length (including + and any other allowed chars)
            if (value.length > 20) {
              throw new Error('Phone number cannot be longer than 20 characters');
            }
          }
        }
      },
      addressLine1: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          notEmpty: { msg: 'Address line 1 is required' },
          len: {
            args: [2, 255],
            msg: 'Address line 1 must be between 2 and 255 characters',
          },
        },
      },
      addressLine2: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          notEmpty: { msg: 'City is required' },
          len: {
            args: [2, 100],
            msg: 'City must be between 2 and 100 characters',
          },
        },
      },
      state: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          notEmpty: { msg: 'State/province is required' },
          len: {
            args: [2, 100],
            msg: 'State/province must be between 2 and 100 characters',
          },
        },
      },
      postalCode: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          notEmpty: { msg: 'Postal/ZIP code is required' },
          len: {
            args: [2, 20],
            msg: 'Postal/ZIP code must be between 2 and 20 characters',
          },
        },
      },
      country: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: 'United States',
        validate: {
          notEmpty: { msg: 'Country is required' },
        },
      },
      emailVerificationToken: {
        type: DataTypes.STRING,
        allowNull: true,
        },
      isEmailVerified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      lastLogin: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      passwordResetToken: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      passwordResetExpires: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      signature: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      tableName: 'users',
      paranoid: true, // Enable soft deletes
      defaultScope: {
        attributes: { 
          exclude: [
            'password', 
            'passwordResetToken', 
            'passwordResetExpires',
            'emailVerificationToken'
          ] 
        },
      },
      scopes: {
        withPassword: {
          attributes: { include: ['password'] },
        },
        withResetToken: {
          attributes: { 
            include: [
              'passwordResetToken', 
              'passwordResetExpires',
              'emailVerificationToken'
            ] 
          },
        },
        withEmailVerification: {
          attributes: { include: ['emailVerificationToken', 'isEmailVerified'] },
        },
        withDeleted: {
          paranoid: false // Allow querying soft-deleted records
        }
      },
    }
  );

  // Define associations
  User.associate = (models) => {
    // Pets relationship (one-to-many)
    User.hasMany(models.Pet, {
      foreignKey: 'userId',
      as: 'pets',
    });
    
    // Account relationship (many-to-many through AccountUser)
    User.belongsToMany(models.Account, {
      through: models.AccountUser,
      foreignKey: 'userId',
      as: 'accounts',
    });
    
    // One-to-many relationship with AccountUser
    User.hasMany(models.AccountUser, {
      foreignKey: 'userId',
      as: 'accountUsers',
    });

    // Roles are now managed through AccountUser model

    // PropertyUser relationship (use this to get all property associations)
    User.hasMany(models.PropertyUser, {
      foreignKey: 'userId',
      as: 'propertyUsers',
      onDelete: 'CASCADE',
    });

    // Property relationship through PropertyUser
    User.belongsToMany(models.Property, {
      through: {
        model: models.PropertyUser,
        unique: false, // Allow multiple roles per user per property
      },
      foreignKey: 'userId',
      as: 'properties',
    });

    // Note: Role association through PropertyUser has been removed as part of the account-user refactoring
    // Roles are now stored directly in the PropertyUser model as strings

    // Refresh tokens relationship
    User.hasMany(models.RefreshToken, {
      foreignKey: 'userId',
      as: 'refreshTokens',
      onDelete: 'CASCADE',
    });

    // Vehicles relationship (one-to-many)
    User.hasMany(models.Vehicle, {
      foreignKey: 'userId',
      as: 'vehicles',
    });

    // Many-to-many relationship with Leases (as tenant)
    User.belongsToMany(models.Lease, {
      through: 'lease_tenants', // Must match the name in Lease model
      foreignKey: 'user_id',
      otherKey: 'lease_id',
      as: 'leases',
      timestamps: true,
    });
  };

  return User;
};

// models/propertyFile.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const PropertyFile = sequelize.define('PropertyFile', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_name',
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_key',
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_type',
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'property_files',
    underscored: true,
  });

  PropertyFile.associate = (models) => {
    PropertyFile.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
    });
    PropertyFile.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader',
    });
  };

  return PropertyFile;
};
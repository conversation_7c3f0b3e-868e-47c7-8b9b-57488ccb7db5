const { createHash } = require('crypto');

/**
 * Generate a SHA-256 hash of the token
 * @param {string} token - The JWT token
 * @returns {string} - The hashed token
 */
const hashToken = (token) => {
  return createHash('sha256').update(token).digest('hex');
};

module.exports = (sequelize, DataTypes) => {
  const TokenBlacklist = sequelize.define(
    'TokenBlacklist',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tokenHash: {
        type: DataTypes.STRING(64), // SHA-256 produces a 64-character hex string
        allowNull: false,
        unique: true,
        field: 'token_hash',
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'expires_at',
      },
    },
    {
      tableName: 'token_blacklists',
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ['token_hash'],
        },
        {
          fields: ['expires_at'],
        },
      ],
    }
  );

  // Add a class method to help with hashing
  TokenBlacklist.hashToken = hashToken;

  return TokenBlacklist;
};

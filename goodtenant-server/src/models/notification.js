const { DataTypes } = require('sequelize');
const { NOTIFICATION_TYPES, NOTIFICATION_ENTITIES } = require('../constants/notifications');

module.exports = (sequelize) => {
  const Notification = sequelize.define(
    'Notification',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Title is required' },
          len: {
            args: [2, 100],
            msg: 'Title must be between 2 and 100 characters',
          },
        },
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Message is required' },
        },
      },
      type: {
        type: DataTypes.ENUM(Object.values(NOTIFICATION_TYPES)),
        allowNull: false,
      },
      isRead: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      entityType: {
        type: DataTypes.ENUM(Object.values(NOTIFICATION_ENTITIES)),
        allowNull: true,
        comment: 'Type of the related entity (e.g., Task, User, Property)',
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: true,
        comment: 'ID of the related entity',
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Additional data related to the notification',
      },
    },
    {
      tableName: 'notifications',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['userId'],
        },
        {
          fields: ['isRead'],
        },
        {
          fields: ['createdAt'],
        },
      ],
    }
  );

  Notification.associate = (models) => {
    Notification.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    Notification.belongsTo(models.User, {
      foreignKey: 'createdById',
      as: 'creator',
      onDelete: 'SET NULL',
    });
  };

  return Notification;
};

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const LeaseFile = sequelize.define('LeaseFile', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_name',
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_key',
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_type',
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
    },
  }, {
    tableName: 'lease_files',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  LeaseFile.associate = function(models) {
    // Many-to-one relationship with Lease
    LeaseFile.belongsTo(models.Lease, {
      foreignKey: 'leaseId',
      as: 'lease',
    });

    // Many-to-one relationship with User (uploader)
    LeaseFile.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader',
    });
  };

  return LeaseFile;
};
const { DataTypes } = require('sequelize');
const crypto = require('crypto');

function hashToken(token) {
  return crypto.createHash('sha256').update(token).digest('hex');
}

module.exports = (sequelize) => {
  const RefreshToken = sequelize.define(
    'RefreshToken',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      token: {
        type: DataTypes.STRING(1000),
        allowNull: false,
      },
      tokenHash: {
        type: DataTypes.STRING(64), // SHA-256 produces a 64-character hex string
        allowNull: false,
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      isRevoked: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      replacedByToken: {
        type: DataTypes.STRING(1000),
        allowNull: true,
      },
      ipAddress: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      userAgent: {
        type: DataTypes.STRING(500),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      tableName: 'refresh_tokens',
      indexes: [
        {
          unique: true,
          fields: ['tokenHash'],
        },
        {
          fields: ['userId'],
        },
        {
          fields: ['expiresAt'],
        },
      ],
    }
  );

  RefreshToken.beforeValidate((refreshToken) => {
    if (refreshToken.token) {
      refreshToken.tokenHash = hashToken(refreshToken.token);
    }
  });

  RefreshToken.findByToken = async function(token) {
    return this.findOne({
      where: {
        tokenHash: hashToken(token)
      }
    });
  };

  RefreshToken.associate = (models) => {
    RefreshToken.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return RefreshToken;
};

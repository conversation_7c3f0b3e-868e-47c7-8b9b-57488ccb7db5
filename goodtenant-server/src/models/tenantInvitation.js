const { v4: uuidv4 } = require('uuid');
const { addDays } = require('date-fns');

module.exports = (sequelize, DataTypes) => {
  const TenantInvitation = sequelize.define('TenantInvitation', {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: () => uuidv4(),
      allowNull: false
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false
    },
    propertyId: {
      type: DataTypes.UUID,
      allowNull: false
    },
    role: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: {
          msg: 'Email must be a valid email address'
        }
      }
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('pending', 'accepted', 'expired', 'cancelled'),
      defaultValue: 'pending',
      allowNull: false
    },
    token: {
      type: DataTypes.UUID,
      defaultValue: () => uuidv4(),
      allowNull: false
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: () => addDays(new Date(), 7) // 7 days expiry by default
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false
    }
  }, {
    tableName: 'tenant_invitations',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['token']
      },
      {
        fields: ['property_id', 'email', 'status']
      }
    ],
    hooks: {
      beforeCreate: (invitation) => {
        invitation.email = invitation.email.toLowerCase();
      }
    }
  });

  TenantInvitation.associate = (models) => {
    // Association with Property
    TenantInvitation.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property'
    });

    // Association with Account
    TenantInvitation.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account'
    });

    // Note: Role association has been removed as part of the account-user refactoring
    // Roles are now stored directly in the TenantInvitation model as strings

    // Association with User who created the invitation
    TenantInvitation.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  };

  return TenantInvitation;
};

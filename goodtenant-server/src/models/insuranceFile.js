module.exports = (sequelize, DataTypes) => {
  const InsuranceFile = sequelize.define('InsuranceFile', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_key',
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_type',
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'insurance_files',
    underscored: true,
  });

  InsuranceFile.associate = (models) => {
    InsuranceFile.belongsTo(models.Insurance, {
      foreignKey: 'insuranceId',
      as: 'insurance',
    });
    InsuranceFile.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader',
    });
  };

  return InsuranceFile;
};

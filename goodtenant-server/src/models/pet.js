const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Pet = sequelize.define(
    'Pet',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Pet name is required' },
          len: {
            args: [2, 50],
            msg: 'Name must be between 2 and 50 characters',
          },
        },
      },
      type: {
        type: DataTypes.ENUM('dog', 'cat', 'bird', 'fish', 'reptile', 'small_animal', 'other'),
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Pet type is required' },
          isIn: {
            args: [['dog', 'cat', 'bird', 'fish', 'reptile', 'small_animal', 'other']],
            msg: 'Invalid pet type',
          },
        },
      },
      size: {
        type: DataTypes.ENUM('small', 'medium', 'large', 'xlarge'),
        allowNull: true,
      },
      isEmotionalSupport: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      hasVaccinations: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      breed: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      weight: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: true,
        validate: {
          min: 0,
          max: 1000
        }
      },
      age: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: 0,
          max: 50
        }
      },
      sex: {
        type: DataTypes.ENUM('male', 'female', 'unknown'),
        allowNull: true
      },
      registration: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
    },
    {
      timestamps: true,
      tableName: 'pets',
      indexes: [
        {
          fields: ['userId'],
        },
        {
          fields: ['type'],
        },
        {
          fields: ['breed'],
        },
        {
          fields: ['registration'],
        },
      ],
    }
  );

  // Define associations
  Pet.associate = (models) => {
    Pet.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'owner',
    });
  };

  return Pet;
};

// models/maintenanceTicket.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const MaintenanceTicket = sequelize.define('MaintenanceTicket', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('open', 'in_progress', 'on_hold', 'completed', 'cancelled'),
      defaultValue: 'open',
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'emergency'),
      defaultValue: 'medium',
    },
    reportedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    assignedTo: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    propertyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'properties',
        key: 'id',
      },
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  }, {
    tableName: 'maintenance_tickets',
    timestamps: true,
    underscored: true,
  });

  MaintenanceTicket.associate = (models) => {
    MaintenanceTicket.belongsTo(models.User, {
      foreignKey: 'reportedBy',
      as: 'reporter',
    });
    MaintenanceTicket.belongsTo(models.User, {
      foreignKey: 'assignedTo',
      as: 'assignee',
    });
    MaintenanceTicket.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
    });
    MaintenanceTicket.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
    });
    MaintenanceTicket.hasMany(models.MaintenanceFile, {
      foreignKey: 'ticketId',
      as: 'files',
    });
  };

  return MaintenanceTicket;
};
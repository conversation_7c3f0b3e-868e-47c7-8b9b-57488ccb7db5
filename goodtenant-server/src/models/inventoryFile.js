// src/models/inventoryFile.js
module.exports = (sequelize, DataTypes) => {
  const InventoryFile = sequelize.define('InventoryFile', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_name',
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_key',
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_type',
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'inventory_files',
    underscored: true,
  });

  InventoryFile.associate = (models) => {
    InventoryFile.belongsTo(models.Inventory, {
      foreignKey: 'inventoryId',
      as: 'inventory',
    });
    InventoryFile.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader',
    });
  };

  return InventoryFile;
};
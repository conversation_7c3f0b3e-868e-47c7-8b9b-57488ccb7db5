const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

const models = {};

// Read all model files dynamically
console.log('Loading model files from directory:', __dirname);
fs.readdirSync(__dirname)
  .filter((file) => {
    const include = (
      file !== 'index.js' &&
      file.endsWith('.js') &&
      !file.endsWith('.test.js')
    );
    console.log(`- ${file} ${include ? '(included)' : '(excluded)'}`);
    return include;
  })
  .forEach((file) => {
    try {
      const modelPath = path.join(__dirname, file);
      const model = require(modelPath)(sequelize, Sequelize.DataTypes);
      
      if (!model || !model.name) {
        logger.warn(`Skipping invalid model file: ${file} - Missing name property`);
        return;
      }
      
      models[model.name] = model;
      logger.debug(`Loaded model: ${model.name}`);
    } catch (error) {
      logger.error(`Error loading model ${file}:`, error);
      throw new Error(`Failed to load model ${file}: ${error.message}`);
    }
  });

// Define sync order based on dependencies
const syncOrder = [
  'User', 
  'Account',
  'AccountUser',
  'Role',
  'UserRole',
];

// Set up associations
Object.values(models).forEach((model) => {
  try {
    if (typeof model.associate === 'function') {
      model.associate(models);
      logger.debug(`Set up associations for model: ${model.name}`);
    }
  } catch (error) {
    logger.error(`Error setting up associations for ${model.name}:`, error);
    throw new Error(`Failed to set up associations for ${model.name}: ${error.message}`);
  }
});

// Add a function to sync all models
const syncModels = async (options = { alter: false, force: false }) => {
  try {
    await sequelize.sync(options);
    logger.info('Database synced successfully');
  } catch (error) {
    logger.error('Error syncing database:', error);
    throw error;
  }
};

module.exports = {
  sequelize,
  syncModels,
  ...models,
};

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Account = sequelize.define(
    'Account',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      status: {
        type: DataTypes.ENUM('active', 'past_due', 'unpaid', 'canceled', 'incomplete', 'incomplete_expired', 'trialing'),
        defaultValue: 'incomplete',
      },
      plan: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      currentPeriodStart: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      currentPeriodEnd: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      cancelAtPeriodEnd: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      stripeCustomerId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      stripeSubscriptionId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      paymentMethodId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      trialEnd: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      stripeSecretKey: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      stripePublishableKey: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      indexes: [
        {
          fields: ['stripeCustomerId'],
        },
        {
          fields: ['stripeSubscriptionId'],
        },
      ],
      tableName: 'accounts',
    }
  );

  Account.associate = (models) => {
    // Many-to-many relationship with users through AccountUser
    Account.belongsToMany(models.User, {
      through: models.AccountUser,
      foreignKey: 'accountId',
      as: 'users',
    });

    // One-to-many relationship with AccountUser
    Account.hasMany(models.AccountUser, {
      foreignKey: 'accountId',
      as: 'accountUsers',
    });
  };

  return Account;
};

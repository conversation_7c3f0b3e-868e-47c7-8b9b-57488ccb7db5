const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Tax = sequelize.define('Tax', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    taxType: {
      type: DataTypes.ENUM(
        'Property',
        'Income',
        'Sales',
        'Business',
        'Other'
      ),
      allowNull: false,
      field: 'tax_type',
      comment: 'Type of tax',
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Description or notes about the tax',
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
      comment: 'Website for tax payments or information',
    },
    accountNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'account_number',
      comment: 'Tax account number',
    },
    taxId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'tax_id',
      comment: 'Government-issued tax ID number',
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: 'Tax amount',
    },
    dueDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'due_date',
      comment: 'Due date for the tax payment',
    },
    paymentFrequency: {
      type: DataTypes.ENUM(
        'monthly',
        'quarterly',
        'semi-annually',
        'annually',
        'one-time'
      ),
      allowNull: false,
      field: 'payment_frequency',
      defaultValue: 'annually',
      comment: 'Frequency of tax payments',
    },
    status: {
      type: DataTypes.ENUM(
        'unpaid',
        'partially_paid',
        'paid',
        'overdue',
        'cancelled'
      ),
      defaultValue: 'unpaid',
      comment: 'Payment status of the tax',
    },
    paymentMethod: {
      type: DataTypes.ENUM(
        'credit_card',
        'bank_transfer',
        'check',
        'cash',
        'other'
      ),
      allowNull: true,
      field: 'payment_method',
      comment: 'Method used for payment',
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'payment_date',
      comment: 'Date when payment was made',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes or comments',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
      comment: 'Whether the tax record is active',
    },
    propertyId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'property_id',
      comment: 'Reference to associated property (if applicable)',
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'account_id',
      comment: 'Reference to the account this tax belongs to',
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'created_by',
      comment: 'User who created the record',
    },
    updatedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'updated_by',
      comment: 'User who last updated the record',
    },
  }, {
    tableName: 'taxes',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  // Define associations
  Tax.associate = (models) => {
    // Belongs to Property (optional, for property taxes)
    Tax.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    });

    // Belongs to Account
    Tax.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // Created by User
    Tax.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE'
    });

    // Updated by User
    Tax.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'CASCADE'
    });
  };

  return Tax;
};
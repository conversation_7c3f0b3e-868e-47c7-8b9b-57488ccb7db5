const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Vehicle = sequelize.define(
    'Vehicle',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      make: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Vehicle make is required' },
          len: {
            args: [2, 50],
            msg: 'Make must be between 2 and 50 characters',
          },
        },
      },
      model: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Vehicle model is required' },
          len: {
            args: [1, 50],
            msg: 'Model must be between 1 and 50 characters',
          },
        },
      },
      year: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Vehicle year is required' },
          isInt: { msg: 'Year must be a valid number' },
          min: { args: [1900], msg: 'Year must be 1900 or later' },
          max: { 
            args: [new Date().getFullYear() + 1], 
            msg: `Year cannot be in the future` 
          },
        },
      },
      color: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      licensePlate: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'License plate number',
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Any additional notes about the vehicle',
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
    },
    {
      timestamps: true,
      tableName: 'vehicles',
      indexes: [
        {
          fields: ['userId'],
        },
        {
          fields: ['licensePlate'],
          unique: true,
          where: {
            licensePlate: { [sequelize.Sequelize.Op.ne]: null },
          },
        },
      ],
    }
  );

  // Define associations
  Vehicle.associate = (models) => {
    Vehicle.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'owner',
    });
  };

  return Vehicle;
};

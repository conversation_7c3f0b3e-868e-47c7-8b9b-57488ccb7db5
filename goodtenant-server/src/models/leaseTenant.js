const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const LeaseTenant = sequelize.define('LeaseTenant', {
    leaseId: {
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      field: 'lease_id',
      references: {
        model: 'leases',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
  
  }, {
    tableName: 'lease_tenants',
    timestamps: true,
    underscored: true,
    // Disable the default id field since we're using composite primary key
    id: false,
    indexes: [
      // Index for faster lookups by user_id
      {
        fields: ['user_id'],
        name: 'idx_lease_tenant_user_id'
      },
      // Index for faster lookups by lease_id
      {
        fields: ['lease_id'],
        name: 'idx_lease_tenant_lease'
      },
      // Index for status filtering
      {
        fields: ['status'],
        name: 'idx_lease_tenant_status'
      }
    ]
  });

  // Add model associations
  LeaseTenant.associate = (models) => {
    // Belongs to Lease
    LeaseTenant.belongsTo(models.Lease, {
      foreignKey: 'leaseId',
      as: 'lease',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // Belongs to User
    LeaseTenant.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });
  };
  
  return LeaseTenant;
};

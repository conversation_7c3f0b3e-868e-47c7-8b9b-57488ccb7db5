const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Payment = sequelize.define(
    'Payment',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING(3),
        defaultValue: 'USD',
      },
      status: {
        type: DataTypes.ENUM(
          'pending',
          'completed',
          'failed',
          'refunded',
          'partially_refunded',
          'cancelled'
        ),
        defaultValue: 'pending',
      },
      paymentMethod: {
        type: DataTypes.ENUM('stripe', 'cash', 'bank_transfer', 'check', 'other'),
        allowNull: false,
        field: 'payment_method',
      },
      paymentDate: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'payment_date',
      },
      dueDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'due_date',
      },
      referenceNumber: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'reference_number',
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      receivedBy: {
        type: DataTypes.UUID,
        allowNull: true,
        field: 'received_by',
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      refundedAmount: {
        type: DataTypes.DECIMAL(10, 2),
        defaultValue: 0,
        field: 'refunded_amount',
      },
      payerId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: 'payer_id',
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
      },
      receiverId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: 'receiver_id',
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: 'account_id',
        references: {
          model: 'accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
        get() {
          const value = this.getDataValue('metadata');
          return value && typeof value === 'object' ? value : {};
        },
        set(value) {
          this.setDataValue('metadata', value && typeof value === 'object' ? value : {});
        }
      },
    },
    {
      timestamps: true,
      tableName: 'payments',
      underscored: true,
      indexes: [
        { fields: ['payer_id'] },
        { fields: ['receiver_id'] },
        { fields: ['account_id'] },
        { fields: ['created_at'] },
        { fields: ['updated_at'] },
      ],
    }
  );

  Payment.associate = (models) => {
    Payment.belongsTo(models.User, { 
      as: 'payer', 
      foreignKey: 'payer_id',
      targetKey: 'id'
    });
    
    Payment.belongsTo(models.User, { 
      as: 'receiver', 
      foreignKey: 'receiver_id',
      targetKey: 'id'
    });
    
    Payment.belongsTo(models.Account, { 
      foreignKey: 'account_id',
      targetKey: 'id',
      as: 'account',
      onDelete: 'CASCADE'
    });
    
    Payment.hasMany(models.PaymentReceipt, { 
      foreignKey: 'payment_id', 
      as: 'receipts',
      onDelete: 'CASCADE' 
    });
  };

  return Payment;
};

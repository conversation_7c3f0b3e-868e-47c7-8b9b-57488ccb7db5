// src/models/contact.js
module.exports = (sequelize, DataTypes) => {
  const Contact = sequelize.define('Contact', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    // Basic Information
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'first_name',
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'last_name',
    },
    company: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    jobTitle: {
      type: DataTypes.STRING,
      field: 'job_title',
      allowNull: true,
    },
    // Contact Details
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    mobile: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    // Address
    addressLine1: {
      type: DataTypes.STRING,
      field: 'address_line_1',
      allowNull: true,
    },
    addressLine2: {
      type: DataTypes.STRING,
      field: 'address_line_2',
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    postalCode: {
      type: DataTypes.STRING,
      field: 'postal_code',
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      defaultValue: 'USA',
      allowNull: true,
    },
    // Categorization
    type: {
      type: DataTypes.ENUM(
        'contractor',
        'vendor',
        'service_provider',
        'tenant',
        'owner',
        'manager',
        'other'
      ),
      allowNull: false,
      defaultValue: 'other',
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      field: 'is_primary',
      defaultValue: false,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      field: 'is_active',
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    // Foreign Keys
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'account_id',
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    propertyId: {
      type: DataTypes.UUID,
      field: 'property_id',
      allowNull: true,
      references: {
        model: 'properties',
        key: 'id',
      },
    },
    createdBy: {
      type: DataTypes.UUID,
      field: 'created_by',
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    updatedBy: {
      type: DataTypes.UUID,
      field: 'updated_by',
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'contacts',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  // Define associations
  Contact.associate = (models) => {
    // Belongs to Account
    Contact.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    // Optional: Belongs to Property
    Contact.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    // Created by User
    Contact.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
    });

    // Updated by User
    Contact.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'CASCADE',
    });

  };

  return Contact;
};
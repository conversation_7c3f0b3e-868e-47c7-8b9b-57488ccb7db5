// models/maintenanceFile.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const MaintenanceFile = sequelize.define('MaintenanceFile', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_name',
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_key',
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'file_type',
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'file_size',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    uploadedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'maintenance_files',
    underscored: true,
  });

  MaintenanceFile.associate = (models) => {
    MaintenanceFile.belongsTo(models.MaintenanceTicket, {
      foreignKey: 'ticketId',
      as: 'ticket',
    });
    MaintenanceFile.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader',
    });
  };

  return MaintenanceFile;
};
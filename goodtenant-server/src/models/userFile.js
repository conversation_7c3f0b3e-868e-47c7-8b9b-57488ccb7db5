// models/userFile.js
const { DataTypes } = require('sequelize');

console.log('Initializing UserFile model...');

module.exports = (sequelize) => {
  const UserFile = sequelize.define('UserFile', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fileKey: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'user_files',
    timestamps: true,
    paranoid: true,
  });

  UserFile.associate = (models) => {
    UserFile.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  console.log('UserFile model initialized:', UserFile.name);
  return UserFile;
};
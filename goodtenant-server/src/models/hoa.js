const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const HOA = sequelize.define('HOA', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'account_id',
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Monthly or annual HOA fee',
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        is: /^[\d\s\-().+]+$/,
      },
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    addressLine1: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'address_line1',
    },
    addressLine2: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'address_line2',
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING(2),
      allowNull: true,
      validate: {
        len: [0, 2],
      },
    },
    zipCode: {
      type: DataTypes.STRING(10),
      allowNull: true,
      field: 'zip_code',
      validate: {
        is: /^\d{5}(-\d{4})?$/,
      },
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'US',
    },
    managerName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'manager_name',
    },
    managerEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'manager_email',
      validate: {
        isEmail: true,
      },
    },
    managerPhone: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'manager_phone',
      validate: {
        is: /^[\d\s\-().+]+$/,
      },
    },
    gateCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'gate_code',
    },
    comments: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
    },
  }, {
    tableName: 'hoas',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  // Add associations
  HOA.associate = function(models) {
    // A HOA belongs to an Account
    HOA.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account'
    });
    
    // A HOA can have many properties
    HOA.hasMany(models.Property, { 
      foreignKey: 'hoaId',
      as: 'properties'
    });
    
    // A HOA can have many files
    HOA.hasMany(models.HOAFIle, {
      foreignKey: 'hoaId',
      as: 'files'
    });
  };

  return HOA;
};

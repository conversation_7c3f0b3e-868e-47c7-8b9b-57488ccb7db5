module.exports = (sequelize, DataTypes) => {
  const Utility = sequelize.define('Utility', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Reference to the account this utility belongs to',
    },
    propertyId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'properties',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'Reference to the property this utility is associated with (if any)',
    },
    utilityType: {
      type: DataTypes.ENUM(
        'electricity',
        'water',
        'gas',
        'internet',
        'trash',
        'sewer',
        'other'
      ),
      allowNull: false,
      field: 'utility_type',
      comment: 'Type of utility',
    },
    providerName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'provider_name',
      comment: 'Name of the utility provider',
    },
    accountNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'account_number',
      comment: 'Account number with the utility provider',
    },
    billingCycle: {
      type: DataTypes.ENUM('monthly', 'bi-monthly', 'quarterly', 'annually'),
      defaultValue: 'monthly',
      field: 'billing_cycle',
      comment: 'Billing frequency',
    },
    dueDate: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'due_date',
      validate: {
        min: 1,
        max: 31,
      },
      comment: 'Day of month when payment is due',
    },
    isIncludedInRent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_included_in_rent',
      comment: 'Whether this utility is included in the rent',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
      comment: 'Whether this utility is currently active',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes about the utility',
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      field: 'created_by',
      comment: 'User who created the record',
    },
    updatedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      field: 'updated_by',
      comment: 'User who last updated the record',
    },
  }, {
    tableName: 'utilities',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  // Define associations
  Utility.associate = (models) => {
    // Belongs to Account
    Utility.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // Optional: Belongs to Property
    Utility.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    });

    // Has many UtilityFiles
    Utility.hasMany(models.UtilityFile, {
      foreignKey: 'utilityId',
      as: 'files',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // Created by User
    Utility.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE'
    });

    // Updated by User
    Utility.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'CASCADE'
    });
  };

  return Utility;
};
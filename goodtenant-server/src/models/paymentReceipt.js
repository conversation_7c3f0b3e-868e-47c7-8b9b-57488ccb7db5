const { DataTypes } = require('sequelize');
const path = require('path');


module.exports = (sequelize) => {
  const PaymentReceipt = sequelize.define(
    'PaymentReceipt',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      fileUrl: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isUrl: {
            msg: 'File URL must be a valid URL',
          },
        },
      },
      fileType: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isIn: {
            args: [
              [
                'application/pdf',
                'image/jpeg',
                'image/png',
                'image/gif',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              ],
            ],
            msg: 'Invalid file type',
          },
        },
      },
      fileName: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'File name is required',
          },
        },
      },
      fileSize: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: 1,
          max: 10 * 1024 * 1024, // 10MB max file size
        },
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      isPrimary: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      uploadedById: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
      },
      paymentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'payments',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
    },
    {
      timestamps: true,
      tableName: 'payment_receipts',
      hooks: {
        beforeValidate: (receipt) => {
          // Extract file extension from URL if not provided
          if (receipt.fileUrl && !receipt.fileType) {
            const ext = path.extname(receipt.fileUrl).toLowerCase();
            const mimeTypes = {
              '.pdf': 'application/pdf',
              '.jpg': 'image/jpeg',
              '.jpeg': 'image/jpeg',
              '.png': 'image/png',
              '.gif': 'image/gif',
              '.doc': 'application/msword',
              '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            };
            
            if (mimeTypes[ext]) {
              receipt.fileType = mimeTypes[ext];
            }
          }
          
          // Set filename from URL if not provided
          if (receipt.fileUrl && !receipt.fileName) {
            receipt.fileName = path.basename(receipt.fileUrl);
          }
        },
      },
    }
  );

  PaymentReceipt.associate = (models) => {
    PaymentReceipt.belongsTo(models.Payment, {
      foreignKey: 'paymentId',
      onDelete: 'CASCADE',
    });
    
    PaymentReceipt.belongsTo(models.User, {
      as: 'uploadedBy',
      foreignKey: 'uploadedById',
    });
  };

  // Instance methods
  PaymentReceipt.prototype.getFileExtension = function() {
    const ext = path.extname(this.fileName).toLowerCase();
    return ext ? ext.substring(1) : '';
  };

  PaymentReceipt.prototype.isImage = function() {
    return this.fileType && this.fileType.startsWith('image/');
  };

  PaymentReceipt.prototype.isPdf = function() {
    return this.fileType === 'application/pdf';
  };

  PaymentReceipt.prototype.isDocument = function() {
    return [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ].includes(this.fileType);
  };

  return PaymentReceipt;
};

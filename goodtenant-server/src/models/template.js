const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Template = sequelize.define(
    'Template',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Template name is required' },
          len: {
            args: [2, 100],
            msg: 'Template name must be between 2 and 100 characters',
          },
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      type: {
        type: DataTypes.ENUM('LEASE', 'PET', 'INVENTORY','HOA'),
        allowNull: false,
        defaultValue: 'LEASE',
      },
      content: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {
          version: '1.0',
          content: [
            {
              type: 'paragraph',
              text: ''
            }
          ]
        },
        validate: {
          notEmpty: { msg: 'Template content is required' },
        },
      },
      usedVariables: {
        type: DataTypes.JSON,
        defaultValue: [],
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      isSystem: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
    },
    {
      timestamps: true,
      underscored: true,
      tableName: 'templates',
      hooks: {
        beforeValidate: (template) => {
          // Ensure content is properly initialized
          if (!template.content) {
            template.content = {
              version: '1.0',
              content: [
                {
                  type: 'paragraph',
                  text: ''
                }
              ]
            };
          }
          
          // Ensure usedVariables is always an array
          if (!template.usedVariables) {
            template.usedVariables = [];
          }
        },
        beforeSave: (template) => {
          // Update usedVariables from content if it's being updated
          if (template.changed('content')) {
            // This will be handled by the service layer now
          }
        }
      }
    }
  );

  return Template;
};

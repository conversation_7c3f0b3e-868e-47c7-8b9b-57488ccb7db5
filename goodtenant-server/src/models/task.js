const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Task = sequelize.define(
    'Task',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: { msg: 'Title is required' },
          len: {
            args: [2, 100],
            msg: 'Title must be between 2 and 100 characters',
          },
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      dueDate: {
        type: DataTypes.DATE,
        allowNull: false,
        validate: {
          notNull: { msg: 'Due date is required' },
          isDate: { msg: 'Invalid due date' },
        },
      },
      status: {
        type: DataTypes.ENUM(
          'pending',
          'in_progress',
          'completed',
          'overdue',
          'cancelled'
        ),
        defaultValue: 'pending',
        allowNull: false,
      },
      priority: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
        defaultValue: 'medium',
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(
          'tax',
          'utility',
          'insurance',
          'loan',
          'lease',
          'maintenance',
          'other'
        ),
        defaultValue: 'other',
        allowNull: false,
      },
      // Polymorphic association fields
      taskableId: {
        type: DataTypes.UUID,
        allowNull: true,
        comment: 'ID of the related entity',
      },
      taskableType: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Type of the related entity (e.g., Tax, Utility, Insurance, Lease)',
      },
      // Recurring task fields
      isRecurring: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      recurrencePattern: {
        type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'yearly'),
        allowNull: true,
      },
      nextRecurrenceDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      // Completion tracking
      completedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      completedById: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
    },
    {
      timestamps: true,
      tableName: 'tasks',
      paranoid: true, // Enable soft deletes
      indexes: [
        // For quick lookups by due date and status
        {
          fields: ['dueDate', 'status'],
        },
        // For polymorphic association lookups
        {
          fields: ['taskableId', 'taskableType'],
        },
        // For finding overdue tasks
        {
          fields: ['status'],
          where: {
            status: 'pending',
            dueDate: { [sequelize.Sequelize.Op.lt]: new Date() },
          },
        },
      ],
    }
  );

  // Define associations
  Task.associate = (models) => {
    // Task belongs to an account
    Task.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'CASCADE',
    });

    // Task is created by a user
    Task.belongsTo(models.User, {
      foreignKey: 'createdById',
      as: 'creator',
    });

    // Task can be assigned to a user
    Task.belongsTo(models.User, {
      foreignKey: 'assignedToId',
      as: 'assignee',
    });

    // Task can be related to a property
    Task.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
    });
  };

  // Hooks for automatic status updates
  Task.addHook('beforeSave', async (task) => {
    if (task.changed('dueDate') || task.changed('status')) {
      // Check if task is overdue
      if (task.status !== 'completed' && task.dueDate < new Date()) {
        task.status = 'overdue';
      }
    }

    // Set completedAt when status changes to completed
    if (task.changed('status') && task.status === 'completed' && !task.completedAt) {
      task.completedAt = new Date();
    }
  });

  return Task;
};

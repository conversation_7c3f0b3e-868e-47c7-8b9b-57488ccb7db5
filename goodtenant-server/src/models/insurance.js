const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Insurance = sequelize.define('Insurance', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    policyNumber: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'policy_number',
    },
    provider: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    insuranceType: {
      type: DataTypes.ENUM('Homeowners', 'Rental', 'Liability', 'Flood', 'Earthquake', 'Umbrella', 'Other'),
      allowNull: false,
      field: 'insurance_type',
      defaultValue: 'Homeowners',
    },
    effectiveStartDate: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'effective_start_date',
    },
    effectiveEndDate: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'effective_end_date',
    },
    premiumAmount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      field: 'premium_amount',
      comment: 'The amount paid for the insurance policy',
    },
    coverageAmount: {
      type: DataTypes.DECIMAL(14, 2),
      allowNull: false,
      field: 'coverage_amount',
      comment: 'Total coverage amount of the policy',
    },
    deductible: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      comment: 'Deductible amount for claims',
    },
    paymentFrequency: {
      type: DataTypes.ENUM('monthly', 'quarterly', 'semi-annually', 'annually'),
      allowNull: false,
      field: 'payment_frequency',
      defaultValue: 'annually',
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'USD',
    },
    agentName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'agent_name',
    },
    agentPhone: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'agent_phone',
      validate: {
        is: /^[\d\s\-().+]+$/,
      },
    },
    agentEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'agent_email',
      validate: {
        isEmail: true,
      },
    },
    propertyId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'property_id',
      references: {
        model: 'properties',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'account_id',
      references: {
        model: 'accounts',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'created_by',
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
    },
    updatedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'updated_by',
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
    },
  }, {
    tableName: 'insurances',
    timestamps: true,
    underscored: true,
    paranoid: true,
  });

  // Define associations
  Insurance.associate = (models) => {
    // Belongs to Property
    Insurance.belongsTo(models.Property, {
      foreignKey: 'propertyId',
      as: 'property',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // Belongs to Account
    Insurance.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // Created by User
    Insurance.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE'
    });

    // Updated by User
    Insurance.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'CASCADE'
    });
  };

  return Insurance;
};
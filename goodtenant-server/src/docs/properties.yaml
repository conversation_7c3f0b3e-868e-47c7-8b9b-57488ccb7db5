paths:
  /api/properties/account:
    get:
      tags: [Properties]
      summary: Get properties for current user's account with optional search and filters
      description: Retrieve all properties for the currently authenticated user's account with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term to filter properties by name, address, city, state, or zip code
          example: "Sunset"
        - in: query
          name: propertyType
          schema:
            type: string
            enum: [apartment, house, condo, townhouse, commercial, other]
          description: Filter properties by type
          example: "apartment"
        - in: query
          name: status
          schema:
            type: string
            enum: [vacant, occupied, under_maintenance, renovating, off_market]
          description: Filter properties by status
          example: "vacant"
      responses:
        200:
          description: List of properties retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyListResponse'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/ServerError'

  /api/properties:
    post:
      tags: [Properties]
      summary: Create a new property
      description: Create a new property under the authenticated user's account
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the property
                  example: "Sunset Villas"
                  minLength: 2
                  maxLength: 100
                description:
                  type: string
                  description: Description of the property
                  example: "A beautiful property with ocean view"
                propertyType:
                  type: string
                  enum: [apartment, house, condo, townhouse, commercial, other]
                  description: Type of the property
                  example: "apartment"
                status:
                  type: string
                  enum: [vacant, occupied, under_maintenance, renovating, off_market]
                  description: Status of the property
                  default: "vacant"
                  example: "vacant"
                addressLine1:
                  type: string
                  description: First line of the address
                  example: "123 Main St"
                  minLength: 2
                  maxLength: 255
                addressLine2:
                  type: string
                  description: Second line of the address (optional)
                  example: "Apt 4B"
                city:
                  type: string
                  description: City name
                  example: "New York"
                  minLength: 2
                  maxLength: 100
                state:
                  type: string
                  description: State or province
                  example: "NY"
                  minLength: 2
                  maxLength: 100
                postalCode:
                  type: string
                  description: ZIP or postal code
                  example: "10001"
                  minLength: 2
                  maxLength: 20
                country:
                  type: string
                  description: Country name
                  default: "United States"
                  example: "United States"
                latitude:
                  type: number
                  format: float
                  description: Latitude coordinate (optional)
                  example: 40.7128
                longitude:
                  type: number
                  format: float
                  description: Longitude coordinate (optional)
                  example: -74.0060
                priceAmount:
                  type: number
                  format: decimal
                  minimum: 0
                  description: |
                    The price amount (e.g., 1500.00). 
                    If provided, both priceCurrency and priceInterval must also be provided.
                  example: 2500.00
                priceCurrency:
                  type: string
                  minLength: 3
                  maxLength: 3
                  default: "USD"
                  description: |
                    ISO 4217 currency code (e.g., USD, EUR, GBP). 
                    Required if priceAmount is provided.
                  example: "USD"
                priceInterval:
                  type: string
                  enum: [hour, day, week, month, year]
                  description: |
                    The time interval for the price (e.g., 'month' for monthly rent).
                    Required if priceAmount is provided.
                  example: "month"
                depositAmount:
                  type: number
                  format: decimal
                  minimum: 0
                  description: Security deposit amount in the specified currency
                  example: 3000.00
                bedrooms:
                  type: integer
                  minimum: 0
                  description: Number of bedrooms in the property
                  example: 2
                bathrooms:
                  type: number
                  format: float
                  minimum: 0
                  description: Number of bathrooms in the property (can be a half-bath)
                  example: 1.5
                sizeSquareFeet:
                  type: integer
                  minimum: 0
                  description: Size of the property in square feet
                  example: 1200
                yearBuilt:
                  type: integer
                  minimum: 1500
                  maximum: 2025
                  description: Year the property was built
                  example: 2020
                hoaId:
                  type: string
                  format: uuid
                  description: ID of the HOA this property belongs to (optional)
                  example: "123e4567-e89b-12d3-a456-************"
              required:
                - name
                - propertyType
                - addressLine1
                - city
                - state
                - postalCode
      responses:
        201:
          description: Property created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyResponse'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/ServerError'

  /api/properties/hoa/{hoaId}:
    get:
      tags: [Properties]
      summary: Get properties by HOA ID
      description: Retrieve all properties associated with a specific HOA
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/hoaIdParam'
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        200:
          description: List of properties for the specified HOA
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyListResponse'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

  /api/properties/user/me:
    get:
      tags: [Properties]
      summary: Get current user's properties
      description: Retrieve a list of properties for the current user
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/pageParam'
        - $ref: '#/components/parameters/limitParam'
        - in: query
          name: role
          schema:
            type: string
            enum: [owner, manager, tenant, viewer]
          description: Filter properties by user's role
        - in: query
          name: status
          schema:
            type: string
            enum: [vacant, occupied, under_maintenance, renovating, off_market]
          description: Filter properties by status
          example: "vacant"
      responses:
        200:
          description: List of user's properties retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyListResponse'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/ServerError'

  /api/properties/{id}:
    get:
      tags: [Properties]
      summary: Get property by ID
      description: Retrieve property details by ID
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/propertyIdParam'
      responses:
        200:
          description: Property details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyResponse'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Properties]
      summary: Update a property
      description: Update property details (owner or manager only)
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/propertyIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the property
                  minLength: 2
                  maxLength: 100
                description:
                  type: string
                  description: Description of the property
                propertyType:
                  type: string
                  enum: [apartment, house, condo, townhouse, commercial, other]
                status:
                  type: string
                  enum: [vacant, occupied, under_maintenance, renovating, off_market]
                hoaId:
                  type: string
                  format: uuid
                  description: ID of the HOA this property belongs to (set to null to remove from HOA)
                  example: "123e4567-e89b-12d3-a456-************"
                addressLine1:
                  type: string
                  description: First line of the address
                  example: "123 Main St"
                addressLine2:
                  type: string
                  description: Second line of the address (optional)
                  example: "Apt 4B"
                city:
                  type: string
                  description: City name
                  example: "New York"
                state:
                  type: string
                  description: State or province
                  example: "NY"
                postalCode:
                  type: string
                  description: ZIP or postal code
                  example: "10001"
                priceAmount:
                  type: number
                  format: decimal
                  minimum: 0
                  description: |
                    The price amount (e.g., 1500.00). 
                    If provided, both priceCurrency and priceInterval must also be provided.
                  example: 2500.00
                priceCurrency:
                  type: string
                  minLength: 3
                  maxLength: 3
                  default: "USD"
                  description: |
                    ISO 4217 currency code (e.g., USD, EUR, GBP). 
                    Required if priceAmount is provided.
                  example: "USD"
                priceInterval:
                  type: string
                  enum: [hour, day, week, month, year]
                  description: |
                    The time interval for the price (e.g., 'month' for monthly rent).
                    Required if priceAmount is provided.
                  example: "month"
                depositAmount:
                  type: number
                  format: decimal
                  minimum: 0
                  description: Security deposit amount in the specified currency
                  example: 3000.00
                bedrooms:
                  type: integer
                  minimum: 0
                  description: Number of bedrooms in the property
                  example: 2
                bathrooms:
                  type: number
                  format: float
                  minimum: 0
                  description: Number of bathrooms in the property (can be a half-bath)
                  example: 1.5
                country:
                  type: string
                  example: "USA"
                latitude:
                  type: number
                  format: float
                  example: 40.7128
                longitude:
                  type: number
                  format: float
                  example: -74.0060
                yearBuilt:
                  type: integer
                  example: 2020
                sizeSquareFeet:
                  type: number
                  format: float
                  example: 1500.5
                isActive:
                  type: boolean
                  example: true

      responses:
        200:
          description: Property updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyResponse'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Properties]
      summary: Delete a property
      description: Delete a property (owner only)
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/propertyIdParam'
      responses:
        200:
          description: Property deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Property deleted successfully
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

  /api/properties/{propertyId}/users:
    get:
      tags: [Properties]
      summary: Get all users for a property
      description: Retrieve a paginated list of all users associated with a property
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/propertyIdParamPath'
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for pagination
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          description: Number of items per page
      responses:
        200:
          description: List of users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PropertyUserResponse'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
                      limit:
                        type: integer
                        example: 10
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Properties]
      summary: Add user to property
      description: Add a user to a property (account-level permissions required)
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/propertyIdParamPath'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: string
                  format: uuid
                  example: 123e4567-e89b-12d3-a456-************
                isPrimary:
                  type: boolean
                  example: false
                startDate:
                  type: string
                  format: date-time
                endDate:
                  type: string
                  format: date-time
                notes:
                  type: string
      responses:
        200:
          description: User added to property successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyResponse'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

  /api/properties/{propertyId}/users/{userId}:
    delete:
      tags: [Properties]
      summary: Remove user from property
      description: Remove a user from a property (owner or manager only, cannot remove last owner)
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/propertyIdParamPath'
        - in: path
          name: userId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the user to remove
      responses:
        200:
          description: User removed from property successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: User removed from property successfully
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

components:
  parameters:
    hoaIdParam:
      in: path
      name: hoaId
      required: true
      schema:
        type: string
        format: uuid
      description: HOA ID
    propertyIdParam:
      in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
      description: Property ID
    
    propertyIdParamPath:
      in: path
      name: propertyId
      required: true
      schema:
        type: string
        format: uuid
      description: Property ID

  schemas:
    AccountReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: "Acme Properties LLC"
        isActive:
          type: boolean
          example: true

    PropertyInput:
      type: object
      required:
        - name
        - addressLine1
        - city
        - state
        - postalCode
        - country
      properties:
        accountId:
          type: string
          format: uuid
          description: ID of the account this property belongs to (optional on update)
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: "Downtown Luxury Apartments"
        description:
          type: string
          example: "Luxury apartment complex in downtown area"
        propertyType:
          type: string
          enum: [apartment, house, condo, townhouse, commercial, other]
          example: "apartment"
        status:
          type: string
          enum: [vacant, occupied, under_maintenance, renovating, off_market]
          example: "vacant"
        addressLine1:
          type: string
          example: "123 Main St"
        addressLine2:
          type: string
          example: "Apt 4B"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"
        postalCode:
          type: string
          example: "10001"
          minLength: 2
          maxLength: 20
        priceAmount:
          type: number
          format: decimal
          minimum: 0
          description: |
            The price amount (e.g., 1500.00). 
            If provided, both priceCurrency and priceInterval must also be provided.
          example: 2500.00
        priceCurrency:
          type: string
          minLength: 3
          maxLength: 3
          default: "USD"
          description: |
            ISO 4217 currency code (e.g., USD, EUR, GBP). 
            Required if priceAmount is provided.
          example: "USD"
        priceInterval:
          type: string
          enum: [hour, day, week, month, year]
          description: |
            The time interval for the price (e.g., 'month' for monthly rent).
            Required if priceAmount is provided.
          example: "month"
        depositAmount:
          type: number
          format: decimal
          minimum: 0
          description: Security deposit amount in the specified currency
          example: 3000.00
        bedrooms:
          type: integer
          minimum: 0
          description: Number of bedrooms in the property
          example: 2
        bathrooms:
          type: number
          format: float
          minimum: 0
          description: Number of bathrooms in the property (can be a half-bath)
          example: 1.5
        country:
          type: string
          example: "USA"
        latitude:
          type: number
          format: float
          example: 40.7128
        longitude:
          type: number
          format: float
          example: -74.0060
        yearBuilt:
          type: integer
          example: 2020
        sizeSquareFeet:
          type: number
          format: float
          example: 1500.5
        isActive:
          type: boolean
          example: true

    PropertyResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/Property'

    PropertyListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/Property'
        pagination:
          type: object
          properties:
            total:
              type: integer
              example: 42
            page:
              type: integer
              example: 1
            totalPages:
              type: integer
              example: 5
            limit:
              type: integer
              example: 10

    Property:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the property
        accountId:
          type: string
          format: uuid
          description: ID of the account that owns the property
        hoaId:
          type: string
          format: uuid
          description: ID of the HOA this property belongs to, if any
          nullable: true
          example: "123e4567-e89b-12d3-a456-************"
        hoa:
          $ref: '#/components/schemas/HOARef'
        name:
          type: string
          description: Name of the property
        description:
          type: string
          description: Detailed description of the property
        propertyType:
          type: string
          enum: [apartment, house, condo, townhouse, commercial, other]
          description: Type of the property
        status:
          type: string
          enum: [vacant, occupied, under_maintenance, renovating, off_market]
          description: Current status of the property
        priceAmount:
          type: number
          format: decimal
          description: The price amount in the specified currency
        priceCurrency:
          type: string
          description: ISO 4217 currency code
        priceInterval:
          type: string
          enum: [hour, day, week, month, year]
          description: The time interval for the price
        depositAmount:
          type: number
          format: decimal
          description: Security deposit amount in the specified currency
        bedrooms:
          type: integer
          description: Number of bedrooms in the property
        bathrooms:
          type: number
          format: float
          description: Number of bathrooms in the property (can be a half-bath)
        addressLine1:
          type: string
          description: First line of the address
        addressLine2:
          type: string
          description: Second line of the address (optional)
        city:
          type: string
          description: City name
        state:
          type: string
          description: State or province
        postalCode:
          type: string
          description: ZIP or postal code
        country:
          type: string
          description: Country name
        latitude:
          type: number
          format: float
          description: Latitude coordinate
        longitude:
          type: number
          format: float
          description: Longitude coordinate
        yearBuilt:
          type: integer
          description: Year the property was built
        sizeSquareFeet:
          type: integer
          description: Size of the property in square feet
        isActive:
          type: boolean
          description: Whether the property is active
        createdAt:
          type: string
          format: date-time
          description: When the property was created
        updatedAt:
          type: string
          format: date-time
          description: When the property was last updated
        deletedAt:
          type: string
          format: date-time
          description: When the property was soft-deleted (if applicable)
          nullable: true
        users:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              firstName:
                type: string
              lastName:
                type: string
              email:
                type: string
              avatar:
                type: string
              PropertyUser:
                type: object
                properties:
                  startDate:
                    type: string
                    format: date-time
                  endDate:
                    type: string
                    format: date-time
                    nullable: true
                  isPrimary:
                    type: boolean
                  role:
                    $ref: '#/components/schemas/Role'

    Role:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: "owner"
        description:
          type: string
          example: "Property owner with full access"

    PropertyUserResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User ID
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
        phoneNumber:
          type: string
          description: User's phone number
          nullable: true
        isPrimary:
          type: boolean
          description: Whether this is the user's primary property
        startDate:
          type: string
          format: date
          description: Start date of the user's association with the property
          nullable: true
        endDate:
          type: string
          format: date
          description: End date of the user's association with the property
          nullable: true
        notes:
          type: string
          description: Additional notes about the user's association with the property
          nullable: true
        associationId:
          type: string
          format: uuid
          description: ID of the property-user association
        addedAt:
          type: string
          format: date-time
          description: When the user was added to the property

    HOARef:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the HOA
        name:
          type: string
          description: Name of the HOA
        description:
          type: string
          description: Description of the HOA
        createdAt:
          type: string
          format: date-time
          description: When the HOA was created
        updatedAt:
          type: string
          format: date-time
          description: When the HOA was last updated

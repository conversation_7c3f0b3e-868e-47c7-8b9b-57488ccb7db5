# src/docs/inventory.yaml
openapi: 3.0.0
info:
  title: Inventory API
  description: API for managing property inventory items
  version: 1.0.0
servers:
  - url: /api
    description: API server
tags:
  - name: Inventory
    description: Property inventory management

paths:
  /inventory:
    get:
      tags: [Inventory]
      summary: Get all inventory items
      description: Retrieve a list of inventory items with optional filtering and pagination
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: search
          schema:
            type: string
          description: Search term (searches name, description, serial number, model number, brand)
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
        - in: query
          name: status
          schema:
            type: string
            enum: [new, good, needs_maintenance, needs_replacement, disposed]
          description: Filter by item status
        - in: query
          name: category
          schema:
            type: string
            enum: [appliances, furniture, electronics, utilities, tools, fixtures, other]
          description: Filter by item category
      responses:
        '200':
          description: Successfully retrieved inventory items
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Inventory'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Inventory]
      summary: Create a new inventory item
      description: Create a new inventory item record
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InventoryInput'
      responses:
        '201':
          description: Inventory item created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Inventory'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

  /inventory/{id}:
    get:
      tags: [Inventory]
      summary: Get inventory item by ID
      description: Retrieve a single inventory item by its ID
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/idParam'
      responses:
        '200':
          description: Successfully retrieved inventory item
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Inventory'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Inventory]
      summary: Update an inventory item
      description: Update an existing inventory item
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/idParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InventoryInput'
      responses:
        '200':
          description: Inventory item updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Inventory'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Inventory]
      summary: Delete an inventory item
      description: Delete an inventory item (Admin only)
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/idParam'
      responses:
        '200':
          description: Inventory item deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Inventory item deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'

  /properties/{propertyId}/inventory:
    get:
      tags: [Inventory]
      summary: Get inventory items by property
      description: Retrieve all inventory items associated with a specific property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the property
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
      responses:
        '200':
          description: Successfully retrieved property inventory
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Inventory'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Inventory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "Refrigerator"
        description:
          type: string
          example: "Stainless steel French door refrigerator"
        category:
          type: string
          enum: [appliances, furniture, electronics, utilities, tools, fixtures, other]
          example: "appliances"
        status:
          type: string
          enum: [new, good, needs_maintenance, needs_replacement, disposed]
          example: "good"
        quantity:
          type: integer
          example: 1
        serialNumber:
          type: string
          example: "RF123456789"
        modelNumber:
          type: string
          example: "RF28R7351SR"
        brand:
          type: string
          example: "Samsung"
        purchaseDate:
          type: string
          format: date
          example: "2023-01-15"
        purchasePrice:
          type: number
          format: float
          example: 1999.99
        purchaseFrom:
          type: string
          example: "Best Buy"
        hasWarranty:
          type: boolean
          example: true
        warrantyExpiryDate:
          type: string
          format: date
          example: "2026-01-15"
        warrantyDetails:
          type: string
          example: "3-year manufacturer's warranty"
        location:
          type: string
          example: "Kitchen"
        lastMaintenanceDate:
          type: string
          format: date
          example: "2023-06-01"
        maintenanceFrequency:
          type: string
          enum: [weekly, monthly, quarterly, biannually, annually, as_needed]
          example: "annually"
        expectedLifespan:
          type: integer
          description: "Lifespan in months"
          example: 120
        disposalDate:
          type: string
          format: date
          example: "2033-01-15"
        disposalReason:
          type: string
          example: "End of useful life"
        propertyId:
          type: string
          format: uuid
        vendorId:
          type: string
          format: uuid
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        property:
          $ref: '#/components/schemas/PropertyReference'
        vendor:
          $ref: '#/components/schemas/ContactReference'
        creator:
          $ref: '#/components/schemas/UserReference'
        updater:
          $ref: '#/components/schemas/UserReference'

    InventoryInput:
      type: object
      required:
        - name
        - category
        - status
        - propertyId
      properties:
        name:
          type: string
          example: "Refrigerator"
        description:
          type: string
          example: "Stainless steel French door refrigerator"
        category:
          type: string
          enum: [appliances, furniture, electronics, utilities, tools, fixtures, other]
          example: "appliances"
        status:
          type: string
          enum: [new, good, needs_maintenance, needs_replacement, disposed]
          example: "good"
        quantity:
          type: integer
          minimum: 1
          example: 1
        serialNumber:
          type: string
          example: "RF123456789"
        modelNumber:
          type: string
          example: "RF28R7351SR"
        brand:
          type: string
          example: "Samsung"
        purchaseDate:
          type: string
          format: date
          example: "2023-01-15"
        purchasePrice:
          type: number
          format: float
          example: 1999.99
        purchaseFrom:
          type: string
          example: "Best Buy"
        hasWarranty:
          type: boolean
          example: true
        warrantyExpiryDate:
          type: string
          format: date
          example: "2026-01-15"
        warrantyDetails:
          type: string
          example: "3-year manufacturer's warranty"
        location:
          type: string
          example: "Kitchen"
        lastMaintenanceDate:
          type: string
          format: date
          example: "2023-06-01"
        maintenanceFrequency:
          type: string
          enum: [weekly, monthly, quarterly, biannually, annually, as_needed]
          example: "annually"
        expectedLifespan:
          type: integer
          description: "Lifespan in months"
          example: 120
        disposalDate:
          type: string
          format: date
          example: "2033-01-15"
        disposalReason:
          type: string
          example: "End of useful life"
        propertyId:
          type: string
          format: uuid
        vendorId:
          type: string
          format: uuid
          nullable: true

    PropertyReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        addressLine1:
          type: string
        city:
          type: string
        state:
          type: string

    ContactReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        company:
          type: string
        email:
          type: string
        phone:
          type: string

    UserReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string

    Pagination:
      type: object
      properties:
        total:
          type: integer
          example: 100
        page:
          type: integer
          example: 1
        totalPages:
          type: integer
          example: 10

  parameters:
    idParam:
      in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
      description: Inventory item ID
    page:
      in: query
      name: page
      schema:
        type: integer
        default: 1
      description: Page number for pagination
    limit:
      in: query
      name: limit
      schema:
        type: integer
        default: 10
      description: Number of items per page

  responses:
    BadRequest:
      description: Bad request - invalid input
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFound:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        error:
          type: string
          example: "Detailed error message"
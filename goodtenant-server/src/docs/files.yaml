paths:
  /api/files/{modelName}/{modelId}:
    get:
      tags: [Files]
      summary: Get all files for a model instance
      description: Retrieve a list of files associated with a model instance (Property, HOA, Insurance, Loan, Tax, Maintenance, Utility, Inventory, Lease)
      parameters:
        - name: modelName
          in: path
          required: true
          schema:
            type: string
            enum: [Property, HOA, Insurance, Loan, Tax, Maintenance, Utility, Inventory, Lease]
          description: Type of the model
        - name: modelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the model instance
      security:
        - bearerAuth: []
      responses:
        200:
          description: List of files retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  count:
                    type: integer
                    example: 1
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/File'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/ServerError'

  /api/files/{modelName}/{modelId}/upload:
    post:
      tags: [Files]
      summary: Upload a file to a model instance
      description: Upload a file and associate it with a model instance (Property, HOA, Insurance, Loan, Maintenance, Utility, Inventory, or Lease)
      parameters:
        - name: modelName
          in: path
          required: true
          schema:
            type: string
            enum: [Property, HOA, Insurance, Loan, Tax, Maintenance, Utility, Inventory, Lease]
          description: Type of the model
        - name: modelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the model instance
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The file to upload
                description:
                  type: string
                  description: Optional description of the file
      responses:
        201:
          description: File uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "File uploaded successfully"
                  data:
                    $ref: '#/components/schemas/File'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        500:
          $ref: '#/components/responses/ServerError'

  /api/files/{modelName}/{fileId}:
    delete:
      tags: [Files]
      summary: Delete a file
      description: Delete a file from storage and its metadata
      parameters:
        - name: modelName
          in: path
          required: true
          schema:
            type: string
            enum: [Property, HOA, Insurance, Loan, Tax, Maintenance, Utility, Inventory, Lease]
          description: Type of the model
        - name: fileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the file to delete
      security:
        - bearerAuth: []
      responses:
        200:
          description: File deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "File deleted successfully"
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

  /api/files/download/{modelName}/{fileKey}:
    get:
      tags: [Files]
      summary: Get a signed URL for downloading a file
      description: Returns a signed URL that can be used to download the file
      parameters:
        - name: modelName
          in: path
          required: true
          schema:
            type: string
            enum: [Property, HOA, Insurance, Loan, Tax, Maintenance, Utility, Inventory, Lease]
          description: Type of the model the file is associated with
        - name: fileKey
          in: path
          required: true
          schema:
            type: string
          description: The S3 key of the file to download
      security:
        - bearerAuth: []
      responses:
        200:
          description: Signed URL generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        format: uri
                        example: "https://example.com/files/123e4567-e89b-12d3-a456-************?token=abc123"
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    File:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        propertyId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        hoaId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        insuranceId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        loanId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        taxId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        fileName:
          type: string
          example: "document.pdf"
        fileKey:
          type: string
          example: "properties/123e4567-e89b-12d3-a456-************/1620000000000-document.pdf"
        fileType:
          type: string
          example: "application/pdf"
        fileSize:
          type: integer
          example: 1024
        isImage:
          type: boolean
          example: false
        description:
          type: string
          nullable: true
          example: "Important document"
        url:
          type: string
          format: uri
          example: "https://example.com/files/123e4567-e89b-12d3-a456-************?token=abc123"
        uploadedBy:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

  responses:
    BadRequest:
      description: Bad request - Invalid input
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "Invalid file type"
    Forbidden:
      description: Forbidden - Insufficient permissions
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "You don't have permission to access this resource"
    NotFound:
      description: The requested resource was not found
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "File not found"
    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              message:
                type: string
                example: "An unexpected error occurred"
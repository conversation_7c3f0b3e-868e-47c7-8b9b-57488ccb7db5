paths:
  /api/pets:
    get:
      tags: [Pets]
      summary: Get all pets for the authenticated user
      description: Retrieve a paginated list of pets for the authenticated user with optional search
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for pet name, breed, registration, or notes
      responses:
        200:
          description: List of pets retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Pet'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

    post:
      tags: [Pets]
      summary: Create a new pet
      description: Create a new pet for the authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePetInput'
      responses:
        201:
          description: Pet created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Pet'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/pets/{id}:
    get:
      tags: [Pets]
      summary: Get pet by ID
      description: Retrieve a specific pet by ID (must belong to the authenticated user)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Pet ID
      responses:
        200:
          description: Pet details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Pet'
        401:
          description: Unauthorized
        404:
          description: Pet not found or access denied
        500:
          description: Server error

    put:
      tags: [Pets]
      summary: Update a pet
      description: Update an existing pet (must belong to the authenticated user)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Pet ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePetInput'
      responses:
        200:
          description: Pet updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Pet'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        404:
          description: Pet not found or access denied
        500:
          description: Server error

    delete:
      tags: [Pets]
      summary: Delete a pet
      description: Delete a pet (must belong to the authenticated user)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Pet ID
      responses:
        200:
          description: Pet deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Pet deleted successfully"
        401:
          description: Unauthorized
        404:
          description: Pet not found or access denied
        500:
          description: Server error

components:
  schemas:
    Pet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "Buddy"
        type:
          type: string
          enum: [dog, cat, bird, fish, reptile, small_animal, other]
          example: "dog"
        size:
          type: string
          enum: [small, medium, large, xlarge]
          example: "medium"
          nullable: true
        isEmotionalSupport:
          type: boolean
          example: false
        hasVaccinations:
          type: boolean
          example: true
        notes:
          type: string
          example: "Friendly and well-behaved dog"
          nullable: true
        breed:
          type: string
          maxLength: 100
          example: "Golden Retriever"
          nullable: true
        weight:
          type: number
          format: float
          minimum: 0
          maximum: 1000
          example: 25.5
          nullable: true
        age:
          type: integer
          minimum: 0
          maximum: 50
          example: 3
          nullable: true
        sex:
          type: string
          enum: [male, female, unknown]
          example: "male"
          nullable: true
        registration:
          type: string
          maxLength: 50
          example: "AKC-12345"
          nullable: true
        userId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          example: "2025-06-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2025-06-01T12:00:00Z"
        owner:
          $ref: '#/components/schemas/UserReference'

    CreatePetInput:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 50
          example: "Buddy"
        type:
          type: string
          enum: [dog, cat, bird, fish, reptile, small_animal, other]
          example: "dog"
        size:
          type: string
          enum: [small, medium, large, xlarge]
          example: "medium"
          nullable: true
        isEmotionalSupport:
          type: boolean
          default: false
        hasVaccinations:
          type: boolean
          default: false
        notes:
          type: string
          example: "Friendly and well-behaved dog"
          nullable: true
        breed:
          type: string
          maxLength: 100
          example: "Golden Retriever"
          nullable: true
        weight:
          type: number
          format: float
          minimum: 0
          maximum: 1000
          example: 25.5
          nullable: true
        age:
          type: integer
          minimum: 0
          maximum: 50
          example: 3
          nullable: true
        sex:
          type: string
          enum: [male, female, unknown]
          example: "male"
          nullable: true
        registration:
          type: string
          maxLength: 50
          example: "AKC-12345"
          nullable: true

    UpdatePetInput:
      type: object
      minProperties: 1
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 50
          example: "Buddy"
        type:
          type: string
          enum: [dog, cat, bird, fish, reptile, small_animal, other]
          example: "dog"
        size:
          type: string
          enum: [small, medium, large, xlarge]
          example: "medium"
          nullable: true
        isEmotionalSupport:
          type: boolean
          example: false
        hasVaccinations:
          type: boolean
          example: true
        notes:
          type: string
          example: "Friendly and well-behaved dog"
          nullable: true
        breed:
          type: string
          maxLength: 100
          example: "Golden Retriever"
          nullable: true
        weight:
          type: number
          format: float
          minimum: 0
          maximum: 1000
          example: 25.5
          nullable: true
        age:
          type: integer
          minimum: 0
          maximum: 50
          example: 3
          nullable: true
        sex:
          type: string
          enum: [male, female, unknown]
          example: "male"
          nullable: true
        registration:
          type: string
          maxLength: 50
          example: "AKC-12345"
          nullable: true

    UserReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"

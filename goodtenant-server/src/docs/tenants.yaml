paths:
  /api/tenants/properties/invitations/{propertyId}:
    post:
      tags: [Tenant Invitations]
      summary: Create tenant invitation
      description: Create a new tenant invitation for a property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the property
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, firstName, lastName]
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                firstName:
                  type: string
                  example: "John"
                lastName:
                  type: string
                  example: "Doe"
                phone:
                  type: string
                  example: "+**********"
      responses:
        201:
          description: Tenant invitation created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Tenant invitation created successfully"
                  data:
                    $ref: '#/components/schemas/TenantInvitation'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        404:
          description: Property not found
        409:
          description: Pending invitation already exists for this email
        500:
          description: Server error

    get:
      tags: [Tenant Invitations]
      summary: Get property invitations
      description: Get paginated list of tenant invitations for a property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the property
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, completed, cancelled, expired]
          description: Filter by invitation status
      responses:
        200:
          description: List of tenant invitations retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/TenantInvitation'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        404:
          description: Property not found
        500:
          description: Server error

  /api/tenants/invitations/{invitationId}:
    delete:
      tags: [Tenant Invitations]
      summary: Cancel invitation
      description: Cancel a pending tenant invitation
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: invitationId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the invitation to cancel
      responses:
        200:
          description: Invitation cancelled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Invitation cancelled successfully"
        401:
          description: Unauthorized
        404:
          description: Invitation not found or already processed
        500:
          description: Server error

  /api/tenants/invitations/{invitationId}/resend:
    post:
      tags: [Tenant Invitations]
      summary: Resend invitation
      description: Resend a tenant invitation email
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: invitationId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the invitation to resend
      responses:
        200:
          description: Invitation resent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Invitation resent successfully"
                  data:
                    $ref: '#/components/schemas/TenantInvitation'
        401:
          description: Unauthorized
        404:
          description: Invitation not found or already processed
        500:
          description: Server error

  /api/public/invitations/{token}/finalize:
    patch:
      tags: [Tenant Onboarding]
      summary: Finalize tenant onboarding
      description: Mark a tenant invitation as accepted after successful onboarding using the invitation token
      parameters:
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: Invitation token received in the invitation email
      responses:
        200:
          description: Tenant onboarding finalized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Tenant onboarding finalized successfully"
                  data:
                    type: object
                    properties:
                      invitationId:
                        type: string
                        format: uuid
                        example: "123e4567-e89b-12d3-a456-************"
                      status:
                        type: string
                        example: "accepted"
                      acceptedAt:
                        type: string
                        format: date-time
                        example: "2025-06-10T00:00:00.000Z"
        400:
          description: Bad request (e.g., invitation already accepted)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Invitation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/public/invitations/{token}/verify:
    get:
      tags: [Tenant Onboarding]
      summary: Verify invitation token
      description: Verify if an invitation token is valid and get invitation details
      parameters:
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: Invitation token
      responses:
        200:
          description: Token is valid
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Valid invitation"
                  data:
                    $ref: '#/components/schemas/InvitationVerification'
        404:
          description: Invalid or expired invitation
        500:
          description: Server error

  /api/public/upload/{token}:
    post:
      tags: [Tenant Onboarding]
      summary: Upload a file during tenant onboarding
      description: Upload a file during the tenant onboarding process using a valid invitation token
      security: []
      parameters:
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: Valid invitation token
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required: [file, modelName, modelId]
              properties:
                file:
                  type: string
                  format: binary
                  description: The file to upload
                modelName:
                  type: string
                  enum: [Property, HOA, Insurance, Loan, Tax, Maintenance, Utility, Inventory, Lease]
                  description: Type of the model this file belongs to
                modelId:
                  type: string
                  format: uuid
                  description: ID of the model instance this file belongs to
                description:
                  type: string
                  description: Optional file description
      responses:
        201:
          description: File uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/File'
        400:
          description: Invalid input or token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/public/invitations/{token}/complete:
    post:
      tags: [Tenant Onboarding]
      summary: Complete tenant onboarding
      description: Complete the tenant onboarding process using a valid invitation token
      parameters:
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: Invitation token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [userData]
              properties:
                userData:
                  type: object
                  required: [firstName, lastName, password]
                  properties:
                    firstName:
                      type: string
                      minLength: 2
                      maxLength: 50
                      example: "John"
                    lastName:
                      type: string
                      minLength: 2
                      maxLength: 50
                      example: "Doe"
                    password:
                      type: string
                      format: password
                      minLength: 8
                      example: "SecurePass123!"
                    phoneNumber:
                      type: string
                      pattern: '^\+?[1-9]\d{1,14}$'
                      description: E.164 format (e.g., +**********)
                      example: "+**********"
                additionalData:
                  type: object
                  description: Optional additional data for tenant onboarding
                  properties:
                    vehicles:
                      type: array
                      description: List of vehicles (optional)
                      items:
                        type: object
                        required: [make, model, year, licensePlate]
                        properties:
                          make:
                            type: string
                            example: "Toyota"
                          model:
                            type: string
                            example: "Camry"
                          year:
                            type: integer
                            minimum: 1900
                            maximum: 2100
                            example: 2020
                          color:
                            type: string
                            example: "Blue"
                          licensePlate:
                            type: string
                            example: "ABC123"
                    pets:
                      type: array
                      description: List of pets (optional)
                      items:
                        type: object
                        required: [name, type]
                        properties:
                          name:
                            type: string
                            example: "Buddy"
                          type:
                            type: string
                            example: "Dog"
                          breed:
                            type: string
                            example: "Golden Retriever"
                          weight:
                            type: number
                            minimum: 0
                            maximum: 1000
                            example: 25.5
                          age:
                            type: integer
                            minimum: 0
                            maximum: 100
                            example: 3
                          sex:
                            type: string
                            enum: [male, female, unknown]
                            example: "male"
                          registration:
                            type: string
                            example: "AKC-12345"
                          notes:
                            type: string
                            example: "Friendly but barks at mailmen"
                    occupants:
                      type: array
                      description: List of additional occupants (optional)
                      items:
                        type: object
                        required: [firstName, lastName, relationship]
                        properties:
                          firstName:
                            type: string
                            minLength: 2
                            maxLength: 50
                            example: "Jane"
                          lastName:
                            type: string
                            minLength: 2
                            maxLength: 50
                            example: "Doe"
                          age:
                            type: integer
                            minimum: 0
                            maximum: 120
                            example: 30
                          relationship:
                            type: string
                            enum: [spouse, child, parent, sibling, other_relative, roommate, other]
                            example: "spouse"
                          notes:
                            type: string
                            example: "Primary contact for emergencies"
      responses:
        200:
          description: Onboarding completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Tenant onboarding completed successfully"
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      property:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          name:
                            type: string
                      children:
                        type: array
                        items:
                          $ref: '#/components/schemas/Child'
        400:
          description: Invalid input data
        404:
          description: Invalid or expired invitation
        409:
          description: User with this email already exists
        500:
          description: Server error

  /api/public/leases/{leaseId}/document/{token}:
    get:
      tags: [Tenant Leases]
      summary: Get lease document content (Public)
      description: |
        Get the content of a lease document for display or signing using a valid invitation token.
        
        **Access Control:**
        - Public access with a valid invitation token
        - Token must be provided in the URL
      parameters:
        - in: path
          name: leaseId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease to get document for
        - in: path
          name: token
          required: true
          schema:
            type: string
            format: uuid
          description: Valid invitation token for accessing the lease document

  /api/public/leases/{leaseId}/hoa-document/{token}:
    get:
      tags: [Tenant Leases]
      summary: Get HOA document content (Public)
      description: |
        Get the content of an HOA document for display or signing using a valid invitation token.
        This endpoint is accessible without authentication but requires a valid invitation token.
        The document includes property, tenant, vehicle, pet, and occupant information.
      parameters:
        - in: path
          name: leaseId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease
        - in: path
          name: token
          required: true
          schema:
            type: string
          description: Valid invitation token
      responses:
        200:
          description: HOA document content retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      hoa:
                        $ref: '#/components/schemas/HOA'
                      property:
                        $ref: '#/components/schemas/Property'
                      lease:
                        $ref: '#/components/schemas/Lease'
                      tenants:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
                      vehicles:
                        type: array
                        items:
                          $ref: '#/components/schemas/Vehicle'
                      occupants:
                        type: array
                        items:
                          $ref: '#/components/schemas/Occupant'
                      pets:
                        type: array
                        items:
                          $ref: '#/components/schemas/Pet'
                      content:
                        type: object
                        description: Processed template content with variables replaced
                      generatedOn:
                        type: string
                        format: date
                        example: '2025-06-09'
        400:
          description: Invalid input or missing required fields
        401:
          description: Invalid or expired token
        404:
          description: Lease not found or no HOA associated with property
        500:
          description: Server error while generating document

  /api/public/leases/{leaseId}/pet-document/{token}:
    get:
      tags: [Tenant Leases]
      summary: Get pet document content (Public)
      description: |
        Get the content of a pet document for display or signing using a valid invitation token.
        This endpoint is accessible without authentication but requires a valid invitation token.
        
        **Access Control:**
        - Public access with a valid invitation token
        - Token must be provided in the URL
      parameters:
        - in: path
          name: leaseId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease to get pet document for
        - in: path
          name: token
          required: true
          schema:
            type: string
            format: uuid
          description: Valid invitation token for accessing the pet document
      responses:
        200:
          description: Pet document content retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      documentName:
                        type: string
                        example: "pet-addendum-12345-1623264000000.html"
                      templateId:
                        type: string
                        format: uuid
                        example: "123e4567-e89b-12d3-a456-************"
                      leaseId:
                        type: string
                        format: uuid
                        example: "123e4567-e89b-12d3-a456-************"
                      content:
                        type: string
                        example: "<html>...</html>"
                      variables:
                        type: object
                        properties:
                          property:
                            $ref: '#/components/schemas/Property'
                          landlord:
                            $ref: '#/components/schemas/User'
                          lease:
                            $ref: '#/components/schemas/Lease'
                          tenants:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: string
                                  format: uuid
                                  example: "123e4567-e89b-12d3-a456-************"
                                firstName:
                                  type: string
                                  example: "John"
                                lastName:
                                  type: string
                                  example: "Doe"
                                email:
                                  type: string
                                  format: email
                                  example: "<EMAIL>"
                                phone:
                                  type: string
                                  example: "+**********"
                                pets:
                                  type: array
                                  items:
                                    type: object
                                    properties:
                                      id:
                                        type: string
                                        format: uuid
                                        example: "123e4567-e89b-12d3-a456-************"
                                      name:
                                        type: string
                                        example: "Buddy"
                                      type:
                                        type: string
                                        example: "Dog"
                                      breed:
                                        type: string
                                        example: "Golden Retriever"
                                      weight:
                                        type: string
                                        example: "65 lbs"
                                      age:
                                        type: string
                                        example: "3 years"
                          currentDate:
                            type: string
                            format: date
                            example: "2023-06-09"
                      meta:
                        type: object
                        properties:
                          totalTenants:
                            type: integer
                            example: 2
                          totalPets:
                            type: integer
                            example: 3
                          generationDate:
                            type: string
                            format: date-time
                            example: "2023-06-09T16:02:30.000Z"
        401:
          description: Unauthorized - Either a valid token or authentication is required
        403:
          description: Forbidden - User doesn't have permission to access this lease
        404:
          description: Lease not found or no active template available
        500:
          $ref: '#/components/responses/ServerError'

  /api/tenants/leases/{leaseId}/sign:
    post:
      tags: [Tenant Leases]
      summary: Sign a lease document
      description: Sign a lease document as a tenant
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: leaseId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease to sign
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [signature]
              properties:
                signature:
                  type: object
                  required: [data, x, y]
                  properties:
                    data:
                      type: string
                      description: Base64 encoded signature image
                      example: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
                    x:
                      type: number
                      format: float
                      description: X coordinate for signature placement
                      example: 100.5
                    y:
                      type: number
                      format: float
                      description: Y coordinate for signature placement
                      example: 200.5
      responses:
        200:
          description: Lease signed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      documentUrl:
                        type: string
                        format: uri
                        description: Temporary URL to access the signed document
                        example: "https://example.com/signed-lease.pdf?token=abc123"
                      documentId:
                        type: string
                        format: uuid
                        description: ID of the signed document
        400:
          description: Invalid input data or signature
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          description: User does not have permission to sign this lease
        404:
          description: Lease not found or no active lease document available
        500:
          $ref: '#/components/responses/ServerError'

  /api/public/leases/{id}/document:
    get:
      tags: [Tenant Leases]
      summary: Get lease document content
      description: Generate and retrieve the lease document content for a specific lease
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease
      responses:
        200:
          description: Lease document content generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Lease document content generated successfully"
                  data:
                    type: object
                    properties:
                      documentContent:
                        type: object
                        description: The processed lease document content with variables replaced
                      documentName:
                        type: string
                        example: "Lease Agreement - 123 Main St"
                      templateId:
                        type: string
                        format: uuid
                        example: "123e4567-e89b-12d3-a456-************"
                      leaseId:
                        type: string
                        format: uuid
                        example: "123e4567-e89b-12d3-a456-************"
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    TenantInvitation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        status:
          type: string
          enum: [pending, completed, cancelled, expired]
          example: "pending"
        expiresAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    InvitationVerification:
      type: object
      properties:
        invitation:
          type: object
          properties:
            id:
              type: string
              format: uuid
            email:
              type: string
              format: email
            firstName:
              type: string
            lastName:
              type: string
            expiresAt:
              type: string
              format: date-time
        property:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string
            addressLine1:
              type: string
            addressLine2:
              type: string
            city:
              type: string
            state:
              type: string
            postalCode:
              type: string

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        emailVerified:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Child:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "Emma"
        lastName:
          type: string
          example: "Smith"
        dateOfBirth:
          type: string
          format: date
          example: "2020-01-15"
        gender:
          type: string
          enum: [male, female, other, prefer_not_to_say]
          example: "female"
        isActive:
          type: boolean
          example: true
        comments:
          type: string
          example: "Allergies to peanuts"
        parentId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message describing what went wrong"
        error:
          type: string
          example: "Detailed error information"

  responses:
    UnauthorizedError:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

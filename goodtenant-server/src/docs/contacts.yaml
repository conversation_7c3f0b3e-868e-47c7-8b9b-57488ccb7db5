openapi: 3.0.0
info:
  title: Contacts API
  description: API for managing property contacts
  version: 1.0.0
servers:
  - url: /api
    description: API server
tags:
  - name: Contacts
    description: Property contact management

paths:
  /contacts:
    get:
      tags: [Contacts]
      summary: Get all contacts
      description: Retrieve a list of contacts with optional filtering and pagination
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: search
          schema:
            type: string
          description: Search term (searches first name, last name, company, email, phone)
        - in: query
          name: accountId
          schema:
            type: string
            format: uuid
          description: Filter by account ID
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
        - in: query
          name: type
          schema:
            type: string
            enum: [contractor, vendor, service_provider, tenant, owner, manager, other]
          description: Filter by contact type
        - in: query
          name: isActive
          schema:
            type: boolean
          description: Filter by active status
      responses:
        '200':
          description: Successfully retrieved contacts
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Contact'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Contacts]
      summary: Create a new contact
      description: Create a new contact record
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactInput'
      responses:
        '201':
          description: Contact created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Contact'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

  /contacts/{id}:
    get:
      tags: [Contacts]
      summary: Get contact by ID
      description: Retrieve a single contact by its ID
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/idParam'
      responses:
        '200':
          description: Successfully retrieved contact
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Contact'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Contacts]
      summary: Update a contact
      description: Update an existing contact
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/idParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactInput'
      responses:
        '200':
          description: Contact updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Contact'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Contacts]
      summary: Delete a contact
      description: Delete a contact (Admin only)
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/idParam'
      responses:
        '200':
          description: Contact deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Contact deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'

  /properties/{propertyId}/contacts:
    get:
      tags: [Contacts]
      summary: Get contacts by property
      description: Retrieve all contacts associated with a specific property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the property
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
      responses:
        '200':
          description: Successfully retrieved property contacts
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Contact'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        company:
          type: string
          example: "ACME Inc."
        jobTitle:
          type: string
          example: "Property Manager"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+**********"
        mobile:
          type: string
          example: "+**********"
        website:
          type: string
          example: "https://example.com"
        addressLine1:
          type: string
          example: "123 Main St"
        addressLine2:
          type: string
          example: "Apt 4B"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"
        postalCode:
          type: string
          example: "10001"
        country:
          type: string
          example: "USA"
        type:
          type: string
          enum: [contractor, vendor, service_provider, tenant, owner, manager, other]
          example: "property_manager"
        isPrimary:
          type: boolean
          example: true
        isActive:
          type: boolean
          example: true
        notes:
          type: string
          example: "Preferred contact method is email"
        accountId:
          type: string
          format: uuid
        propertyId:
          type: string
          format: uuid
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        property:
          $ref: '#/components/schemas/PropertyReference'
        account:
          $ref: '#/components/schemas/AccountReference'

    ContactInput:
      type: object
      required:
        - firstName
        - lastName
        - email
        - phone
        - accountId
        - type
      properties:
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        company:
          type: string
          example: "ACME Inc."
        jobTitle:
          type: string
          example: "Property Manager"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+**********"
        mobile:
          type: string
          example: "+**********"
        website:
          type: string
          example: "https://example.com"
        addressLine1:
          type: string
          example: "123 Main St"
        addressLine2:
          type: string
          example: "Apt 4B"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"
        postalCode:
          type: string
          example: "10001"
        country:
          type: string
          example: "USA"
        type:
          type: string
          enum: [contractor, vendor, service_provider, tenant, owner, manager, other]
          example: "property_manager"
        isPrimary:
          type: boolean
          example: true
        isActive:
          type: boolean
          example: true
        notes:
          type: string
          example: "Preferred contact method is email"
        accountId:
          type: string
          format: uuid
        propertyId:
          type: string
          format: uuid
          nullable: true

    PropertyReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        addressLine1:
          type: string
        city:
          type: string
        state:
          type: string

    AccountReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    Pagination:
      type: object
      properties:
        total:
          type: integer
          example: 100
        page:
          type: integer
          example: 1
        totalPages:
          type: integer
          example: 10

  parameters:
    idParam:
      in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
      description: Contact ID
    page:
      in: query
      name: page
      schema:
        type: integer
        default: 1
      description: Page number for pagination
    limit:
      in: query
      name: limit
      schema:
        type: integer
        default: 10
      description: Number of items per page

  responses:
    BadRequest:
      description: Bad request - invalid input
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFound:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        error:
          type: string
          example: "Detailed error message"
paths:
  /api/notifications:
    get:
      tags: [Notifications]
      summary: Get all notifications
      description: Retrieve a paginated list of notifications with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: isRead
          schema:
            type: boolean
          description: Filter by read status
        - in: query
          name: type
          schema:
            type: string
            enum: [task_assigned, task_updated, task_due_soon, task_overdue, task_completed, document_expiring, document_expired, payment_received, payment_due, payment_overdue, system_announcement, maintenance_request, maintenance_update, lease_renewal]
          description: Filter by notification type
        - in: query
          name: entityType
          schema:
            type: string
            enum: [Task, User, Property, Lease, Document, Payment, Maintenance]
          description: Filter by related entity type
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: Filter by related entity ID
        - in: query
          name: startDate
          schema:
            type: string
            format: date-time
          description: Filter by start date (created after)
        - in: query
          name: endDate
          schema:
            type: string
            format: date-time
          description: Filter by end date (created before)
        - in: query
          name: sortBy
          schema:
            type: string
            default: createdAt
          description: Field to sort by
        - in: query
          name: sortOrder
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          description: Sort order (ASC or DESC)
      responses:
        200:
          description: List of notifications retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Notification'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Notifications]
      summary: Create a new notification
      description: Create a new notification (admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationInput'
      responses:
        201:
          description: Notification created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Notification'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/notifications/{id}:
    get:
      tags: [Notifications]
      summary: Get notification by ID
      description: Retrieve a specific notification by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Notification ID
      responses:
        200:
          description: Notification retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Notification'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Notifications]
      summary: Delete a notification
      description: Delete a specific notification (soft delete)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Notification ID
      responses:
        200:
          description: Notification deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Notification deleted successfully
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/notifications/{id}/read:
    patch:
      tags: [Notifications]
      summary: Mark notification as read
      description: Mark a specific notification as read
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Notification ID
      responses:
        200:
          description: Notification marked as read
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Notification'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/notifications/read-all:
    patch:
      tags: [Notifications]
      summary: Mark all notifications as read
      description: Mark all unread notifications for the current user as read
      security:
        - bearerAuth: []
      responses:
        200:
          description: All notifications marked as read
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Marked 5 notifications as read"
                  data:
                    type: object
                    properties:
                      updatedCount:
                        type: integer
                        example: 5
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Notification:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        title:
          type: string
          example: "New task assigned"
        message:
          type: string
          example: "You have been assigned a new task: Pay rent"
        type:
          type: string
          enum: [task_assigned, task_updated, task_due_soon, task_overdue, task_completed, document_expiring, document_expired, payment_received, payment_due, payment_overdue, system_announcement, maintenance_request, maintenance_update, lease_renewal]
          example: "task_assigned"
        isRead:
          type: boolean
          example: false
        entityType:
          type: string
          enum: [Task, User, Property, Lease, Document, Payment, Maintenance]
          example: "Task"
        entityId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        metadata:
          type: object
          additionalProperties: true
          example: {"priority": "high", "dueDate": "2023-12-31T23:59:59.000Z"}
        userId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        createdAt:
          type: string
          format: date-time
          example: '2023-06-15T10:00:00.000Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-06-15T10:00:00.000Z'
        user:
          $ref: '#/components/schemas/UserBasic'
        creator:
          $ref: '#/components/schemas/UserBasic'

    NotificationInput:
      type: object
      required:
        - userId
        - title
        - message
        - type
      properties:
        userId:
          type: string
          format: uuid
          description: ID of the user who will receive the notification
          example: 123e4567-e89b-12d3-a456-************
        title:
          type: string
          minLength: 2
          maxLength: 100
          example: "New task assigned"
        message:
          type: string
          minLength: 2
          example: "You have been assigned a new task: Pay rent"
        type:
          type: string
          enum: [task_assigned, task_updated, task_due_soon, task_overdue, task_completed, document_expiring, document_expired, payment_received, payment_due, payment_overdue, system_announcement, maintenance_request, maintenance_update, lease_renewal]
          example: "task_assigned"
        entityType:
          type: string
          enum: [Task, User, Property, Lease, Document, Payment, Maintenance]
          required: false
          example: "Task"
        entityId:
          type: string
          format: uuid
          required: false
          example: 123e4567-e89b-12d3-a456-************
        metadata:
          type: object
          additionalProperties: true
          required: false
          example: {"priority": "high", "dueDate": "2023-12-31T23:59:59.000Z"}

  responses:
    UnauthorizedError:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            message: Please authenticate

    ForbiddenError:
      description: Forbidden - Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            message: Insufficient permissions to perform this action

    ValidationError:
      description: Bad Request - Invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            message: "Validation error"
            errors:
              - "Title is required"
              - "Message must be at least 2 characters"

    NotFoundError:
      description: Not Found - Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            message: Notification not found

    ServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            message: An unexpected error occurred
            error: "Error message details"

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

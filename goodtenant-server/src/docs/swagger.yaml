openapi: 3.0.0
info:
  title: GoodTenant API
  description: API documentation for GoodTenant application
  version: 1.0.0
  contact:
    name: GoodTenant Support
    email: <EMAIL>
servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://api.goodtenant.com/v1
    description: Production server
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: Error message describing the issue
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: email
              message:
                type: string
                example: Please provide a valid email
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Doe
        email:
          type: string
          format: email
          example: <EMAIL>
        role:
          type: string
          enum: [tenant, landlord, admin]
          example: tenant
        isEmailVerified:
          type: boolean
          example: false
        lastLogin:
          type: string
          format: date-time
          example: 2023-05-27T12:00:00Z
        createdAt:
          type: string
          format: date-time
          example: 2023-05-20T10:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2023-05-20T10:00:00Z
    AuthResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
              example: 123e4567-e89b-12d3-a456-************
            firstName:
              type: string
              example: John
            lastName:
              type: string
              example: Doe
            email:
              type: string
              format: email
              example: <EMAIL>
            role:
              type: string
              example: tenant
            token:
              type: string
              example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
security:
  - bearerAuth: []
tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management
  - name: Properties
    description: Property management
  - name: Leases
    description: Lease management
  - name: Maintenance
    description: Maintenance request management
  - name: Payments
    description: Payment processing and history

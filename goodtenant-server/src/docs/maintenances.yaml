# src/docs/maintenance.yaml
paths:
  /api/maintenance:
    get:
      tags: [Maintenance]
      summary: Get all maintenance tickets
      description: Retrieve a paginated list of maintenance tickets with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [open, in_progress, on_hold, completed, cancelled]
          description: Filter by ticket status
        - in: query
          name: priority
          schema:
            type: string
            enum: [low, medium, high, critical]
          description: Filter by ticket priority
        - in: query
          name: assignedTo
          schema:
            type: string
            format: uuid
          description: Filter by assigned user ID
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
      responses:
        200:
          description: List of maintenance tickets retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MaintenanceTicket'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/maintenance/property/{propertyId}:
    post:
      tags: [Maintenance]
      summary: Create a new maintenance ticket for a property
      description: Create a new maintenance ticket for the specified property
      operationId: createPropertyMaintenanceTicket
      security:
        - bearerAuth: []
      parameters:
        - name: propertyId
          in: path
          required: true
          description: ID of the property to create the maintenance ticket for
          schema:
            type: string
            format: uuid
            example: 123e4567-e89b-12d3-a456-************
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceTicketInput'
      responses:
        201:
          description: Maintenance ticket created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/MaintenanceTicket'
        400:
          description: Invalid input or property not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Property not found or you do not have permission to create a ticket for this property"
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/maintenance/{id}:
    get:
      tags: [Maintenance]
      summary: Get maintenance ticket by ID
      description: Retrieve a specific maintenance ticket by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Maintenance ticket ID
      responses:
        200:
          description: Maintenance ticket retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/MaintenanceTicket'
        401:
          description: Unauthorized
        403:
          description: Forbidden (no access to this ticket)
        404:
          description: Ticket not found
        500:
          description: Server error

    put:
      tags: [Maintenance]
      summary: Update a maintenance ticket
      description: Update an existing maintenance ticket
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Maintenance ticket ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceTicketUpdate'
      responses:
        200:
          description: Maintenance ticket updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/MaintenanceTicket'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (no permission to update this ticket)
        404:
          description: Ticket not found
        500:
          description: Server error

  /api/maintenance/{id}/assign:
    patch:
      tags: [Maintenance]
      summary: Assign a maintenance ticket
      description: Assign a maintenance ticket to a user
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Maintenance ticket ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - assigneeId
              properties:
                assigneeId:
                  type: string
                  format: uuid
                  description: ID of the user to assign the ticket to
      responses:
        200:
          description: Ticket assigned successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Ticket assigned to John Doe"
                  data:
                    $ref: '#/components/schemas/MaintenanceTicket'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (no permission to assign this ticket)
        404:
          description: Ticket or assignee not found
        500:
          description: Server error

  /api/maintenance/{id}/status:
    patch:
      tags: [Maintenance]
      summary: Update ticket status
      description: Update the status of a maintenance ticket
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Maintenance ticket ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [open, in_progress, on_hold, completed, cancelled]
                  description: New status for the ticket
      responses:
        200:
          description: Ticket status updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Ticket status updated to in progress"
                  data:
                    $ref: '#/components/schemas/MaintenanceTicket'
        400:
          description: Invalid status value
        401:
          description: Unauthorized
        403:
          description: Forbidden (no permission to update this ticket)
        404:
          description: Ticket not found
        500:
          description: Server error

components:
  schemas:
    MaintenanceTicket:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the maintenance ticket
        title:
          type: string
          description: Title of the maintenance ticket
        description:
          type: string
          description: Detailed description of the issue
        status:
          type: string
          enum: [open, in_progress, on_hold, completed, cancelled]
          description: Current status of the ticket
        priority:
          type: string
          enum: [low, medium, high, critical]
          description: Priority level of the ticket
        propertyId:
          type: string
          format: uuid
          description: ID of the property this ticket is associated with
        reportedBy:
          type: string
          format: uuid
          description: ID of the user who created the ticket
        assignedTo:
          type: string
          format: uuid
          nullable: true
          description: ID of the user assigned to the ticket
        completedAt:
          type: string
          format: date-time
          nullable: true
          description: When the ticket was marked as completed
        createdAt:
          type: string
          format: date-time
          description: When the ticket was created
        updatedAt:
          type: string
          format: date-time
          description: When the ticket was last updated
        reporter:
          $ref: '#/components/schemas/User'
        assignee:
          $ref: '#/components/schemas/User'
        property:
          $ref: '#/components/schemas/Property'
        files:
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceFile'

    MaintenanceTicketInput:
      type: object
      required:
        - title
        - description
      properties:
        title:
          type: string
          minLength: 5
          maxLength: 255
          description: Title of the maintenance ticket
          example: "Leaking faucet in master bathroom"
        description:
          type: string
          minLength: 10
          description: Detailed description of the issue
          example: "The faucet in the master bathroom sink is leaking continuously. The issue started yesterday and is getting worse."
        priority:
          type: string
          enum: [low, medium, high, critical]
          default: medium
          description: Priority level of the ticket
          example: "medium"
      example:
        title: "Leaking faucet in master bathroom"
        description: "The faucet in the master bathroom sink is leaking continuously. The issue started yesterday and is getting worse."
        priority: "medium"

    MaintenanceTicketUpdate:
      type: object
      properties:
        title:
          type: string
          minLength: 5
          maxLength: 255
          description: Title of the maintenance ticket
        description:
          type: string
          minLength: 10
          description: Detailed description of the issue
        priority:
          type: string
          enum: [low, medium, high, critical]
          description: Priority level of the ticket

    MaintenanceFile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the file
        fileName:
          type: string
          description: Original name of the file
        fileKey:
          type: string
          description: Key used to store the file in the storage service
        fileType:
          type: string
          description: MIME type of the file
        fileSize:
          type: integer
          description: Size of the file in bytes
        description:
          type: string
          description: Optional description of the file
        uploadedBy:
          type: string
          format: uuid
          description: ID of the user who uploaded the file
        uploadedAt:
          type: string
          format: date-time
          description: When the file was uploaded
        url:
          type: string
          format: uri
          description: Temporary URL to access the file

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email

    Property:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        address:
          type: string
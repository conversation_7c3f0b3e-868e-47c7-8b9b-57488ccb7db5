paths:
  /api/leases/{leaseId}/occupants:
    post:
      tags: [Occupants]
      summary: Create a new occupant for a lease
      description: Create a new occupant associated with a specific lease
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: leaseId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease to add the occupant to
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OccupantInput'
      responses:
        201:
          description: Occupant created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Occupant'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (user doesn't have access to this lease)
        404:
          description: Lease not found
        500:
          description: Server error

    get:
      tags: [Occupants]
      summary: Get all occupants for a lease
      description: Retrieve a paginated list of occupants for a specific lease
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: leaseId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the lease to get occupants for
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for occupant names or notes
        - in: query
          name: isActive
          schema:
            type: boolean
          description: Filter by active status
      responses:
        200:
          description: List of occupants retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Occupant'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        403:
          description: Forbidden (user doesn't have access to this lease)
        404:
          description: Lease not found
        500:
          description: Server error

  /api/occupants/{id}:
    get:
      tags: [Occupants]
      summary: Get occupant by ID
      description: Retrieve a specific occupant by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Occupant ID
      responses:
        200:
          description: Occupant details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Occupant'
        401:
          description: Unauthorized
        403:
          description: Forbidden (user doesn't have access to this occupant)
        404:
          description: Occupant not found
        500:
          description: Server error

    put:
      tags: [Occupants]
      summary: Update an occupant
      description: Update an existing occupant's information
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Occupant ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OccupantInput'
      responses:
        200:
          description: Occupant updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Occupant'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (user doesn't have access to this occupant)
        404:
          description: Occupant not found
        500:
          description: Server error

    delete:
      tags: [Occupants]
      summary: Delete an occupant
      description: Soft delete an occupant (sets isActive to false)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Occupant ID
      responses:
        200:
          description: Occupant deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Occupant deleted successfully"
        401:
          description: Unauthorized
        403:
          description: Forbidden (user doesn't have access to this occupant)
        404:
          description: Occupant not found
        500:
          description: Server error

components:
  schemas:
    Occupant:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        age:
          type: integer
          example: 30
          nullable: true
        relationship:
          type: string
          enum: [spouse, child, parent, sibling, other_relative, roommate, other]
          example: "spouse"
        isActive:
          type: boolean
          example: true
        notes:
          type: string
          example: "Additional notes about the occupant"
          nullable: true
        leaseId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00.000Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00.000Z"
        deletedAt:
          type: string
          format: date-time
          nullable: true
          example: null

    OccupantInput:
      type: object
      required:
        - firstName
        - lastName
        - relationship
      properties:
        firstName:
          type: string
          example: "John"
          minLength: 1
          maxLength: 50
        lastName:
          type: string
          example: "Doe"
          minLength: 1
          maxLength: 50
        age:
          type: integer
          example: 30
          minimum: 0
          maximum: 120
          nullable: true
        relationship:
          type: string
          enum: [spouse, child, parent, sibling, other_relative, roommate, other]
          example: "spouse"
        notes:
          type: string
          example: "Additional notes about the occupant"
          nullable: true

paths:
  /api/references:
    get:
      tags: [References]
      summary: Get all references
      description: Retrieve a paginated list of references with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for name, email, or property address
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, contacted, verified, rejected]
          description: Filter by reference status
        - in: query
          name: isVerified
          schema:
            type: boolean
          description: Filter by verification status
        - in: query
          name: userId
          schema:
            type: string
            format: uuid
          description: Filter by user ID (admin only)
      responses:
        200:
          description: List of references retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Reference'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Server error

    post:
      tags: [References]
      summary: Create a new reference
      description: Create a new rental reference
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReferenceInput'
      responses:
        201:
          description: Reference created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Reference'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/references/my-references:
    get:
      tags: [References]
      summary: Get current user's references
      description: Retrieve references for the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, contacted, verified, rejected]
          description: Filter by reference status
        - in: query
          name: isVerified
          schema:
            type: boolean
          description: Filter by verification status
      responses:
        200:
          description: List of user's references retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Reference'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/references/{id}:
    get:
      tags: [References]
      summary: Get a reference by ID
      description: Retrieve a specific reference (users can only access their own references)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Reference ID
      responses:
        200:
          description: Reference details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Reference'
        401:
          description: Unauthorized
        403:
          description: Forbidden (trying to access another user's reference)
        404:
          description: Reference not found
        500:
          description: Server error

    put:
      tags: [References]
      summary: Update a reference
      description: Update an existing reference (users can only update their own references)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Reference ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReferenceInput'
      responses:
        200:
          description: Reference updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Reference'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (trying to update another user's reference)
        404:
          description: Reference not found
        500:
          description: Server error

    delete:
      tags: [References]
      summary: Delete a reference
      description: Delete a reference (users can only delete their own references)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Reference ID
      responses:
        200:
          description: Reference deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Reference deleted successfully"
        401:
          description: Unauthorized
        403:
          description: Forbidden (trying to delete another user's reference)
        404:
          description: Reference not found
        500:
          description: Server error

components:
  schemas:
    Reference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        userId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "John Smith"
        position:
          type: string
          example: "Previous Landlord"
        company:
          type: string
          example: "ABC Properties"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+****************"
        propertyAddress:
          type: string
          example: "123 Main St"
        propertyCity:
          type: string
          example: "Anytown"
        propertyState:
          type: string
          example: "CA"
        propertyZip:
          type: string
          example: "12345"
        rentalStartDate:
          type: string
          format: date
          example: "2020-01-01"
        rentalEndDate:
          type: string
          format: date
          nullable: true
          example: "2022-12-31"
        referenceText:
          type: string
          nullable: true
          example: "Excellent tenant, always paid on time"
        isVerified:
          type: boolean
          example: false
        status:
          type: string
          enum: [pending, contacted, verified, rejected]
          example: "pending"
        verificationDate:
          type: string
          format: date-time
          nullable: true
        verifiedById:
          type: string
          format: uuid
          nullable: true
        notes:
          type: string
          nullable: true
        isActive:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        deletedAt:
          type: string
          format: date-time
          nullable: true
        tenant:
          $ref: '#/components/schemas/UserReference'
        verifiedBy:
          $ref: '#/components/schemas/UserReference'

    ReferenceInput:
      type: object
      required:
        - name
        - email
        - phone
        - propertyAddress
        - propertyCity
        - propertyState
        - propertyZip
        - rentalStartDate
      properties:
        name:
          type: string
          example: "John Smith"
        position:
          type: string
          example: "Previous Landlord"
        company:
          type: string
          example: "ABC Properties"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+****************"
        propertyAddress:
          type: string
          example: "123 Main St"
        propertyCity:
          type: string
          example: "Anytown"
        propertyState:
          type: string
          example: "CA"
        propertyZip:
          type: string
          example: "12345"
        rentalStartDate:
          type: string
          format: date
          example: "2020-01-01"
        rentalEndDate:
          type: string
          format: date
          nullable: true
          example: "2022-12-31"
        referenceText:
          type: string
          nullable: true
          example: "Excellent tenant, always paid on time"
        status:
          type: string
          enum: [pending, contacted, verified, rejected]
          example: "pending"
        isVerified:
          type: boolean
          example: false
        notes:
          type: string
          nullable: true

    UserReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
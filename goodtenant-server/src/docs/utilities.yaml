paths:
  /api/utilities:
    get:
      tags: [Utilities]
      summary: Get all utilities
      description: Retrieve a paginated list of utilities with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for provider name, account number, or notes
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
        - in: query
          name: utilityType
          schema:
            type: string
            enum: [electricity, water, gas, internet, trash, sewer, other]
          description: Filter by utility type
        - in: query
          name: isActive
          schema:
            type: boolean
          description: Filter by active status
      responses:
        200:
          description: List of utilities retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Utility'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Utilities]
      summary: Create a new utility
      description: Create a new utility record
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilityInput'
      responses:
        201:
          description: Utility created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Utility'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/ServerError'

  /api/utilities/{id}:
    get:
      tags: [Utilities]
      summary: Get utility by ID
      description: Retrieve a specific utility by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Utility ID
      responses:
        200:
          description: Utility retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Utility'
        401:
          $ref: '#/components/responses/Unauthorized'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Utilities]
      summary: Update a utility
      description: Update an existing utility
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Utility ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilityInput'
      responses:
        200:
          description: Utility updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Utility'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Utilities]
      summary: Delete a utility
      description: Delete a utility (admin only)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Utility ID
      responses:
        200:
          description: Utility deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Utility deleted successfully
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

  /api/utilities/properties/{propertyId}:
    get:
      tags: [Utilities]
      summary: Get utilities by property ID
      description: Retrieve all utilities associated with a property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: Property ID
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        200:
          description: List of property utilities retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Utility'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        401:
          $ref: '#/components/responses/Unauthorized'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Utility:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        propertyId:
          type: string
          format: uuid
          nullable: true
          example: "123e4567-e89b-12d3-a456-************"
        utilityType:
          type: string
          enum: [electricity, water, gas, internet, trash, sewer, other]
          example: "electricity"
        providerName:
          type: string
          example: "ACME Utilities"
        accountNumber:
          type: string
          example: "UTL123456"
        billingCycle:
          type: string
          enum: [monthly, bi-monthly, quarterly, annually]
          example: "monthly"
        dueDate:
          type: integer
          minimum: 1
          maximum: 31
          example: 15
        isIncludedInRent:
          type: boolean
          example: false
        isActive:
          type: boolean
          example: true
        notes:
          type: string
          example: "Meter #12345"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        deletedAt:
          type: string
          format: date-time
          nullable: true
        property:
          $ref: '#/components/schemas/Property'
        account:
          $ref: '#/components/schemas/Account'
        creator:
          $ref: '#/components/schemas/User'
        updater:
          $ref: '#/components/schemas/User'

    UtilityInput:
      type: object
      required:
        - propertyId
        - utilityType
        - providerName
      properties:
        propertyId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        utilityType:
          type: string
          enum: [electricity, water, gas, internet, trash, sewer, other]
          example: "electricity"
        providerName:
          type: string
          example: "ACME Utilities"
        accountNumber:
          type: string
          example: "UTL123456"
        billingCycle:
          type: string
          enum: [monthly, bi-monthly, quarterly, annually]
          default: "monthly"
          example: "monthly"
        dueDate:
          type: integer
          minimum: 1
          maximum: 31
          example: 15
        isIncludedInRent:
          type: boolean
          default: false
          example: false
        isActive:
          type: boolean
          default: true
          example: true
        notes:
          type: string
          example: "Meter #12345"
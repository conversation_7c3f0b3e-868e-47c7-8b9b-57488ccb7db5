paths:
  /api/signatures:
    post:
      tags: [Signatures]
      summary: Upload a user signature
      description: Upload a signature file for the authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                signature:
                  type: string
                  format: binary
                  description: The signature file to upload (image/png, image/jpeg, image/svg+xml)
      responses:
        201:
          description: Signature uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      fileKey:
                        type: string
                        description: The unique key of the uploaded file
                      signedUrl:
                        type: string
                        description: Pre-signed URL to access the uploaded signature
        400:
          description: Bad request (e.g., no file uploaded, invalid file type)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized (missing or invalid token)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    get:
      tags: [Signatures]
      summary: Get user signature
      description: Retrieve the signed URL for the authenticated user's signature
      security:
        - bearerAuth: []
      responses:
        200:
          description: Signature URL retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      signedUrl:
                        type: string
                        description: Pre-signed URL to access the user's signature
                      expiresAt:
                        type: string
                        format: date-time
                        description: When the signed URL will expire
        401:
          description: Unauthorized (missing or invalid token)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: No signature found for user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags: [Signatures]
      summary: Delete user signature
      description: Delete the signature for the authenticated user
      security:
        - bearerAuth: []
      responses:
        200:
          description: Signature deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Signature deleted successfully"
        401:
          description: Unauthorized (missing or invalid token)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: No signature found for user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          description: Error message
        error:
          type: string
          description: Detailed error message (in development/staging)
      required:
        - success
        - message

paths:
  /api/taxes:
    get:
      tags: [Taxes]
      summary: Get all taxes
      description: Retrieve a paginated list of taxes with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term (searches in description, account number, tax ID)
        - in: query
          name: status
          schema:
            type: string
            enum: [unpaid, partially_paid, paid, overdue, cancelled]
          description: Filter by payment status
        - in: query
          name: taxType
          schema:
            type: string
            enum: [Property, Income, Sales, Business, Other]
          description: Filter by tax type
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
      responses:
        200:
          description: List of taxes retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Tax'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        401:
          description: Unauthorized
        500:
          description: Server error

    post:
      tags: [Taxes]
      summary: Create a new tax record
      description: Create a new tax record
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaxInput'
      responses:
        201:
          description: Tax record created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Tax'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/taxes/{id}:
    get:
      tags: [Taxes]
      summary: Get tax by ID
      description: Retrieve a specific tax record by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Tax record ID
      responses:
        200:
          description: Tax record retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Tax'
        401:
          description: Unauthorized
        403:
          description: Forbidden - not your tax record
        404:
          description: Tax record not found
        500:
          description: Server error

    put:
      tags: [Taxes]
      summary: Update a tax record
      description: Update an existing tax record
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Tax record ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaxInput'
      responses:
        200:
          description: Tax record updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Tax'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden - not your tax record
        404:
          description: Tax record not found
        500:
          description: Server error

    delete:
      tags: [Taxes]
      summary: Delete a tax record
      description: Soft delete a tax record
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Tax record ID
      responses:
        200:
          description: Tax record deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Tax record deleted successfully"
        401:
          description: Unauthorized
        403:
          description: Forbidden - not your tax record
        404:
          description: Tax record not found
        500:
          description: Server error

  /api/taxes/properties/{propertyId}:
    get:
      tags: [Taxes]
      summary: Get taxes for a property
      description: Retrieve taxes associated with a specific property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: Property ID
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [unpaid, partially_paid, paid, overdue, cancelled]
          description: Filter by payment status
      responses:
        200:
          description: List of property taxes retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Tax'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        401:
          description: Unauthorized
        500:
          description: Server error

components:
  schemas:
    Tax:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        taxType:
          type: string
          enum: [Property, Income, Sales, Business, Other]
          example: "Property"
        description:
          type: string
          example: "Annual property tax for 2023"
        website:
          type: string
          format: uri
          example: "https://tax.payment.portal"
        accountNumber:
          type: string
          example: "TAX-12345"
        taxId:
          type: string
          example: "12-3456789"
        amount:
          type: number
          format: float
          example: 2500.00
        dueDate:
          type: string
          format: date
          example: "2023-12-31"
        paymentFrequency:
          type: string
          enum: [monthly, quarterly, semi-annually, annually, one-time]
          example: "annually"
        status:
          type: string
          enum: [unpaid, partially_paid, paid, overdue, cancelled]
          example: "unpaid"
        paymentMethod:
          type: string
          enum: [credit_card, bank_transfer, check, cash, other]
          nullable: true
          example: "bank_transfer"
        paymentDate:
          type: string
          format: date-time
          nullable: true
          example: "2023-12-15T14:30:00Z"
        notes:
          type: string
          nullable: true
          example: "Paid via online banking"
        isActive:
          type: boolean
          example: true
        propertyId:
          type: string
          format: uuid
          nullable: true
          example: "123e4567-e89b-12d3-a456-************"
        accountId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdBy:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        updatedBy:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        deletedAt:
          type: string
          format: date-time
          nullable: true
        property:
          $ref: '#/components/schemas/PropertyReference'
        account:
          $ref: '#/components/schemas/AccountReference'
        creator:
          $ref: '#/components/schemas/UserReference'
        updater:
          $ref: '#/components/schemas/UserReference'

    TaxInput:
      type: object
      required:
        - taxType
        - amount
        - dueDate
        - paymentFrequency
      properties:
        taxType:
          type: string
          enum: [Property, Income, Sales, Business, Other]
          example: "Property"
        description:
          type: string
          example: "Annual property tax for 2023"
        website:
          type: string
          format: uri
          example: "https://tax.payment.portal"
        accountNumber:
          type: string
          example: "TAX-12345"
        taxId:
          type: string
          example: "12-3456789"
        amount:
          type: number
          format: float
          example: 2500.00
        dueDate:
          type: string
          format: date
          example: "2023-12-31"
        paymentFrequency:
          type: string
          enum: [monthly, quarterly, semi-annually, annually, one-time]
          example: "annually"
        status:
          type: string
          enum: [unpaid, partially_paid, paid, overdue, cancelled]
          default: "unpaid"
          example: "unpaid"
        paymentMethod:
          type: string
          enum: [credit_card, bank_transfer, check, cash, other]
          nullable: true
          example: "bank_transfer"
        paymentDate:
          type: string
          format: date-time
          nullable: true
          example: "2023-12-15T14:30:00Z"
        notes:
          type: string
          nullable: true
          example: "Paid via online banking"
        propertyId:
          type: string
          format: uuid
          nullable: true
          example: "123e4567-e89b-12d3-a456-************"

    PropertyReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "123 Main St"
        addressLine1:
          type: string
          example: "123 Main St"
        city:
          type: string
          example: "Anytown"
        state:
          type: string
          example: "CA"

    AccountReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "John Doe"

    UserReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"

    Pagination:
      type: object
      properties:
        total:
          type: integer
          example: 1
        page:
          type: integer
          example: 1
        totalPages:
          type: integer
          example: 1
paths:
  /api/leases:
    get:
      tags: [Leases]
      summary: Get all leases
      description: Retrieve a paginated list of leases with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for lease details (searches property name, address, landlord name, or tenant name)
        - in: query
          name: status
          schema:
            type: string
            enum: [draft, active, expired, terminated]
          description: Filter by lease status
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
        - in: query
          name: landlordId
          schema:
            type: string
            format: uuid
          description: Filter by landlord ID (admin only)
        - in: query
          name: tenantId
          schema:
            type: string
            format: uuid
          description: Filter by tenant ID
      responses:
        200:
          description: List of leases retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Lease'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Leases]
      summary: Create a new lease
      description: Create a new lease agreement
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LeaseInput'
      responses:
        201:
          description: Lease created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Lease'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/leases/{id}:
    get:
      tags: [Leases]
      summary: Get lease by ID
      description: Retrieve a specific lease by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the lease to retrieve
      responses:
        200:
          description: Lease retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Lease'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Leases]
      summary: Update a lease
      description: Update an existing lease
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the lease to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LeaseInput'
      responses:
        200:
          description: Lease updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Lease'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Leases]
      summary: Delete a lease
      description: Soft delete an existing lease (sets isActive to false)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the lease to delete
      responses:
        200:
          description: Lease deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 'Lease deleted successfully'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/leases/{id}/tenants:
    get:
      tags: [Leases]
      summary: Get tenants for a lease
      description: Retrieve all tenants associated with a specific lease
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the lease
      responses:
        200:
          description: Tenants retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/UserReference'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Leases]
      summary: Add tenants to a lease
      description: Add one or more tenants to an existing lease
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the lease
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - tenantIds
              properties:
                tenantIds:
                  type: array
                  minItems: 1
                  items:
                    type: string
                    format: uuid
                  example: ['123e4567-e89b-12d3-a456-************']
      responses:
        200:
          description: Tenants added to lease successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Lease'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/leases/{leaseId}/tenants/{tenantId}:
    delete:
      tags: [Leases]
      summary: Remove a tenant from a lease
      description: Remove a specific tenant from a lease
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: leaseId
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the lease
        - in: path
          name: tenantId
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the tenant to remove
      responses:
        200:
          description: Tenant removed from lease successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Lease'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Lease:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        startDate:
          type: string
          format: date
          example: '2023-01-01'
        endDate:
          type: string
          format: date
          example: '2024-12-31'
        monthlyRent:
          type: number
          format: decimal
          example: 1500.00
        securityDeposit:
          type: number
          format: decimal
          example: 1500.00
        status:
          type: string
          enum: [draft, active, expired, terminated]
          example: active
        leaseType:
          type: string
          enum: [fixed, month-to-month, yearly]
          example: fixed
        paymentDueDate:
          type: integer
          minimum: 1
          maximum: 31
          example: 1
        lateFee:
          type: number
          format: decimal
          example: 50.00
        nsfFee:
          type: number
          format: decimal
          example: 35.00
          description: 'Fee charged for returned payments due to non-sufficient funds'
        earlyTerminationFee:
          type: number
          format: decimal
          nullable: true
          example: 500.00
          description: 'Fixed fee for early lease termination'
        numberOfPeople:
          type: integer
          minimum: 1
          nullable: true
          example: 2
          description: 'Number of people living in the property'
        notes:
          type: string
          nullable: true
          example: 'Tenant prefers communication via email'
        isActive:
          type: boolean
          example: true
        propertyId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        landlordId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        property:
          $ref: '#/components/schemas/PropertyReference'
        landlord:
          $ref: '#/components/schemas/UserReference'
        tenants:
          type: array
          items:
            $ref: '#/components/schemas/UserReference'
        createdAt:
          type: string
          format: date-time
          example: '2023-01-01T00:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-01-01T00:00:00Z'

    LeaseInput:
      type: object
      required:
        - startDate
        - endDate
        - monthlyRent
        - securityDeposit
        - leaseType
        - propertyId
        - tenantIds
      properties:
        startDate:
          type: string
          format: date
          example: '2023-01-01'
        endDate:
          type: string
          format: date
          example: '2024-12-31'
        monthlyRent:
          type: number
          format: decimal
          minimum: 0
          example: 1500.00
        securityDeposit:
          type: number
          format: decimal
          minimum: 0
          example: 1500.00
        status:
          type: string
          enum: [draft, active, expired, terminated]
          default: draft
          example: draft
        leaseType:
          type: string
          enum: [fixed, month-to-month, yearly]
          example: fixed
        paymentDueDate:
          type: integer
          minimum: 1
          maximum: 31
          default: 1
          example: 1
        lateFee:
          type: number
          format: decimal
          minimum: 0
          default: 0
          example: 50.00
        nsfFee:
          type: number
          format: decimal
          minimum: 0
          default: 0
          example: 35.00
          description: 'Fee charged for returned payments due to non-sufficient funds'
        earlyTerminationFee:
          type: number
          format: decimal
          minimum: 0
          nullable: true
          example: 500.00
          description: 'Fixed fee for early lease termination'
        numberOfPeople:
          type: integer
          minimum: 1
          nullable: true
          example: 2
          description: 'Number of people living in the property'
        notes:
          type: string
          nullable: true
          example: 'Tenant prefers communication via email'
        propertyId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        tenantIds:
          type: array
          minItems: 1
          items:
            type: string
            format: uuid
          example: ['123e4567-e89b-12d3-a456-************']

    PropertyReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        name:
          type: string
          example: 'Downtown Apartment'
        addressLine1:
          type: string
          example: '123 Main St'
        city:
          type: string
          example: 'New York'
        state:
          type: string
          example: 'NY'
        zipCode:
          type: string
          example: '10001'

    UserReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        firstName:
          type: string
          example: 'John'
        lastName:
          type: string
          example: 'Doe'
        email:
          type: string
          format: email
          example: '<EMAIL>'
        phone:
          type: string
          example: '+1234567890'

  responses:
    UnauthorizedError:
      description: Unauthorized - Authentication credentials were missing or incorrect
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    ForbiddenError:
      description: Forbidden - User doesn't have permission to access this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFoundError:
      description: The requested resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    ValidationError:
      description: The request was invalid or missing required fields
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    ServerError:
      description: An unexpected error occurred on the server
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: 'An error occurred'
        error:
          type: string
          example: 'Detailed error message'
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: 'email'
              message:
                type: string
                example: 'Invalid email format'

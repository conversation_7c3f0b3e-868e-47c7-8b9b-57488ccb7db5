paths:
  /api/templates/variables:
    get:
      tags: [Templates]
      summary: Get system variables
      description: Retrieve all system-defined variables that can be used in templates
      security:
        - bearerAuth: []
      responses:
        200:
          description: System variables retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemVariablesResponse'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/templates:
    get:
      tags: [Templates]
      summary: Get all templates
      description: Retrieve a paginated list of templates with optional search and filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for template name or description
        - in: query
          name: type
          schema:
            type: string
            enum: [LEASE, NOTICE, AGREEMENT, OTHER]
          description: Filter templates by type
        - in: query
          name: isActive
          schema:
            type: boolean
          description: Filter templates by active status
      responses:
        200:
          description: List of templates retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Templates]
      summary: Create a new template
      description: Create a new document template
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTemplateInput'
      responses:
        201:
          description: Template created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/templates/{id}:
    get:
      tags: [Templates]
      summary: Get template by ID
      description: Retrieve a specific template by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Template ID
      responses:
        200:
          description: Template retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Templates]
      summary: Update template
      description: Update an existing template
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Template ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTemplateInput'
      responses:
        200:
          description: Template updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Templates]
      summary: Delete template
      description: Delete a template by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Template ID
      responses:
        200:
          description: Template deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 'Template deleted successfully'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/templates/{id}/generate:
    post:
      tags: [Templates]
      summary: Generate document from template
      description: Generate a document by populating a template with provided data
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Template ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - data
              properties:
                data:
                  type: object
                  description: Key-value pairs of variables to populate the template
      responses:
        200:
          description: Document generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GeneratedDocumentResponse'
        400:
          description: Missing required variables or invalid data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Template:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        accountId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        name:
          type: string
          example: 'Standard Lease Agreement'
        description:
          type: string
          example: 'Standard lease agreement template for residential properties'
        type:
          type: string
          enum: [LEASE, NOTICE, AGREEMENT, OTHER]
          example: 'LEASE'
        content:
          $ref: '#/components/schemas/TemplateContent'
        usedVariables:
          type: array
          description: Array of variable keys used in this template (references system variables)
          items:
            type: string
          example: ['tenant_full_name', 'property_address', 'monthly_rent']
        isActive:
          type: boolean
          example: true
        isDefault:
          type: boolean
          example: false
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        createdBy:
          $ref: '#/components/schemas/UserReference'
        updatedBy:
          $ref: '#/components/schemas/UserReference'
        account:
          $ref: '#/components/schemas/AccountReference'

    CreateTemplateInput:
      type: object
      required:
        - name
        - type
        - content
      properties:
        name:
          type: string
          example: 'Standard Lease Agreement'
          minLength: 2
          maxLength: 100
        description:
          type: string
          example: 'Standard lease agreement template for residential properties'
        type:
          type: string
          enum: [LEASE, NOTICE, AGREEMENT, OTHER]
          example: 'LEASE'
        content:
          $ref: '#/components/schemas/TemplateContent'
        isActive:
          type: boolean
          default: true

    UpdateTemplateInput:
      type: object
      properties:
        name:
          type: string
          example: 'Updated Lease Agreement'
          minLength: 2
          maxLength: 100
        description:
          type: string
          example: 'Updated lease agreement template for residential properties'
        type:
          type: string
          enum: [LEASE, NOTICE, AGREEMENT, OTHER]
          example: 'LEASE'
        content:
          $ref: '#/components/schemas/TemplateContent'
        isActive:
          type: boolean

    SystemVariables:
      type: object
      description: |
        System-defined variables that can be used in templates.
        These variables are predefined and cannot be modified through the API.
      properties:
        tenant:
          type: array
          description: Tenant information variables
          items:
            $ref: '#/components/schemas/SystemVariable'
        property:
          type: array
          description: Property information variables
          items:
            $ref: '#/components/schemas/SystemVariable'
        financial:
          type: array
          description: Financial variables
          items:
            $ref: '#/components/schemas/SystemVariable'
        dates:
          type: array
          description: Date-related variables
          items:
            $ref: '#/components/schemas/SystemVariable'
        signatures:
          type: array
          description: Signature fields
          items:
            $ref: '#/components/schemas/SystemVariable'

    SystemVariable:
      type: object
      description: A system-defined variable that can be used in templates
      properties:
        key:
          type: string
          description: The variable key (used in templates with {{key}})
          example: tenant_full_name
        label:
          type: string
          description: Human-readable label for the variable
          example: Tenant Full Name
        type:
          type: string
          enum: [string, number, date, boolean, currency, text, signature]
          description: The data type of the variable
          example: string
        required:
          type: boolean
          description: Whether the variable is required
          default: false
        description:
          type: string
          description: Description of what the variable represents
          example: Full legal name of the tenant
        category:
          type: string
          description: Category for grouping related variables
          example: tenant

    TemplateContent:
      type: object
      description: Structure of the template content
      properties:
        version:
          type: string
          example: "1.0"
        content:
          type: array
          description: Array of content blocks
          items:
            type: object
            properties:
              type:
                type: string
                example: "paragraph"
              text:
                type: string
                description: Text content with optional variables in {{variable}} format
                example: "This agreement is between {{landlord_name}} and {{tenant_name}}."
              variables:
                type: array
                description: Variables used in this content block (automatically extracted)
                items:
                  type: string
                example: ["landlord_name", "tenant_name"]

    UserReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email

    AccountReference:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    TemplateVariables:
      type: object
      description: |
        Available template variables organized by categories.
        These variables can be used in any template by surrounding them with double curly braces, e.g., {{tenant_full_name}}.
      properties:
        tenant:
          type: array
          description: Tenant information variables
          items:
            $ref: '#/components/schemas/TemplateVariable'
          example:
            - key: tenant_full_name
              label: Tenant Full Name
              type: string
              required: true
              description: Full legal name of the tenant
            - key: tenant_email
              label: Tenant Email
              type: string
              description: Email address of the tenant

        property:
          type: array
          description: Property information variables
          items:
            $ref: '#/components/schemas/TemplateVariable'
          example:
            - key: property_address
              label: Property Address
              type: string
              required: true
              description: Full address of the rental property

        financial:
          type: array
          description: Financial variables
          items:
            $ref: '#/components/schemas/TemplateVariable'
          example:
            - key: monthly_rent
              label: Monthly Rent
              type: currency
              required: true
              description: Monthly rental amount

        dates:
          type: array
          description: Date-related variables
          items:
            $ref: '#/components/schemas/TemplateVariable'
          example:
            - key: start_date
              label: Start Date
              type: date
              description: Lease/agreement start date

    TemplateVariable:
      type: object
      description: A variable that can be used in templates
      properties:
        key:
          type: string
          description: The variable key (used in templates with {{key}})
          example: tenant_full_name
        label:
          type: string
          description: Human-readable label for the variable
          example: Tenant Full Name
        type:
          type: string
          enum: [string, number, date, boolean, currency, text, select, signature]
          description: The data type of the variable
          example: string
        required:
          type: boolean
          description: Whether the variable is required
          default: false
        description:
          type: string
          description: Description of what the variable represents
          example: Full legal name of the tenant
        category:
          type: string
          description: Category for grouping related variables
          example: tenant
        placeholder:
          type: string
          description: Example value or placeholder text
          example: John Doe
        options:
          type: array
          description: For 'select' type, the available options
          items:
            type: string
          example: ["Option 1", "Option 2"]

    GeneratedDocument:
      type: object
      properties:
        templateId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        data:
          type: object
          description: The data used to fill in the template
        generatedAt:
          type: string
          format: date-time
        status:
          type: string
          example: 'generated'
        url:
          type: string
          format: uri
          nullable: true
          description: URL to download the generated document

    TemplateListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/Template'
        pagination:
          type: object
          properties:
            total:
              type: integer
              example: 1
            page:
              type: integer
              example: 1
            totalPages:
              type: integer
              example: 1

    TemplateResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/Template'

    GeneratedDocumentResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/GeneratedDocument'

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: 'Invalid request'
        message:
          type: string
          example: 'Missing required variables'

    SystemVariablesResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/SystemVariables'

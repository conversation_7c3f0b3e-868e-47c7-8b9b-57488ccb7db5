openapi: 3.0.0
info:
  title: Payments API
  description: API for managing payments, including Stripe Checkout integration
  version: 2.0.0

tags:
  - name: Checkout
    description: Operations for Stripe Checkout
  - name: Payments
    description: Payment management operations
  - name: Webhooks
    description: Webhook endpoints for payment processing

paths:
  /api/payments/checkout:
    post:
      tags: [Checkout]
      summary: Create a new Stripe Checkout session
      description: Creates a new payment record and returns a Stripe Checkout session URL
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - items
                - amount
                - currency
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/PaymentItemInput'
                amount:
                  type: number
                  description: Total amount in the smallest currency unit (e.g., cents)
                currency:
                  type: string
                  default: usd
                  enum: [usd, eur, gbp, cad, aud]
                description:
                  type: string
                  description: Optional payment description
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        200:
          description: Checkout session created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        description: URL to redirect user to complete payment
                      sessionId:
                        type: string
                        description: Stripe Checkout session ID
        400:
          description: Invalid input or payment processing failed
        401:
          description: Unauthorized
        500:
          description: Server error

    get:
      tags: [Payments]
      summary: Get all payments
      description: Retrieve a paginated list of payments with optional filters
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, completed, failed, refunded, partially_refunded, cancelled]
          description: Filter by payment status
        - in: query
          name: paymentMethod
          schema:
            type: string
            enum: [stripe, cash, bank_transfer, check, other]
          description: Filter by payment method
        - in: query
          name: startDate
          schema:
            type: string
            format: date
          description: Filter by payment date (start)
        - in: query
          name: endDate
          schema:
            type: string
            format: date
          description: Filter by payment date (end)
      responses:
        200:
          description: List of payments retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Payment'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/payments/{id}:
    get:
      tags: [Payments]
      summary: Get payment by ID
      description: Retrieve a specific payment by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Payment ID
      responses:
        200:
          description: Payment retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Payment'
        404:
          description: Payment not found
        401:
          description: Unauthorized
        500:
          description: Server error

    put:
      tags: [Payments]
      summary: Update a payment
      description: Update an existing payment
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Payment ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentUpdateInput'
      responses:
        200:
          description: Payment updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Payment'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        404:
          description: Payment not found
        500:
          description: Server error

    delete:
      tags: [Payments]
      summary: Delete a payment
      description: Soft delete a payment
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Payment ID
      responses:
        200:
          description: Payment deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Payment deleted successfully
        401:
          description: Unauthorized
        404:
          description: Payment not found
        500:
          description: Server error

  /api/payments/create-payment-intent:
    post:
      tags: [Payment Intents]
      summary: Create a payment intent
      description: Create a Stripe payment intent for client-side payment processing
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - amount
              properties:
                amount:
                  type: number
                  format: float
                  minimum: 0.5
                  description: Amount in currency's smallest unit (e.g., cents for USD)
                currency:
                  type: string
                  default: usd
                  description: 3-letter ISO currency code
                metadata:
                  type: object
                  description: Additional metadata to attach to the payment
      responses:
        200:
          description: Payment intent created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  clientSecret:
                    type: string
                    description: Client secret for confirming the payment on the client side
                  paymentIntentId:
                    type: string
                    description: ID of the created payment intent
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/payments/success:
    get:
      tags: [Checkout]
      summary: Handle successful payment redirect
      description: Handles the redirect after a successful payment
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: session_id
          schema:
            type: string
          required: true
          description: Stripe Checkout session ID
      responses:
        302:
          description: Redirects to the success page
          headers:
            Location:
              schema:
                type: string
                  
  /api/payments/cancel:
    get:
      tags: [Checkout]
      summary: Handle cancelled payment
      description: Handles the redirect when a payment is cancelled
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: payment_id
          schema:
            type: string
          description: Optional payment ID to update status if needed
      responses:
        302:
          description: Redirects to the cancelled payment page
          headers:
            Location:
              schema:
                type: string

  /api/payments/session/{sessionId}:
    get:
      tags: [Checkout]
      summary: Get payment by session ID
      description: Retrieves payment details using the Stripe Checkout session ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: sessionId
          schema:
            type: string
          required: true
          description: Stripe Checkout session ID
      responses:
        200:
          description: Payment details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Payment'
        404:
          description: Payment not found

  /api/payments/webhook:
    post:
      tags: [Webhooks]
      summary: Handle Stripe webhook events
      description: Endpoint for Stripe to send payment events (e.g., checkout.session.completed)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: Type of Stripe event
                data:
                  type: object
                  description: Event data
      responses:
        200:
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Webhook processed successfully
        400:
          description: Invalid webhook signature or payload
        500:
          description: Server error

components:
  schemas:
    Payment:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        amount:
          type: number
          format: float
          example: 100.50
        currency:
          type: string
          example: usd
        status:
          type: string
          enum: [pending, completed, failed, refunded, partially_refunded, cancelled]
          example: completed
        paymentMethod:
          type: string
          enum: [stripe, cash, bank_transfer, check, other]
          example: stripe
        paymentDate:
          type: string
          format: date-time
          example: 2023-06-22T14:30:00Z
        referenceNumber:
          type: string
          example: PAY-12345
        notes:
          type: string
          example: Rent payment for June 2023
        stripePaymentIntentId:
          type: string
          example: pi_3NxX5q2eZvKYlo2C0H2T9X4S
        payerId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        receiverId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        accountId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        items:
          type: array
          items:
            $ref: '#/components/schemas/PaymentItem'

    PaymentInput:
      type: object
      required:
        - amount
        - paymentMethod
        - payerId
        - receiverId
        - accountId
      properties:
        amount:
          type: number
          format: float
          minimum: 0.01
          example: 100.50
        currency:
          type: string
          default: usd
          example: usd
        paymentMethod:
          type: string
          enum: [stripe, cash, bank_transfer, check, other]
          example: stripe
        paymentDate:
          type: string
          format: date-time
          example: 2023-06-22T14:30:00Z
        dueDate:
          type: string
          format: date
          example: 2023-06-30
        referenceNumber:
          type: string
          example: PAY-12345
        notes:
          type: string
          example: Rent payment for June 2023
        stripePaymentMethodId:
          type: string
          description: Required if paymentMethod is 'stripe'
          example: pm_1NxX5q2eZvKYlo2C0H2T9X4S
        payerId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        receiverId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        accountId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        items:
          type: array
          items:
            $ref: '#/components/schemas/PaymentItemInput'

    PaymentUpdateInput:
      type: object
      properties:
        status:
          type: string
          enum: [pending, completed, failed, refunded, partially_refunded, cancelled]
          example: completed
        notes:
          type: string
          example: Updated payment notes
        referenceNumber:
          type: string
          example: PAY-12345-UPDATED

    PaymentItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 223e4567-e89b-12d3-a456-************
        description:
          type: string
          example: Rent for June 2023
        amount:
          type: number
          format: float
          example: 90.00
        type:
          type: string
          enum: [rent, fee, tax, deposit, other]
          example: rent
        periodStart:
          type: string
          format: date
          example: 2023-06-01
        periodEnd:
          type: string
          format: date
          example: 2023-06-30
        quantity:
          type: number
          format: float
          default: 1
          example: 1
        unitPrice:
          type: number
          format: float
          example: 90.00
        taxRate:
          type: number
          format: float
          example: 0.1
        taxAmount:
          type: number
          format: float
          example: 9.00
        metadata:
          type: object
          additionalProperties: true

    PaymentItemInput:
      type: object
      required:
        - description
        - amount
      properties:
        description:
          type: string
          example: Rent for June 2023
        amount:
          type: number
          format: float
          minimum: 0.01
          example: 90.00
        type:
          type: string
          enum: [rent, fee, tax, deposit, other]
          default: other
          example: rent
        periodStart:
          type: string
          format: date
          example: 2023-06-01
        periodEnd:
          type: string
          format: date
          example: 2023-06-30
        quantity:
          type: number
          format: float
          minimum: 0.01
          default: 1
          example: 1
        unitPrice:
          type: number
          format: float
          minimum: 0
          example: 90.00
        taxRate:
          type: number
          format: float
          minimum: 0
          maximum: 1
          example: 0.1
        metadata:
          type: object
          additionalProperties: true

    Pagination:
      type: object
      properties:
        total:
          type: integer
          example: 100
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        totalPages:
          type: integer
          example: 10

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

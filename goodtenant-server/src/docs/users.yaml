paths:
  /api/users:
    get:
      tags: [Users]
      summary: Get all users
      description: |
        Retrieve a paginated list of users with optional filtering (admin only).
        
        **Query Parameters:**
        - `page`: Page number (default: 1)
        - `limit`: Number of items per page (default: 10)
        - `role`: Filter by user role (searches both primary and additional roles)
        - `search`: Search term to filter users by name or email
        
        **Available Roles:**
        - `account_owner`: Full account access and administration
        - `property_manager`: Can manage properties and tenants
        - `leasing_agent`: Can manage leases and show properties
        - `maintenance_staff`: Can manage maintenance requests
        - `tenant`: Basic tenant access
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          description: Number of items per page
        - in: query
          name: role
          schema:
            type: string
          description: Filter by user role
        - in: query
          name: search
          schema:
            type: string
          description: Search term to filter users by name or email
      responses:
        200:
          description: List of users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedResponse'
                items:
                  $ref: '#/components/schemas/User'
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)

    post:
      tags: [Users]
      summary: Create a new user
      description: |
        Create a new user (admin only).
        A temporary password will be automatically generated and sent to the user's email address.
        The user will be required to change their password on first login.
        
        **Roles System:**
        - Each user must have one primary role
        - Can have multiple additional roles
        - Valid roles: `account_owner`, `property_manager`, `leasing_agent`, `maintenance_staff`, `tenant`
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - firstName
                - lastName
                - email
              properties:
                firstName:
                  type: string
                  example: "Jane"
                lastName:
                  type: string
                  example: "Smith"
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                primaryRole:
                  type: string
                  enum: [account_owner, property_manager, leasing_agent, maintenance_staff, tenant]
                  default: tenant
                  example: "property_manager"
                  description: "Primary role for the user"
                additionalRoles:
                  type: array
                  items:
                    type: string
                    enum: [property_manager, leasing_agent, maintenance_staff, tenant]
                  example: ["leasing_agent", "maintenance_staff"]
                  description: "Additional roles for the user (cannot include account_owner)"
                addressLine1:
                  type: string
                  example: "123 Main St"
                  description: "Street address"
                addressLine2:
                  type: string
                  example: "Apt 4B"
                  description: "Apartment, suite, etc. (optional)"
                city:
                  type: string
                  example: "New York"
                  description: "City name"
                state:
                  type: string
                  example: "NY"
                  description: "State or province"
                postalCode:
                  type: string
                  example: "10001"
                  description: "ZIP or postal code"
                country:
                  type: string
                  example: "United States"
                  description: "Country name (defaults to 'United States' if not provided)"
      responses:
        201:
          description: |
            User created successfully.
            A welcome email with temporary login credentials has been sent to the user's email address.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
                data:
                  $ref: '#/components/schemas/User'
        400:
          description: Invalid input or email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)

  /api/users/{id}:
    get:
      tags: [Users]
      summary: Get user by ID
      description: Retrieve a specific user by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: User ID (UUID)
      responses:
        200:
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
                data:
                  $ref: '#/components/schemas/User'
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin or self access required)
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags: [Users]
      summary: Update user
      description: |
        Update an existing user's information.
        
        **Role Updates:**
        - To update roles, include `primaryRole` and/or `additionalRoles` in the request body
        - Role updates are scoped to the authenticated user's account
        - Only account owners can update roles for users in their account
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: User ID (UUID)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  example: "Jane"
                lastName:
                  type: string
                  example: "Doe"
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                phone:
                  type: string
                  example: "+****************"
                primaryRole:
                  type: string
                  enum: [account_owner, property_manager, leasing_agent, maintenance_staff, tenant]
                  example: "property_manager"
                  description: |
                    Primary role for the user in the current account.
                    Only account owners can modify roles.
                additionalRoles:
                  type: array
                  items:
                    type: string
                    enum: [property_manager, leasing_agent, maintenance_staff, tenant]
                  example: ["leasing_agent", "maintenance_staff"]
                  description: |
                    Additional roles for the user in the current account.
                    Cannot include 'account_owner'.
                addressLine1:
                  type: string
                  example: "123 Main St"
                  description: "Street address"
                addressLine2:
                  type: string
                  example: "Apt 4B"
                  description: "Apartment, suite, etc. (optional)"
                city:
                  type: string
                  example: "New York"
                  description: "City name"
                state:
                  type: string
                  example: "NY"
                  description: "State or province"
                postalCode:
                  type: string
                  example: "10001"
                  description: "ZIP or postal code"
                country:
                  type: string
                  example: "United States"
                  description: "Country name (defaults to 'United States' if not provided)"
      responses:
        200:
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
                data:
                  $ref: '#/components/schemas/User'
        400:
          description: |
            Bad Request. Possible reasons:
            - Invalid input data
            - Invalid role assignment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
        403:
          description: |
            Forbidden. Possible reasons:
            - Not authorized to update this user
            - Not an account owner trying to update roles
            - User doesn't belong to the current account
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags: [Users]
      summary: Delete user
      description: Delete a user (admin only)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: User ID (UUID)
      responses:
        204:
          description: User deleted successfully
        400:
          description: Cannot delete self or last admin
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/users/me/change-password:
    post:
      tags: [Users]
      summary: Change user password
      description: |
        Change the authenticated user's password.
        Requires the current password for verification.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - currentPassword
                - newPassword
              properties:
                currentPassword:
                  type: string
                  format: password
                  example: "currentSecurePassword123!"
                  description: Current password for verification
                newPassword:
                  type: string
                  format: password
                  minLength: 8
                  example: "newSecurePassword456!"
                  description: New password (minimum 8 characters)
      responses:
        200:
          description: Password changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        400:
          description: Invalid input or current password is incorrect
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized (invalid or missing token)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/users/me:
    get:
      tags: [Users]
      summary: Get current user profile
      description: Get the currently authenticated user's profile
      security:
        - bearerAuth: []
      responses:
        200:
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
                data:
                  $ref: '#/components/schemas/User'
        401:
          description: Unauthorized

    put:
      tags: [Users]
      summary: Update current user profile
      description: |
        Update the currently authenticated user's profile.
        
        **Note:** Only the following fields can be updated through this endpoint:
        - firstName
        - lastName
        - phone
        - addressLine1
        - addressLine2 (optional)
        - city
        - state
        - postalCode
        - country (defaults to 'United States' if not provided)
        
        To change your password, use the dedicated change password endpoint.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  example: "John"
                  description: User's first name
                lastName:
                  type: string
                  example: "Doe"
                  description: User's last name
                phone:
                  type: string
                  example: "+****************"
                  description: User's phone number (at least 10 digits)
                addressLine1:
                  type: string
                  example: "123 Main St"
                  description: First line of the user's address
                addressLine2:
                  type: string
                  example: "Apt 4B"
                  description: Second line of the user's address (optional)
                city:
                  type: string
                  example: "New York"
                  description: City of the user's address
                state:
                  type: string
                  example: "NY"
                  description: State/Province of the user's address
                postalCode:
                  type: string
                  example: "10001"
                  description: Postal/ZIP code of the user's address
                country:
                  type: string
                  example: "United States"
                  description: Country of the user's address (defaults to 'United States')
      responses:
        200:
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
                data:
                  $ref: '#/components/schemas/User'
        400:
          description: |
            Bad Request. Possible reasons:
            - No valid fields provided for update
            - Invalid field values
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "active"
        lastLogin:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        accounts:
          type: array
          items:
            $ref: '#/components/schemas/UserAccount'
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00Z"

    UserAccount:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "Acme Corp"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "active"
        plan:
          type: string
          example: "premium"
        accountUser:
          $ref: '#/components/schemas/AccountUser'

    AccountUser:
      type: object
      properties:
        primaryRole:
          type: string
          enum: [account_owner, property_manager, leasing_agent, maintenance_staff, tenant]
          example: "property_manager"
        additionalRoles:
          type: array
          items:
            type: string
            enum: [property_manager, leasing_agent, maintenance_staff, tenant]
          example: ["leasing_agent", "maintenance_staff"]
        isDefault:
          type: boolean
          example: true

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message describing what went wrong"
        error:
          type: string
          example: "Detailed error message for debugging"

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
        data:
          type: object
          description: "Response data"

    PaginatedResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items: {}
        pagination:
          type: object
          properties:
            total:
              type: integer
              example: 100
            page:
              type: integer
              example: 1
            totalPages:
              type: integer
              example: 10

    # Security scheme for Bearer token authentication
    securitySchemes:
      bearerAuth:
        type: http
        scheme: bearer
        bearerFormat: JWT

security:
  - bearerAuth: []

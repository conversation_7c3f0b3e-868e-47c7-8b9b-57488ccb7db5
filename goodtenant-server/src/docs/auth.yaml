paths:
  /api/auth/register:
    post:
      tags: [Authentication]
      summary: Register with email/password
      description: Creates a new user account with email and password.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  example: "<PERSON>"
                lastName:
                  type: string
                  example: "Doe"
                email:
                  type: string
                  example: "<EMAIL>"
                password:
                  type: string
                  example: "SecurePass123"
                phone:
                  type: string
                  example: "+****************"
                  description: "Optional phone number in any standard format"
                addressLine1:
                  type: string
                  example: "123 Main St"
                  description: "Street address (required)"
                addressLine2:
                  type: string
                  example: "Apt 4B"
                  description: "Apartment, suite, etc. (optional)"
                city:
                  type: string
                  example: "New York"
                  description: "City name (required)"
                state:
                  type: string
                  example: "NY"
                  description: "State or province (required)"
                postalCode:
                  type: string
                  example: "10001"
                  description: "ZIP or postal code (required)"
                country:
                  type: string
                  example: "United States"
                  description: "Country name (defaults to 'United States' if not provided)"
      responses:
        201:
          description: User registered successfully
        400:
          description: Em<PERSON> already exists or invalid input

  /api/auth/login:
    post:
      tags: [Authentication]
      summary: Login with email/password
      description: Authenticates user and returns access + refresh tokens
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: "<EMAIL>"
                password:
                  type: string
                  example: "SecurePass123"
      responses:
        200:
          description: Login successful, returns tokens and user data
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Login successful"
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      tokens:
                        type: object
                        properties:
                          accessToken:
                            type: string
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                          refreshToken:
                            type: string
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        '401':
          description: Invalid credentials

  /api/auth/refresh-token:
    post:
      tags: [Authentication]
      summary: Refresh access token
      description: Uses a refresh token to get a new access token
      security:
        - bearerAuth: []
      responses:
        200:
          description: New tokens generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      tokens:
                        type: object
                        properties:
                          accessToken:
                            type: string
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                          refreshToken:
                            type: string
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        401:
          description: |
            - No refresh token provided in Authorization header
            - Invalid or expired refresh token
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "No refresh token provided"

  /api/auth/logout:
    post:
      tags: [Authentication]
      summary: Logout user
      description: |
        Invalidates the current access token by adding it to the blacklist.
        The token will be rejected in subsequent requests.
      security:
        - bearerAuth: []
      responses:
        200:
          description: Successfully logged out
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Successfully logged out"
        401:
          description: |
            - Token has been revoked
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/request-password-reset:
    post:
      tags: [Authentication]
      summary: Request password reset
      description: Sends a password reset email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: "<EMAIL>"
      responses:
        200:
          description: Password reset email sent

  /api/auth/reset-password:
    post:
      tags: [Authentication]
      summary: Reset password
      description: Resets the user's password using a token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  example: "reset-token-123"
                newPassword:
                  type: string
                  example: "NewSecurePass123"
      responses:
        200:
          description: Password reset successful
        400:
          description: Invalid or expired token

  /api/auth/verify-email:
    get:
      tags: [Authentication]
      summary: Verify email
      description: Verifies a user's email address using a verification token
      parameters:
        - in: query
          name: token
          schema:
            type: string
          required: true
          description: Email verification token sent to the user's email
      responses:
        200:
          description: Email verification successful or already verified
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Email verified successfully. Welcome to our service!"
                  code:
                    type: string
                    example: "VERIFICATION_SUCCESS"
        400:
          description: Invalid, expired, or mismatched token
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Invalid verification token"
                  code:
                    type: string
                    example: "INVALID_TOKEN"
        404:
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "User not found"
                  code:
                    type: string
                    example: "USER_NOT_FOUND"
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        email:
          type: string
          example: "<EMAIL>"
        phone:
          type: string
          example: "+****************"
        addressLine1:
          type: string
          example: "123 Main St"
        addressLine2:
          type: string
          example: "Apt 4B"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"
        postalCode:
          type: string
          example: "10001"
        country:
          type: string
          example: "United States"
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Role:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Property Manager"
        description:
          type: string
          example: "Can manage properties and tenants"
        isSystemRole:
          type: boolean
          example: false
        permissions:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            properties: ["read", "update"]
            tenants: ["read", "create"]

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"

paths:
  /api/hoas:
    get:
      tags: [HOAs]
      summary: Get all HOAs
      description: Retrieve a paginated list of HOAs with optional search
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for HOA name, email, or manager name
      responses:
        200:
          description: List of HOAs retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/HOA'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

    post:
      tags: [HOAs]
      summary: Create a new HOA
      description: Create a new Homeowners Association (admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HOAInput'
      responses:
        201:
          description: HOA created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/HOA'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)
        500:
          description: Server error

  /api/hoas/{id}:
    get:
      tags: [HOAs]
      summary: Get HOA by ID
      description: Retrieve a specific HOA by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: HOA ID
      responses:
        200:
          description: HOA details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/HOA'
        401:
          description: Unauthorized
        404:
          description: HOA not found
        500:
          description: Server error

    put:
      tags: [HOAs]
      summary: Update an HOA
      description: Update an existing HOA (admin only)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: HOA ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HOAInput'
      responses:
        200:
          description: HOA updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/HOA'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)
        404:
          description: HOA not found
        500:
          description: Server error

    delete:
      tags: [HOAs]
      summary: Delete an HOA
      description: Delete an HOA (admin only, only if no properties are associated)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: HOA ID
      responses:
        200:
          description: HOA deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "HOA deleted successfully"
        400:
          description: Cannot delete HOA with associated properties
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)
        404:
          description: HOA not found
        500:
          description: Server error

  /api/hoas/{id}/properties:
    get:
      tags: [HOAs]
      summary: Get properties for an HOA
      description: Retrieve a paginated list of properties belonging to an HOA
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: HOA ID
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        200:
          description: List of properties retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Property'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        404:
          description: HOA not found
        500:
          description: Server error

components:
  schemas:
    HOA:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "Sunnyvale HOA"
        fee:
          type: number
          format: decimal
          example: 250.00
        website:
          type: string
          format: uri
          example: "https://sunnyvale-hoa.example.com"
        phone:
          type: string
          example: "+****************"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        managerName:
          type: string
          example: "John Doe"
        managerEmail:
          type: string
          format: email
          example: "<EMAIL>"
        managerPhone:
          type: string
          example: "+****************"
        gateCode:
          type: string
          example: "#1234"
        comments:
          type: string
          example: "Gate code changes monthly on the 1st"
        isActive:
          type: boolean
          example: true
        addressLine1:
          type: string
          example: "123 Main St"
        addressLine2:
          type: string
          nullable: true
          example: "Apt 4B"
        city:
          type: string
          example: "Sunnyvale"
        state:
          type: string
          example: "CA"
        zipCode:
          type: string
          example: "94085"
        country:
          type: string
          example: "USA"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        deletedAt:
          type: string
          format: date-time
          nullable: true

    HOAInput:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Sunnyvale HOA"
        fee:
          type: number
          format: decimal
          example: 250.00
        website:
          type: string
          format: uri
          example: "https://sunnyvale-hoa.example.com"
        phone:
          type: string
          example: "+****************"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        managerName:
          type: string
          example: "John Doe"
        managerEmail:
          type: string
          format: email
          example: "<EMAIL>"
        managerPhone:
          type: string
          example: "+****************"
        gateCode:
          type: string
          example: "#1234"
        comments:
          type: string
          example: "Gate code changes monthly on the 1st"
        isActive:
          type: boolean
          example: true
        addressLine1:
          type: string
          example: "123 Main St"
        addressLine2:
          type: string
          nullable: true
          example: "Apt 4B"
        city:
          type: string
          example: "Sunnyvale"
        state:
          type: string
          example: "CA"
        zipCode:
          type: string
          example: "94085"
        country:
          type: string
          example: "USA"

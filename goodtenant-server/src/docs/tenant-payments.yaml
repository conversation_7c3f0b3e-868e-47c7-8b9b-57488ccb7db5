paths:
  /api/tenant-payment/status:
    get:
      tags:
        - Tenant Payments
      summary: Get payment status for current user
      description: Returns payment status including amount due, due date, and payment history for the authenticated tenant
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: leaseId
          schema:
            type: string
            format: uuid
          description: Optional lease ID to filter by specific lease
        - in: query
          name: asOfDate
          schema:
            type: string
            format: date
          description: Date to calculate status as of (defaults to current date)
      responses:
        200:
          description: Payment status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/TenantPaymentStatus'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

  /api/tenant-payment/tenant/{tenantId}:
    get:
      tags:
        - Tenant Payments
      summary: Get payment status for a specific tenant (Admin)
      description: Retrieve payment status for a specific tenant (Admin only)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: tenantId
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the tenant
        - in: query
          name: leaseId
          schema:
            type: string
            format: uuid
          description: Optional lease ID to filter by specific lease
        - in: query
          name: asOfDate
          schema:
            type: string
            format: date
          description: Date to calculate status as of (defaults to current date)
      responses:
        200:
          description: Payment status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/TenantPaymentStatus'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        404:
          $ref: '#/components/responses/NotFound'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    TenantPaymentStatus:
      type: object
      properties:
        tenantId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        asOfDate:
          type: string
          format: date-time
          example: '2023-11-02T00:00:00.000Z'
        overallStatus:
          type: object
          properties:
            totalMonthlyRent:
              type: number
              format: float
              example: 2000.00
            totalPaid:
              type: number
              format: float
              example: 1500.00
            totalBalance:
              type: number
              format: float
              example: 500.00
            allPaid:
              type: boolean
              example: false
            anyLate:
              type: boolean
              example: true
        leases:
          type: array
          items:
            $ref: '#/components/schemas/LeasePaymentStatus'

    LeasePaymentStatus:
      type: object
      properties:
        leaseId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        property:
          $ref: '#/components/schemas/Property'
        monthlyRent:
          type: number
          format: float
          example: 1500.00
        paymentDueDate:
          type: integer
          example: 1
        currentPeriod:
          type: object
          properties:
            start:
              type: string
              format: date-time
              example: '2023-11-01T00:00:00.000Z'
            end:
              type: string
              format: date-time
              example: '2023-12-01T00:00:00.000Z'
        paymentStatus:
          type: object
          properties:
            isPaid:
              type: boolean
              example: false
            totalPaid:
              type: number
              format: float
              example: 1000.00
            balance:
              type: number
              format: float
              example: 500.00
            dueDate:
              type: string
              format: date
              example: '2023-11-01'
            isLate:
              type: boolean
              example: true
        paymentHistory:
          type: array
          items:
            $ref: '#/components/schemas/PaymentHistoryItem'

    Property:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: 'Sunny Apartments'
        addressLine1:
          type: string
          example: '123 Main St'
        addressLine2:
          type: string
          nullable: true
          example: 'Apt 4B'
        city:
          type: string
          example: 'Anytown'
        state:
          type: string
          example: 'CA'
        zipCode:
          type: string
          example: '12345'

    PaymentHistoryItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        amount:
          type: number
          format: float
          example: 500.00
        paymentDate:
          type: string
          format: date-time
          example: '2023-11-15T14:30:00.000Z'
        status:
          type: string
          enum: [pending, completed, failed, refunded, partially_refunded, cancelled]
          example: 'completed'
        paymentMethod:
          type: string
          example: 'credit_card'

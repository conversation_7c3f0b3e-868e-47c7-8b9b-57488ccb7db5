paths:
  /api/stripe/config:
    get:
      tags: [Stripe]
      summary: Get Stripe publishable key
      description: Retrieve the Stripe publishable key for an account
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: accountId
          schema:
            type: string
            format: uuid
          description: Account ID (optional - defaults to user's account)
      responses:
        200:
          description: Stripe publishable key retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  publishableKey:
                    type: string
                    example: "pk_test_51Nz12345678901234567890123456789012345678901234"
        400:
          description: Bad request (missing accountId)
        401:
          description: Unauthorized
        404:
          description: Account not found
        500:
          description: Server error

  /api/stripe/config/{accountId}:
    put:
      tags: [Stripe]
      summary: Update Stripe configuration
      description: Update Stripe API keys for an account (admin only)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: accountId
          required: true
          schema:
            type: string
            format: uuid
          description: Account ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StripeConfigInput'
      responses:
        200:
          description: Stripe configuration updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Stripe configuration updated successfully"
                  data:
                    type: object
                    properties:
                      accountId:
                        type: string
                        format: uuid
                        example: "123e4567-e89b-12d3-a456-************"
                      hasStripeSecretKey:
                        type: boolean
                        example: true
                      publishableKey:
                        type: string
                        example: "pk_test_51Nz12345678901234567890123456789012345678901234"
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (admin access required)
        404:
          description: Account not found
        500:
          description: Server error

components:
  schemas:
    StripeConfigInput:
      type: object
      required:
        - stripeSecretKey
        - stripePublishableKey
      properties:
        stripeSecretKey:
          type: string
          example: "sk_test_51Nz12345678901234567890123456789012345678901234"
          description: Stripe secret API key for this account
        stripePublishableKey:
          type: string
          example: "pk_test_51Nz12345678901234567890123456789012345678901234"
          description: Stripe publishable API key for this account

paths:
  /api/account/stats:
    get:
      tags: [Account]
      summary: Get account statistics
      description: Retrieve statistics for the authenticated user's account including total properties and active tenants
      security:
        - bearerAuth: []
      responses:
        200:
          description: Account statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalProperties:
                        type: integer
                        example: 5
                        description: Total number of properties in the account
                      totalTenants:
                        type: integer
                        example: 12
                        description: Total number of active tenants in the account
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

paths:
  /api/insurances:
    get:
      tags: [Insurances]
      summary: Get all insurances
      description: Retrieve a paginated list of insurances with optional search and filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for policy number, provider, or agent name
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
        - in: query
          name: isActive
          schema:
            type: boolean
          description: Filter by active status
      responses:
        200:
          description: List of insurances retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Insurance'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

    post:
      tags: [Insurances]
      summary: Create a new insurance
      description: Create a new insurance policy
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InsuranceInput'
      responses:
        201:
          description: Insurance created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Insurance'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/insurances/{id}:
    get:
      tags: [Insurances]
      summary: Get insurance by ID
      description: Retrieve a specific insurance by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Insurance ID
      responses:
        200:
          description: Insurance details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Insurance'
        401:
          description: Unauthorized
        403:
          description: Forbidden (not the owner or admin)
        404:
          description: Insurance not found
        500:
          description: Server error

    put:
      tags: [Insurances]
      summary: Update an insurance
      description: Update an existing insurance policy
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Insurance ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InsuranceInput'
      responses:
        200:
          description: Insurance updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Insurance'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (not the owner or admin)
        404:
          description: Insurance not found
        500:
          description: Server error

    delete:
      tags: [Insurances]
      summary: Delete an insurance
      description: Soft delete an insurance policy
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Insurance ID
      responses:
        200:
          description: Insurance deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Insurance deleted successfully"
        401:
          description: Unauthorized
        403:
          description: Forbidden (not the owner or admin)
        404:
          description: Insurance not found
        500:
          description: Server error

  /api/insurances/properties/{propertyId}:
    get:
      tags: [Properties, Insurances]
      summary: Get insurances for a property
      description: Retrieve all insurances for a specific property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: Property ID
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: isActive
          schema:
            type: boolean
          description: Filter by active status
      responses:
        200:
          description: List of property insurances retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Insurance'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        403:
          description: Forbidden (not the owner or admin)
        500:
          description: Server error

components:
  schemas:
    Insurance:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        policyNumber:
          type: string
          example: "POL-123456"
        provider:
          type: string
          example: "Acme Insurance"
        website:
          type: string
          format: uri
          example: "https://acme-insurance.com"
        insuranceType:
          type: string
          enum: [Homeowners, Rental, Liability, Flood, Earthquake, Umbrella, Other]
          example: "Homeowners"
        effectiveStartDate:
          type: string
          format: date
          example: "2025-01-01"
        effectiveEndDate:
          type: string
          format: date
          example: "2026-01-01"
        premiumAmount:
          type: number
          format: float
          example: 1200.00
        coverageAmount:
          type: number
          format: float
          example: 500000.00
        deductible:
          type: number
          format: float
          example: 1000.00
        paymentFrequency:
          type: string
          enum: [monthly, quarterly, semi-annually, annually]
          example: "annually"
        currency:
          type: string
          maxLength: 3
          example: "USD"
        agentName:
          type: string
          example: "John Smith"
        agentPhone:
          type: string
          example: "+****************"
        agentEmail:
          type: string
          format: email
          example: "<EMAIL>"
        notes:
          type: string
          example: "Additional notes about the insurance policy"
        isActive:
          type: boolean
          example: true
        propertyId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        accountId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        deletedAt:
          type: string
          format: date-time
          nullable: true

    InsuranceInput:
      type: object
      required:
        - policyNumber
        - provider
        - insuranceType
        - effectiveStartDate
        - effectiveEndDate
        - premiumAmount
        - coverageAmount
        - paymentFrequency
        - propertyId
      properties:
        policyNumber:
          type: string
          example: "POL-123456"
        provider:
          type: string
          example: "Acme Insurance"
        website:
          type: string
          format: uri
          example: "https://acme-insurance.com"
        insuranceType:
          type: string
          enum: [Homeowners, Rental, Liability, Flood, Earthquake, Umbrella, Other]
          example: "Homeowners"
        effectiveStartDate:
          type: string
          format: date
          example: "2025-01-01"
        effectiveEndDate:
          type: string
          format: date
          example: "2026-01-01"
        premiumAmount:
          type: number
          format: float
          example: 1200.00
        coverageAmount:
          type: number
          format: float
          example: 500000.00
        deductible:
          type: number
          format: float
          example: 1000.00
        paymentFrequency:
          type: string
          enum: [monthly, quarterly, semi-annually, annually]
          example: "annually"
        currency:
          type: string
          maxLength: 3
          default: "USD"
          example: "USD"
        agentName:
          type: string
          example: "John Smith"
        agentPhone:
          type: string
          example: "+****************"
        agentEmail:
          type: string
          format: email
          example: "<EMAIL>"
        notes:
          type: string
          example: "Additional notes about the insurance policy"
        isActive:
          type: boolean
          default: true
        propertyId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************

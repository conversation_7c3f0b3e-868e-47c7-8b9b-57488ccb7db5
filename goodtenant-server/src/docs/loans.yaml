paths:
  /api/loans:
    get:
      tags: [Loans]
      summary: Get all loans
      description: Retrieve a paginated list of loans with optional search and filtering
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for loan number, lender name, or phone
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter loans by property ID
      responses:
        200:
          description: List of loans retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Loan'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

    post:
      tags: [Loans]
      summary: Create a new loan
      description: Create a new loan record
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLoanInput'
      responses:
        201:
          description: <PERSON><PERSON> created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Loan'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/loans/{id}:
    get:
      tags: [Loans]
      summary: Get loan by ID
      description: Retrieve a specific loan by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Loan ID
      responses:
        200:
          description: Loan retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Loan'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    put:
      tags: [Loans]
      summary: Update a loan
      description: Update an existing loan
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Loan ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLoanInput'
      responses:
        200:
          description: Loan updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Loan'
        400:
          $ref: '#/components/responses/ValidationError'
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

    delete:
      tags: [Loans]
      summary: Delete a loan
      description: Delete a specific loan by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Loan ID
      responses:
        200:
          description: Loan deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Loan deleted successfully
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        404:
          $ref: '#/components/responses/NotFoundError'
        500:
          $ref: '#/components/responses/ServerError'

  /api/loans/properties/{propertyId}:
    get:
      tags: [Loans]
      summary: Get loans by property ID
      description: Retrieve all loans associated with a specific property
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: propertyId
          required: true
          schema:
            type: string
            format: uuid
          description: Property ID
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      responses:
        200:
          description: List of loans retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Loan'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          $ref: '#/components/responses/UnauthorizedError'
        403:
          $ref: '#/components/responses/ForbiddenError'
        500:
          $ref: '#/components/responses/ServerError'

components:
  schemas:
    Loan:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        accountId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        propertyId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        lenderName:
          type: string
          example: "ABC Mortgage"
        lenderWebsite:
          type: string
          format: uri
          example: "https://example.com"
        loanNumber:
          type: string
          example: "LOAN123456"
        lenderAddressLine1:
          type: string
          example: "123 Main St"
        lenderAddressLine2:
          type: string
          example: "Suite 100"
        lenderCity:
          type: string
          example: "New York"
        lenderState:
          type: string
          example: "NY"
        lenderPostalCode:
          type: string
          example: "10001"
        lenderCountry:
          type: string
          example: "USA"
        customerServicePhone:
          type: string
          example: "******-123-4567"
        principal:
          type: number
          format: decimal
          example: 250000.00
        interestRate:
          type: number
          format: decimal
          example: 3.750
        escrow:
          type: number
          format: decimal
          example: 500.00
        loanAmount:
          type: number
          format: decimal
          example: 250000.00
        comments:
          type: string
          example: "30-year fixed rate mortgage"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        deletedAt:
          type: string
          format: date-time
          nullable: true
        property:
          $ref: '#/components/schemas/PropertyRef'
        account:
          $ref: '#/components/schemas/AccountRef'

    CreateLoanInput:
      type: object
      required:
        - lenderName
        - loanNumber
        - principal
        - interestRate
        - loanAmount
      properties:
        propertyId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        lenderName:
          type: string
          example: "ABC Mortgage"
        lenderWebsite:
          type: string
          format: uri
          example: "https://example.com"
        loanNumber:
          type: string
          example: "LOAN123456"
        lenderAddressLine1:
          type: string
          example: "123 Main St"
        lenderAddressLine2:
          type: string
          example: "Suite 100"
        lenderCity:
          type: string
          example: "New York"
        lenderState:
          type: string
          example: "NY"
        lenderPostalCode:
          type: string
          example: "10001"
        lenderCountry:
          type: string
          example: "USA"
        customerServicePhone:
          type: string
          example: "******-123-4567"
        principal:
          type: number
          format: decimal
          example: 250000.00
        interestRate:
          type: number
          format: decimal
          example: 3.750
        escrow:
          type: number
          format: decimal
          example: 500.00
        loanAmount:
          type: number
          format: decimal
          example: 250000.00
        comments:
          type: string
          example: "30-year fixed rate mortgage"

    UpdateLoanInput:
      type: object
      properties:
        propertyId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        lenderName:
          type: string
          example: "ABC Mortgage"
        lenderWebsite:
          type: string
          format: uri
          example: "https://example.com"
        loanNumber:
          type: string
          example: "LOAN123456"
        lenderAddressLine1:
          type: string
          example: "123 Main St"
        lenderAddressLine2:
          type: string
          example: "Suite 100"
        lenderCity:
          type: string
          example: "New York"
        lenderState:
          type: string
          example: "NY"
        lenderPostalCode:
          type: string
          example: "10001"
        lenderCountry:
          type: string
          example: "USA"
        customerServicePhone:
          type: string
          example: "******-123-4567"
        principal:
          type: number
          format: decimal
          example: 250000.00
        interestRate:
          type: number
          format: decimal
          example: 3.750
        escrow:
          type: number
          format: decimal
          example: 500.00
        loanAmount:
          type: number
          format: decimal
          example: 250000.00
        comments:
          type: string
          example: "30-year fixed rate mortgage"

    PropertyRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "123 Main St"
        addressLine1:
          type: string
          example: "123 Main St"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"

    AccountRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
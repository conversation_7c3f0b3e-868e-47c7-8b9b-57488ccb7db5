paths:
  /api/tasks:
    get:
      tags: [Tasks]
      summary: Get all tasks
      description: Retrieve a paginated list of tasks with optional filters
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for task title or description
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, in_progress, completed, overdue, cancelled]
          description: Filter by task status
        - in: query
          name: priority
          schema:
            type: string
            enum: [low, medium, high, critical]
          description: Filter by task priority
        - in: query
          name: type
          schema:
            type: string
            enum: [manual, tax, utility, insurance, loan, maintenance, other]
          description: Filter by task type
        - in: query
          name: assignedTo
          schema:
            type: string
            format: uuid
          description: Filter by assignee user ID
        - in: query
          name: propertyId
          schema:
            type: string
            format: uuid
          description: Filter by property ID
        - in: query
          name: dueDateFrom
          schema:
            type: string
            format: date-time
          description: Filter by due date from (ISO 8601 format)
        - in: query
          name: dueDateTo
          schema:
            type: string
            format: date-time
          description: Filter by due date to (ISO 8601 format)
      responses:
        200:
          description: List of tasks retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Task'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

    post:
      tags: [Tasks]
      summary: Create a new task
      description: Create a new task
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskInput'
      responses:
        201:
          description: Task created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Task'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/tasks/me:
    get:
      tags: [Tasks]
      summary: Get tasks assigned to current user
      description: Retrieve tasks assigned to the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, in_progress, completed, overdue, cancelled]
          description: Filter by task status
        - in: query
          name: priority
          schema:
            type: string
            enum: [low, medium, high, critical]
          description: Filter by task priority
        - in: query
          name: type
          schema:
            type: string
            enum: [manual, tax, utility, insurance, loan, maintenance, other]
          description: Filter by task type
        - in: query
          name: dueDateFrom
          schema:
            type: string
            format: date-time
          description: Filter by due date from (ISO 8601 format)
        - in: query
          name: dueDateTo
          schema:
            type: string
            format: date-time
          description: Filter by due date to (ISO 8601 format)
      responses:
        200:
          description: List of user's tasks retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Task'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/tasks/{id}:
    get:
      tags: [Tasks]
      summary: Get task by ID
      description: Retrieve a specific task by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Task ID
      responses:
        200:
          description: Task details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Task'
        401:
          description: Unauthorized
        403:
          description: Forbidden (task doesn't belong to user's account)
        404:
          description: Task not found
        500:
          description: Server error

    put:
      tags: [Tasks]
      summary: Update a task
      description: Update an existing task
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Task ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskInput'
      responses:
        200:
          description: Task updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Task'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (task doesn't belong to user's account)
        404:
          description: Task not found
        500:
          description: Server error

    delete:
      tags: [Tasks]
      summary: Delete a task
      description: Delete an existing task (soft delete)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Task ID
      responses:
        200:
          description: Task deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Task deleted successfully
        401:
          description: Unauthorized
        403:
          description: Forbidden (task doesn't belong to user's account)
        404:
          description: Task not found
        500:
          description: Server error

components:
  schemas:
    Task:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        title:
          type: string
          example: 'Pay electricity bill'
        description:
          type: string
          example: 'Pay the monthly electricity bill for the property'
        dueDate:
          type: string
          format: date-time
          example: '2023-06-30T23:59:59.000Z'
        status:
          type: string
          enum: [pending, in_progress, completed, overdue, cancelled]
          example: 'pending'
        priority:
          type: string
          enum: [low, medium, high, critical]
          example: 'high'
        type:
          type: string
          enum: [manual, tax, utility, insurance, loan, maintenance, other]
          example: 'utility'
        isRecurring:
          type: boolean
          example: true
        recurrencePattern:
          type: string
          enum: [daily, weekly, monthly, yearly]
          example: 'monthly'
        nextRecurrenceDate:
          type: string
          format: date-time
          example: '2023-07-31T23:59:59.000Z'
        completedAt:
          type: string
          format: date-time
          example: '2023-06-25T14:30:00.000Z'
        createdAt:
          type: string
          format: date-time
          example: '2023-06-15T10:00:00.000Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-06-15T10:00:00.000Z'
        creator:
          $ref: '#/components/schemas/UserBasic'
        assignee:
          $ref: '#/components/schemas/UserBasic'
        property:
          $ref: '#/components/schemas/PropertyBasic'

    TaskInput:
      type: object
      required:
        - title
        - dueDate
      properties:
        title:
          type: string
          minLength: 2
          maxLength: 100
          example: 'Pay electricity bill'
        description:
          type: string
          example: 'Pay the monthly electricity bill for the property'
        dueDate:
          type: string
          format: date-time
          example: '2023-06-30T23:59:59.000Z'
        status:
          type: string
          enum: [pending, in_progress, completed, cancelled]
          example: 'pending'
        priority:
          type: string
          enum: [low, medium, high, critical]
          example: 'high'
        type:
          type: string
          enum: [lease, tax, utility, insurance, loan, maintenance, other]
          example: 'utility'
        assignedToId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        propertyId:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        isRecurring:
          type: boolean
          default: false
          example: true
        recurrencePattern:
          type: string
          enum: [daily, weekly, monthly, yearly]
          example: 'monthly'
        # These fields are system-managed and should not be set manually
        taskableId:
          readOnly: true
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
          description: 'Automatically set when task is created by the system for a specific entity'
        taskableType:
          readOnly: true
          type: string
          example: 'Utility'
          description: 'Automatically set to the entity type when task is created by the system'

    UserBasic:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        firstName:
          type: string
          example: 'John'
        lastName:
          type: string
          example: 'Doe'
        email:
          type: string
          example: '<EMAIL>'

    PropertyBasic:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: '123e4567-e89b-12d3-a456-************'
        name:
          type: string
          example: 'Main Street Apartment'
        addressLine1:
          type: string
          example: '123 Main St'
        city:
          type: string
          example: 'Anytown'
        state:
          type: string
          example: 'CA'

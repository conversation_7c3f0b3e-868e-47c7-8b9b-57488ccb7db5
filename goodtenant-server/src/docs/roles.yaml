paths:
  /api/roles:
    get:
      tags: [Roles]
      summary: Get all roles
      description: Retrieve all roles for the current account
      security:
        - bearerAuth: []
      responses:
        200:
          description: List of roles retrieved successfully
        401:
          description: Unauthorized

    post:
      tags: [Roles]
      summary: Create a new role
      description: Create a new role with the specified permissions
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: "Property Manager"
                  description: Name of the role
                description:
                  type: string
                  example: "Can manage properties and tenants"
                  description: Description of the role
                permissions:
                  type: object
                  example: {"properties": ["read", "update"], "tenants": ["read", "create"]}
                  description: Permission settings for the role
      responses:
        201:
          description: Role created successfully
        400:
          description: Invalid input or role name already exists
        401:
          description: Unauthorized

  /api/roles/{id}:
    get:
      tags: [Roles]
      summary: Get role by ID
      description: Retrieve a specific role by its ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: Role ID
      responses:
        200:
          description: Role retrieved successfully
        401:
          description: Unauthorized
        404:
          description: Role not found

    put:
      tags: [Roles]
      summary: Update a role
      description: Update an existing role's details and permissions
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: Role ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: "Updated Manager"
                description:
                  type: string
                  example: "Updated role description"
                permissions:
                  type: object
                  example: {"properties": ["*"], "tenants": ["read", "create"]}
                isActive:
                  type: boolean
                  example: true
      responses:
        200:
          description: Role updated successfully
        400:
          description: Invalid input
        401:
          description: Unauthorized
        403:
          description: Forbidden (trying to update system role)
        404:
          description: Role not found

    delete:
      tags: [Roles]
      summary: Delete a role
      description: Delete a role that is not assigned to any users
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: Role ID
      responses:
        204:
          description: Role deleted successfully
        400:
          description: Cannot delete role assigned to users
        401:
          description: Unauthorized
        403:
          description: Forbidden (trying to delete system role)
        404:
          description: Role not found

paths:
  /api/vehicles:
    get:
      tags: [Vehicles]
      summary: Get all vehicles for the authenticated user
      description: Retrieve a paginated list of vehicles for the authenticated user with optional search
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for vehicle make, model, or license plate
      responses:
        200:
          description: List of vehicles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Vehicle'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                        example: 1
                      page:
                        type: integer
                        example: 1
                      totalPages:
                        type: integer
                        example: 1
        401:
          description: Unauthorized
        500:
          description: Server error

    post:
      tags: [Vehicles]
      summary: Create a new vehicle
      description: Create a new vehicle for the authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VehicleInput'
      responses:
        201:
          description: Vehicle created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Vehicle'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        500:
          description: Server error

  /api/vehicles/{id}:
    get:
      tags: [Vehicles]
      summary: Get vehicle by ID
      description: Retrieve a specific vehicle by ID (must belong to the authenticated user)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Vehicle ID
      responses:
        200:
          description: Vehicle details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Vehicle'
        401:
          description: Unauthorized
        404:
          description: Vehicle not found or access denied
        500:
          description: Server error

    put:
      tags: [Vehicles]
      summary: Update a vehicle
      description: Update an existing vehicle (must belong to the authenticated user)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Vehicle ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VehicleInput'
      responses:
        200:
          description: Vehicle updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Vehicle'
        400:
          description: Invalid input
        401:
          description: Unauthorized
        404:
          description: Vehicle not found or access denied
        500:
          description: Server error

    delete:
      tags: [Vehicles]
      summary: Delete a vehicle
      description: Delete a vehicle (must belong to the authenticated user)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Vehicle ID
      responses:
        200:
          description: Vehicle deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Vehicle deleted successfully"
        401:
          description: Unauthorized
        404:
          description: Vehicle not found or access denied
        500:
          description: Server error


components:
  schemas:
    Vehicle:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        make:
          type: string
          example: "Toyota"
        model:
          type: string
          example: "Camry"
        year:
          type: integer
          example: 2020
        color:
          type: string
          example: "Blue"
        licensePlate:
          type: string
          example: "ABC123"
        notes:
          type: string
          example: "Company car"
        userId:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    VehicleInput:
      type: object
      required:
        - make
        - model
        - year
      properties:
        make:
          type: string
          example: "Toyota"
        model:
          type: string
          example: "Camry"
        year:
          type: integer
          example: 2020
        color:
          type: string
          example: "Blue"
        licensePlate:
          type: string
          example: "ABC123"
        notes:
          type: string
          example: "Company car"

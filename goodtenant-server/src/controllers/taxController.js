const catchAsync = require('../utils/catchAsync');
const TaxService = require('../services/taxService');

/**
 * @desc    Create a new tax record
 * @route   POST /api/taxes
 * @access  Private
 */
const createTax = catchAsync(async (req, res) => {
  const taxData = {
    ...req.body,
    createdBy: req.user.id,
    updatedBy: req.user.id,
    accountId: req.user.accountId
  };
  
  const { status, payload } = await TaxService.createTax(taxData);
  res.status(status).json(payload);
});

/**
 * @desc    Get all taxes with optional filtering and pagination
 * @route   GET /api/taxes
 * @access  Private
 */
const getTaxes = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    status,
    taxType,
    propertyId
  } = req.query;

  const { status: responseStatus, payload } = await TaxService.getAllTaxes({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    status,
    taxType,
    propertyId,
    accountId: req.user.accountId
  });
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get a single tax record by ID
 * @route   GET /api/taxes/:id
 * @access  Private
 */
const getTaxById = catchAsync(async (req, res) => {
  const { status, payload } = await TaxService.getTaxById(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing tax record
 * @route   PUT /api/taxes/:id
 * @access  Private
 */
const updateTax = catchAsync(async (req, res) => {
  const taxData = {
    ...req.body,
    updatedBy: req.user.id
  };

  const { status: responseStatus, payload } = await TaxService.updateTax(
    req.params.id, 
    taxData,
    req.user.id
  );
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Delete a tax record (soft delete)
 * @route   DELETE /api/taxes/:id
 * @access  Private
 */
const deleteTax = catchAsync(async (req, res) => {
  const { status, payload } = await TaxService.deleteTax(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get taxes for a specific property
 * @route   GET /api/properties/:propertyId/taxes
 * @access  Private
 */
const getPropertyTaxes = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  
  const { status: responseStatus, payload } = await TaxService.getPropertyTaxes(
    req.params.propertyId,
    { 
      page: parseInt(page), 
      limit: parseInt(limit),
      status
    }
  );
  
  res.status(responseStatus).json(payload);
});

module.exports = {
  createTax,
  getTaxes,
  getTaxById,
  updateTax,
  deleteTax,
  getPropertyTaxes
};
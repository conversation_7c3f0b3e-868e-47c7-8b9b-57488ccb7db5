// src/controllers/fileController.js
const catchAsync = require('../utils/catchAsync');
const fileService = require('../services/fileService');
const propertyService = require('../services/propertyService');
const logger = require('../utils/logger');
const db = require('../models');
const PropertyFile = db.PropertyFile;
const TenantInvitation = db.TenantInvitation;
const { Op } = require('sequelize');

const SUPPORTED_MODELS = ['Property', 'HOA', 'Insurance', 'Loan', 'Tax', 'Maintenance', 'Utility', 'Inventory', 'Lease'];

/**
 * @desc    Get all files for a model instance
 * @route   GET /api/files/{modelName}/{modelId}
 * @access  Private
 */
const getModelFiles = catchAsync(async (req, res) => {
  const { modelName, modelId } = req.params;

  if (!SUPPORTED_MODELS.includes(modelName)) {
    return res.status(400).json({ 
      success: false, 
      message: `Unsupported model type. Supported types: ${SUPPORTED_MODELS.join(', ')}` 
    });
  }

  // Check if user has access to the model instance
  if (modelName === 'Property') {
    const hasAccess = await propertyService.userHasAccess(req.user.id, modelId);
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to view these files'
      });
    }
  }
  // TODO: Add access checks for other model types

  const { status, payload } = await fileService.getModelFiles(modelName, modelId);
  
  if (status === 200) {
    return res.json({
      success: true,
      count: payload.length,
      data: payload
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Upload a file to a model instance
 * @route   POST /api/files/{modelName}/{modelId}/upload
 * @access  Private
 */
const uploadModelFile = catchAsync(async (req, res) => {
  const { modelName, modelId } = req.params;
  const { description = '' } = req.body;
  const file = req.file;

  if (!file) {
    return res.status(400).json({ 
      success: false, 
      message: 'No file uploaded' 
    });
  }

  if (!SUPPORTED_MODELS.includes(modelName)) {
    return res.status(400).json({ 
      success: false, 
      message: `Unsupported model type. Supported types: ${SUPPORTED_MODELS.join(', ')}` 
    });
  }

  // Check if user has access to the model instance
  if (modelName === 'Property') {
    const hasAccess = await propertyService.userHasAccess(req.user.id, modelId);
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to upload files to this property'
      });
    }
  }
  // TODO: Add access checks for other model types

  const { status, payload } = await fileService.uploadModelFile(
    file,
    modelName,
    modelId,
    req.user.id,
    description
  );

  if (status === 201) {
    return res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      data: payload
    });
  }

  res.status(status).json(payload);
});

/**
 * @desc    Delete a file
 * @route   DELETE /api/files/{modelName}/{fileId}
 * @access  Private
 */
const deleteModelFile = catchAsync(async (req, res) => {
  const { modelName, fileId } = req.params;
  
  if (!SUPPORTED_MODELS.includes(modelName)) {
    return res.status(400).json({ 
      success: false, 
      message: `Unsupported model type. Supported types: ${SUPPORTED_MODELS.join(', ')}` 
    });
  }

  // Get file info to check access
  const file = await fileService.getFileById(fileId);
  if (!file) {
    return res.status(404).json({
      success: false,
      message: 'File not found'
    });
  }

  // Check if user has access to the model instance
  if (file.modelName === 'Property') {
    const hasAccess = await propertyService.userHasAccess(req.user.id, file.modelId);
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to delete this file'
      });
    }
  }
  // TODO: Add access checks for other model types

  const { status, payload } = await fileService.deleteModelFile(
    modelName,
    fileId,
    req.user.id
  );

  if (status === 200) {
    return res.json({
      success: true,
      message: 'File deleted successfully'
    });
  }

  res.status(status).json(payload);
});

/**
 * @desc    Get a signed URL for downloading a file
 * @route   GET /api/files/download/{modelName}/{fileKey}
 * @access  Private
 */
const downloadFile = catchAsync(async (req, res) => {
  const { modelName, fileKey } = req.params;
  
  if (!SUPPORTED_MODELS.includes(modelName)) {
    return res.status(400).json({ 
      success: false, 
      message: `Unsupported model type. Supported types: ${SUPPORTED_MODELS.join(', ')}` 
    });
  }

  try {
    const url = await fileService.getSignedFileUrl(fileKey);
    return res.json({
      success: true,
      data: {
        url: url
      }
    });
  } catch (error) {
    logger.error('Error generating signed URL:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate download URL',
      error: error.message
    });
  }
});

/**
 * @desc    Upload a file for a model during tenant onboarding
 * @route   POST /api/public/tenants/upload/:token
 * @access  Public (but requires valid invitation token)
 */
const uploadFileWithToken = catchAsync(async (req, res) => {
  const { token } = req.params;
  
  // Validate the invitation token
  const invitation = await TenantInvitation.findOne({
    where: {
      token,
      status: 'pending',
      expiresAt: { [Op.gt]: new Date() }
    }
  });

  if (!invitation) {
    return res.status(400).json({
      success: false,
      message: 'Invalid or expired invitation token'
    });
  }

  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }

  // Extract file and model info from request
  const { file } = req;
  const { modelName, modelId, description = '' } = req.body;

  if (!modelName || !modelId) {
    return res.status(400).json({
      success: false,
      message: 'modelName and modelId are required'
    });
  }

  try {
    // For public uploads, we set uploadedBy to null since we don't have a valid user ID
    // The foreign key constraint allows NULL values for uploaded_by
    const { status, payload } = await fileService.uploadModelFile(
      file,
      modelName,
      modelId,
      null, // Set to null for public uploads
      `Public upload: ${description || 'No description provided'}`
    );

    return res.status(status).json(payload);
  } catch (error) {
    logger.error('Error in public file upload:', error);
    return res.status(500).json({
      success: false,
      message: 'Error processing file upload',
      error: error.message
    });
  }
});

module.exports = {
  getModelFiles,
  uploadModelFile,
  deleteModelFile,
  downloadFile,
  uploadFileWithToken
};
const catchAsync = require('../utils/catchAsync');
const ReferenceService = require('../services/referenceService');

/**
 * @desc    Create a new reference
 * @route   POST /api/references
 * @access  Private
 */
const createReference = catchAsync(async (req, res) => {
  // Set the userId to the current user's ID
  const referenceData = {
    ...req.body,
    userId: req.user.id // Assuming user is attached to request by auth middleware
  };
  
  const { status, payload } = await ReferenceService.createReference(referenceData);
  res.status(status).json(payload);
});

/**
 * @desc    Get all references with optional filtering
 * @route   GET /api/references
 * @access  Private
 */
const getReferences = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    status,
    isVerified,
    userId = req.user.role === 'admin' ? req.query.userId : req.user.id
  } = req.query;

  const { status: responseStatus, payload } = await ReferenceService.getAllReferences({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    status,
    isVerified: isVerified ? isVerified === 'true' : undefined,
    userId
  });
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get references for the current user
 * @route   GET /api/references/my-references
 * @access  Private
 */
const getMyReferences = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10,
    status,
    isVerified
  } = req.query;

  const { status: responseStatus, payload } = await ReferenceService.getReferencesByUserId(
    req.user.id,
    { 
      page: parseInt(page), 
      limit: parseInt(limit),
      status,
      isVerified: isVerified ? isVerified === 'true' : undefined
    }
  );
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get a single reference by ID
 * @route   GET /api/references/:id
 * @access  Private
 */
const getReferenceById = catchAsync(async (req, res) => {
  const { status, payload } = await ReferenceService.getReferenceById(req.params.id);
  
  // Ensure the user has permission to view this reference
  if (payload.success && payload.data) {
    const isAdmin = req.user.role === 'admin';
    const isOwner = payload.data.userId === req.user.id;
    
    if (!isAdmin && !isOwner) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this reference'
      });
    }
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Update a reference
 * @route   PUT /api/references/:id
 * @access  Private
 */
const updateReference = catchAsync(async (req, res) => {
  // For non-admin users, ensure they can only update their own references
  if (req.user.role !== 'admin') {
    const { payload: reference } = await ReferenceService.getReferenceById(req.params.id);
    
    if (reference.success && reference.data.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this reference'
      });
    }
  }

  const { status, payload } = await ReferenceService.updateReference(
    req.params.id, 
    req.body,
    req.user
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Delete a reference
 * @route   DELETE /api/references/:id
 * @access  Private
 */
const deleteReference = catchAsync(async (req, res) => {
  // For non-admin users, ensure they can only delete their own references
  if (req.user.role !== 'admin') {
    const { payload: reference } = await ReferenceService.getReferenceById(req.params.id);
    
    if (reference.success && reference.data.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this reference'
      });
    }
  }

  const { status, payload } = await ReferenceService.deleteReference(req.params.id);
  res.status(status).json(payload);
});

module.exports = {
  createReference,
  getReferences,
  getMyReferences,
  getReferenceById,
  updateReference,
  deleteReference
};
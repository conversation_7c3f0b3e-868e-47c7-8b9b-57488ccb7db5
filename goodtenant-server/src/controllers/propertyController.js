const catchAsync = require('../utils/catchAsync');
const PropertyService = require('../services/propertyService');
const { validationResult } = require('express-validator');

/**
 * @desc    Create a new property
 * @route   POST /api/properties
 * @access  Private
 */
const createProperty = catchAsync(async (req, res) => {
  const accountId = req.user.accountId;
  
  if (!accountId) {
    return res.status(400).json({
      success: false,
      message: 'User account not found'
    });
  }

  const propertyData = {
    ...req.body,
    accountId
  };

  const { status, payload } = await PropertyService.createProperty(
    propertyData,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get a property by ID
 * @route   GET /api/properties/:id
 * @access  Private
 */
const getPropertyById = catchAsync(async (req, res) => {
  const { status, payload } = await PropertyService.getPropertyById(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update a property
 * @route   PUT /api/properties/:id
 * @access  Private
 */
const updateProperty = catchAsync(async (req, res) => {
  const { status, payload } = await PropertyService.updateProperty(
    req.params.id,
    req.body,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete a property
 * @route   DELETE /api/properties/:id
 * @access  Private
 */
const deleteProperty = catchAsync(async (req, res) => {
  const { status, payload } = await PropertyService.deleteProperty(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get properties for the current user
 * @route   GET /api/properties/user/me
 * @access  Private
 */
const getUserProperties = catchAsync(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const { status, payload } = await PropertyService.getUserProperties(
    req.user.id,
    { 
      page: parseInt(page), 
      limit: parseInt(limit)
    }
  );
  res.status(status).json(payload);
});

/**
 * @desc    Add a user to a property
 * @route   POST /api/properties/:propertyId/users
 * @access  Private
 */
const addUserToProperty = catchAsync(async (req, res) => {
  const { propertyId } = req.params;
  const { userId, ...options } = req.body;
  
  // Note: Role management is now handled at the account level through AccountUser model
  // The options object can still contain other properties like isPrimary, startDate, etc.
  
  const { status, payload } = await PropertyService.addUserToProperty(
    propertyId,
    userId,
    options,
    req.user.id
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Remove a user from a property
 * @route   DELETE /api/properties/:propertyId/users/:userId
 * @access  Private
 */
const removeUserFromProperty = catchAsync(async (req, res) => {
  const { propertyId, userId } = req.params;
  
  const { status, payload } = await PropertyService.removeUserFromProperty(
    propertyId,
    userId,
    req.user.id
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Get properties for current user's account with search and filters
 * @route   GET /api/properties/account
 * @access  Private
 */
const getPropertiesByAccount = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '', 
    propertyType = '',
    status = ''
  } = req.query;
  
  const { status: responseStatus, payload } = await PropertyService.getPropertiesByAccount(
    req.user.id,
    { 
      page: parseInt(page), 
      limit: parseInt(limit), 
      search: search.toString().trim(),
      propertyType: propertyType.toString().trim(),
      status: status.toString().trim()
    }
  );
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get all users for a property
 * @route   GET /api/properties/:propertyId/users
 * @access  Private
 */
const getPropertyUsers = catchAsync(async (req, res) => {
  const { propertyId } = req.params;
  const { page = 1, limit = 10 } = req.query;

  const { status: responseStatus, payload } = await PropertyService.getPropertyUsers(
    propertyId,
    { 
      page: parseInt(page, 10),
      limit: parseInt(limit, 10)
    }
  );

  res.status(responseStatus).json(payload);
});

module.exports = {
  createProperty,
  getPropertyById,
  updateProperty,
  deleteProperty,
  getUserProperties,
  addUserToProperty,
  removeUserFromProperty,
  getPropertiesByAccount,
  getPropertyUsers
};
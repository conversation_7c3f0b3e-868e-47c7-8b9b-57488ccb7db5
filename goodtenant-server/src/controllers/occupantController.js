const catchAsync = require('../utils/catchAsync');
const occupantService = require('../services/occupantService');

/**
 * @desc    Create a new occupant for a lease
 * @route   POST /api/leases/:leaseId/occupants
 * @access  Private (Landlord or Tenant on the lease)
 */
const createOccupant = catchAsync(async (req, res) => {
  const { status, payload } = await occupantService.createOccupant(
    { ...req.body, leaseId: req.params.leaseId },
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get all occupants for a specific lease with optional pagination and search
 * @route   GET /api/leases/:leaseId/occupants
 * @access  Private (Landlord or Tenant on the lease)
 */
const getOccupantsByLease = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '', isActive } = req.query;
  const { status, payload } = await occupantService.getOccupantsByLease(
    req.params.leaseId,
    req.user.id,
    { 
      page: parseInt(page), 
      limit: parseInt(limit), 
      search,
      isActive
    }
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get a single occupant by ID
 * @route   GET /api/occupants/:id
 * @access  Private (Landlord or Tenant on the associated lease)
 */
const getOccupantById = catchAsync(async (req, res) => {
  const { status, payload } = await occupantService.getOccupantById(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing occupant
 * @route   PUT /api/occupants/:id
 * @access  Private (Landlord or Tenant on the associated lease)
 */
const updateOccupant = catchAsync(async (req, res) => {
  const { status, payload } = await occupantService.updateOccupant(
    req.params.id,
    req.body,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete an occupant
 * @route   DELETE /api/occupants/:id
 * @access  Private (Landlord or Tenant on the associated lease)
 */
const deleteOccupant = catchAsync(async (req, res) => {
  const { status, payload } = await occupantService.deleteOccupant(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

module.exports = {
  createOccupant,
  getOccupantsByLease,
  getOccupantById,
  updateOccupant,
  deleteOccupant
};

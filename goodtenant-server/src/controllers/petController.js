const catchAsync = require('../utils/catchAsync');
const petService = require('../services/petService');

/**
 * @desc    Create a new pet
 * @route   POST /api/pets
 * @access  Private
 */
const createPet = catchAsync(async (req, res) => {
  const { status, payload } = await petService.createPet(req.body, req.user.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get all pets for the authenticated user with optional pagination and search
 * @route   GET /api/pets
 * @access  Private
 */
const getPets = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  const { status, payload } = await petService.getUserPets(
    req.user.id,
    { 
      page: parseInt(page), 
      limit: parseInt(limit), 
      search 
    }
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get a single pet by ID
 * @route   GET /api/pets/:id
 * @access  Private
 */
const getPetById = catchAsync(async (req, res) => {
  const { status, payload } = await petService.getPetById(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing pet
 * @route   PUT /api/pets/:id
 * @access  Private
 */
const updatePet = catchAsync(async (req, res) => {
  const { status, payload } = await petService.updatePet(
    req.params.id,
    req.body,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete a pet
 * @route   DELETE /api/pets/:id
 * @access  Private
 */
const deletePet = catchAsync(async (req, res) => {
  const { status, payload } = await petService.deletePet(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Generate pet document content for a lease
 * @route   GET /api/public/leases/:id/pet-document/:token?
 * @access  Public (with token validation) or Private (with auth)
 */
const generatePetDocument = catchAsync(async (req, res) => {
  const { token } = req.params;
  
  // If no token is provided, require authentication
  if (!token && !req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required or valid token must be provided'
    });
  }
  
  const { status, payload } = await petService.generatePetDocument(
    req.params.id,
    token || null
  );
  
  res.status(status).json(payload);
});

module.exports = {
  createPet,
  getPets,
  getPetById,
  updatePet,
  deletePet,
  generatePetDocument
};

const catchAsync = require('../utils/catchAsync');
const HOAService = require('../services/hoaService');

/**
 * @desc    Create a new HOA (Homeowners Association)
 * @route   POST /api/hoas
 * @access  Private/Admin
 */
const createHOA = catchAsync(async (req, res) => {
  const { status, payload } = await HOAService.createHOA(req.body, req.user.accountId);
  res.status(status).json(payload);
});

/**
 * @desc    Get all HOAs with optional pagination and search
 * @route   GET /api/hoas
 * @access  Private
 */
const getHOAs = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  const { status, payload } = await HOAService.getAllHOAs({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search 
  });
  res.status(status).json(payload);
});

/**
 * @desc    Get a single HOA by ID
 * @route   GET /api/hoas/:id
 * @access  Private
 */
const getHOAById = catchAsync(async (req, res) => {
  const { status, payload } = await HOAService.getHOAById(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing HOA
 * @route   PUT /api/hoas/:id
 * @access  Private/Admin
 */
const updateHOA = catchAsync(async (req, res) => {
  const { status, payload } = await HOAService.updateHOA(
    req.params.id, 
    req.body
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete an HOA
 * @route   DELETE /api/hoas/:id
 * @access  Private/Admin
 */
const deleteHOA = catchAsync(async (req, res) => {
  const { status, payload } = await HOAService.deleteHOA(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get properties associated with an HOA
 * @route   GET /api/hoas/:id/properties
 * @access  Private
 */
const getHOAProperties = catchAsync(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const { status, payload } = await HOAService.getHOAProperties(
    req.params.id, 
    { page: parseInt(page), limit: parseInt(limit) }
  );
  res.status(status).json(payload);
});

/**
 * @desc    Generate HOA document content for a lease
 * @route   GET /api/leases/:leaseId/hoa-document
 * @access  Private
 */
const generateHOADocument = catchAsync(async (req, res) => {
  const { leaseId } = req.params;
  const token = req.query.token || null;
  
  const { status, payload } = await HOAService.generateHOADocument(leaseId, token);
  res.status(status).json(payload);
});

/**
 * @desc    Public endpoint to generate HOA document content for a lease with token
 * @route   GET /api/public/leases/:leaseId/hoa-document/:token
 * @access  Public (with valid token)
 */
const generatePublicHOADocument = catchAsync(async (req, res) => {
  const { leaseId, token } = req.params;
  
  const { status, payload } = await HOAService.generateHOADocument(leaseId, token);
  res.status(status).json(payload);
});

module.exports = {
  createHOA,
  getHOAs,
  getHOAById,
  updateHOA,
  deleteHOA,
  getHOAProperties,
  generateHOADocument,
  generatePublicHOADocument
};
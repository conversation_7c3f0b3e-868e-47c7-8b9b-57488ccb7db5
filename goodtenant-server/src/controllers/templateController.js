const catchAsync = require('../utils/catchAsync');
const templateService = require('../services/templateService');

/**
 * @desc    Get all system variables available for templates
 * @route   GET /api/templates/variables
 * @access  Private
 */
const getSystemVariables = catchAsync(async (req, res) => {
  const { status, payload } = await templateService.getSystemVariables();
  res.status(status).json(payload);
});

/**
 * @desc    Create a new template
 * @route   POST /api/templates
 * @access  Private
 */
const createTemplate = catchAsync(async (req, res) => {
  const { status, payload } = await templateService.createTemplate(
    {
      ...req.body,
      accountId: req.user.accountId // Add accountId from authenticated user
    },
    req.user.id // userId from authenticated user
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get all templates with optional pagination and search
 * @route   GET /api/templates
 * @access  Private
 */
const getTemplates = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    type,
    isActive
  } = req.query;

  const { status, payload } = await templateService.getAllTemplates({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    accountId: req.user.accountId, // Filter by user's account
    type,
    isActive: isActive ? isActive === 'true' : undefined
  });
  
  res.status(status).json(payload);
});

/**
 * @desc    Get a single template by ID
 * @route   GET /api/templates/:id
 * @access  Private
 */
const getTemplateById = catchAsync(async (req, res) => {
  const { status, payload } = await templateService.getTemplateById(req.params.id);
  
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing template
 * @route   PUT /api/templates/:id
 * @access  Private
 */
const updateTemplate = catchAsync(async (req, res) => {
  // First get the template to verify ownership
  const { payload: templateData } = await templateService.getTemplateById(req.params.id);
  
  if (!templateData.success || !templateData.data) {
    return res.status(404).json({
      success: false,
      message: 'Template not found'
    });
  }


  const { status, payload } = await templateService.updateTemplate(
    req.params.id,
    req.body,
    req.user.id // userId from authenticated user
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Delete a template
 * @route   DELETE /api/templates/:id
 * @access  Private
 */
const deleteTemplate = catchAsync(async (req, res) => {
  // First get the template to verify ownership
  const { payload: templateData } = await templateService.getTemplateById(req.params.id);
  
  if (!templateData.success || !templateData.data) {
    return res.status(404).json({
      success: false,
      message: 'Template not found'
    });
  }


  const { status, payload } = await templateService.deleteTemplate(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Generate a document from a template
 * @route   POST /api/templates/:id/generate
 * @access  Private
 */
const generateDocument = catchAsync(async (req, res) => {
  // First get the template to verify ownership
  const { payload: templateData } = await templateService.getTemplateById(req.params.id);
  
  if (!templateData.success || !templateData.data) {
    return res.status(404).json({
      success: false,
      message: 'Template not found'
    });
  }


  const { status, payload } = await templateService.generateDocument(
    req.params.id,
    req.body.data || {}
  );
  
  res.status(status).json(payload);
});

module.exports = {
  getSystemVariables,
  createTemplate,
  getTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate,
  generateDocument
};

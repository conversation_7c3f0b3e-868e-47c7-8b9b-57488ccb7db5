// src/controllers/inventoryController.js
const catchAsync = require('../utils/catchAsync');
const inventoryService = require('../services/inventoryService');

/**
 * @desc    Create a new inventory item
 * @route   POST /api/inventory
 * @access  Private
 */
const createInventoryItem = catchAsync(async (req, res) => {
  // Add createdBy and updatedBy from the authenticated user
  const inventoryData = {
    ...req.body,
    createdBy: req.user.id,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await inventoryService.createInventoryItem(inventoryData);
  res.status(status).json(payload);
});

/**
 * @desc    Get all inventory items with optional filtering
 * @route   GET /api/inventory
 * @access  Private
 */
const getInventoryItems = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    propertyId,
    status,
    category
  } = req.query;
  
  const { status: responseStatus, payload } = await inventoryService.getAllInventoryItems({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    propertyId,
    status,
    category
  });
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get a single inventory item by ID
 * @route   GET /api/inventory/:id
 * @access  Private
 */
const getInventoryItemById = catchAsync(async (req, res) => {
  const { status, payload } = await inventoryService.getInventoryItemById(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Update an inventory item
 * @route   PUT /api/inventory/:id
 * @access  Private
 */
const updateInventoryItem = catchAsync(async (req, res) => {
  // Add updatedBy from the authenticated user
  const updateData = {
    ...req.body,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await inventoryService.updateInventoryItem(
    req.params.id, 
    updateData
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete an inventory item
 * @route   DELETE /api/inventory/:id
 * @access  Private/Admin
 */
const deleteInventoryItem = catchAsync(async (req, res) => {
  const { status, payload } = await inventoryService.deleteInventoryItem(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get inventory items by property ID
 * @route   GET /api/properties/:propertyId/inventory
 * @access  Private
 */
const getInventoryItemsByPropertyId = catchAsync(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const { status, payload } = await inventoryService.getInventoryItemsByPropertyId(
    req.params.propertyId, 
    { page: parseInt(page), limit: parseInt(limit) }
  );
  res.status(status).json(payload);
});

module.exports = {
  createInventoryItem,
  getInventoryItems,
  getInventoryItemById,
  updateInventoryItem,
  deleteInventoryItem,
  getInventoryItemsByPropertyId
};
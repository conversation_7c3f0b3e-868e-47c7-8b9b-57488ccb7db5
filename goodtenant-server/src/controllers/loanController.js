const catchAsync = require('../utils/catchAsync');
const LoanService = require('../services/loanService');
const { Property, Loan } = require('../models');

/**
 * @desc    Create a new loan
 * @route   POST /api/loans
 * @access  Private
 */
const createLoan = catchAsync(async (req, res) => {
  const { status, payload } = await LoanService.createLoan({
    ...req.body,
    accountId: req.user.accountId // Assuming user is authenticated and has accountId
  });
  res.status(status).json(payload);
});

/**
 * @desc    Get all loans with optional pagination and search
 * @route   GET /api/loans
 * @access  Private
 */
const getLoans = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    propertyId 
  } = req.query;

  const { status, payload } = await LoanService.getAllLoans({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    accountId: req.user.accountId, // Filter by user's account
    propertyId
  });
  
  res.status(status).json(payload);
});

/**
 * @desc    Get a single loan by ID
 * @route   GET /api/loans/:id
 * @access  Private
 */
const getLoanById = catchAsync(async (req, res) => {
  const { status, payload } = await LoanService.getLoanById(req.params.id);
  
  console.log('Loan data from service:', JSON.stringify(payload, null, 2));
  console.log('User account ID:', req.user.accountId);
  
  // Verify the loan belongs to the user's account using _accountId from the payload
  if (payload.success && payload._accountId && payload._accountId !== req.user.accountId) {
    console.log('Permission denied - account IDs do not match');
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to access this loan'
    });
  }
  
  // Remove _accountId from the final response
  const { _accountId, ...responsePayload } = payload;
  res.status(status).json(responsePayload);
});

/**
 * @desc    Update an existing loan
 * @route   PUT /api/loans/:id
 * @access  Private
 */
const updateLoan = catchAsync(async (req, res) => {
  // First get the loan to verify ownership
  const { payload: loanData } = await LoanService.getLoanById(req.params.id);
  
  console.log('Update loan - Loan data:', JSON.stringify(loanData, null, 2));
  console.log('Update loan - User account ID:', req.user.accountId);
  
  if (!loanData.success) {
    return res.status(404).json({
      success: false,
      message: 'Loan not found'
    });
  }

  // Check permission using _accountId from the payload
  if (loanData._accountId && loanData._accountId !== req.user.accountId) {
    console.log('Update loan - Permission denied - account IDs do not match');
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to update this loan'
    });
  }

  const { status, payload } = await LoanService.updateLoan(
    req.params.id, 
    req.body
  );
  
  // Remove _accountId from the final response if it exists
  if (payload && payload._accountId) {
    const { _accountId, ...responsePayload } = payload;
    return res.status(status).json(responsePayload);
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Delete a loan
 * @route   DELETE /api/loans/:id
 * @access  Private
 */
const deleteLoan = catchAsync(async (req, res) => {
  // Get the loan directly to verify ownership
  const loan = await Loan.findByPk(req.params.id);
  
  if (!loan) {
    return res.status(404).json({
      success: false,
      message: 'Loan not found'
    });
  }

  if (loan.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to delete this loan'
    });
  }

  const { status, payload } = await LoanService.deleteLoan(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get loans by property ID
 * @route   GET /api/properties/:propertyId/loans
 * @access  Private
 */
const getLoansByPropertyId = catchAsync(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  // First verify the property belongs to the user's account
  const property = await Property.findByPk(req.params.propertyId);
  if (!property || property.accountId !== req.user.accountId) {
    return res.status(404).json({
      success: false,
      message: 'Property not found or access denied'
    });
  }

  const { status, payload } = await LoanService.getLoansByPropertyId(
    req.params.propertyId,
    { 
      page: parseInt(page), 
      limit: parseInt(limit) 
    }
  );
  
  res.status(status).json(payload);
});

module.exports = {
  createLoan,
  getLoans,
  getLoanById,
  updateLoan,
  deleteLoan,
  getLoansByPropertyId
};
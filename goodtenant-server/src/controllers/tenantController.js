const { Op } = require('sequelize');
const catchAsync = require('../utils/catchAsync');
const tenantService = require('../services/tenantService');
const pdfService = require('../services/pdfService');
const fileService = require('../services/fileService');
const { getUserSignature } = require('../services/userFileService');
const models = require('../models');
const logger = require('../utils/logger');
const { validateToken } = require('../middlewares/auth');

/**
 * Finalize tenant onboarding by updating the invitation status to 'accepted'
 * @route   PATCH /api/tenant-invitations/:invitationId/finalize
 * @access  Private (Tenant)
 */
const finalizeTenantOnboarding = catchAsync(async (req, res) => {
  const { token } = req.params;
  
  // For public routes, we might not have a user ID
  const userId = req.user?.id || null;
  
  const result = await tenantService.finalizeTenantOnboardingByToken(token, userId);
  
  res.status(result.status).json(result.payload);
});

/**
 * Create a tenant invitation
 * @route   POST /api/properties/:propertyId/invitations
 * @access  Private (Landlord/Admin)
 */
const createTenantInvitation = catchAsync(async (req, res) => {
  const { propertyId } = req.params;
  const { accountId } = req.user;
  const userId = req.user.id; // Get userId from req.user.id
  
  
  const result = await tenantService.createTenantInvitation(
    accountId,
    propertyId,
    req.body,
    userId
  );
  
  res.status(result.status).json(result.payload);
});
/**
 * Get tenant invitations for a property
 * @route   GET /api/properties/:propertyId/invitations
 * @access  Private (Landlord/Admin)
 */
const getPropertyInvitations = catchAsync(async (req, res) => {
  const { propertyId } = req.params;
  const { accountId } = req.user;
  const { page, limit, status } = req.query;
  
  const result = await tenantService.getPropertyInvitations(
    accountId,
    propertyId,
    { page, limit, status }
  );
  
  res.status(result.status).json(result.payload);
});

/**
 * Verify tenant invitation token
 * @route   GET /api/tenant-invitations/verify/:token
 * @access  Public
 */
const verifyInvitationToken = catchAsync(async (req, res) => {
  const { token } = req.params;
  const result = await tenantService.verifyInvitationToken(token);
  res.status(result.status).json(result.payload);
});

/**
 * Complete tenant onboarding
 * @route   POST /api/tenant-onboarding/:token
 * @access  Public
 */
const completeTenantOnboarding = catchAsync(async (req, res) => {
  const { token } = req.params;
  const result = await tenantService.completeTenantOnboarding(
    token,
    req.body.userData,
    req.body.additionalData
  );
  res.status(result.status).json(result.payload);
});

/**
 * Cancel a tenant invitation
 * @route   DELETE /api/tenant-invitations/:invitationId
 * @access  Private (Landlord/Admin)
 */
const cancelInvitation = catchAsync(async (req, res) => {
  const { invitationId } = req.params;
  const { accountId } = req.user;
  
  const result = await tenantService.cancelInvitation(accountId, invitationId);
  res.status(result.status).json(result.payload);
});

/**
 * Resend a tenant invitation
 * @route   POST /api/tenant-invitations/:invitationId/resend
 * @access  Private (Landlord/Admin)
 */
const resendInvitation = catchAsync(async (req, res) => {
  const { invitationId } = req.params;
  const { accountId } = req.user;
  
  const result = await tenantService.resendInvitation(accountId, invitationId);
  res.status(result.status).json(result.payload);
});

/**
 * Sign a lease document
 * @route   POST /api/tenants/leases/:leaseId/sign
 * @access  Private (Tenant)
 */
const signLeaseDocument = catchAsync(async (req, res) => {
  const { leaseId } = req.params;
  const { signature } = req.body;
  const userId = req.user.id;
  
  const result = await tenantService.signLeaseDocument(leaseId, signature, userId);
  res.status(result.status).json(result.payload);
});


/**
 * Get landlord signature by lease (public with token)
 * @route   GET /api/public/leases/:leaseId/landlord-signature/:token
 * @access  Public (with token)
 */
const getLandlordSignatureByLease = catchAsync(async (req, res) => {
  const { leaseId, token } = req.params;
  const { Lease, User, TenantInvitation } = models;
  
  // Debug log the token being checked
  logger.info(`Validating token: ${token}`);
  
  try {
    // Verify the token is valid and get the invitation
    const invitation = await TenantInvitation.findOne({
      where: { 
        token: token,  
        status: 'pending',
        expiresAt: { [Op.gt]: new Date() }  
      },
      raw: true 
    });

    logger.info('Found invitation:', invitation);

    if (!invitation) {
      logger.warn('No invitation found for token:', token);
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token',
        debug: {
          token: token,
          timestamp: new Date().toISOString()
        }
      });
    }
    
    // Find the lease with the associated landlord
    const lease = await Lease.findByPk(leaseId, {
      include: [{
        model: User,
        as: 'landlord',
        attributes: ['id', 'signature'] // Include signature field
      }]
    });

    if (!lease) {
      logger.warn('Lease not found:', leaseId);
      return res.status(404).json({
        success: false,
        message: 'Lease not found'
      });
    }

    if (!lease.landlord) {
      logger.warn('Landlord not found for lease:', leaseId);
      return res.status(404).json({
        success: false,
        message: 'Landlord not found for this lease'
      });
    }

    // Use the userFileService to get the signed URL for the signature
    const result = await getUserSignature(lease.landlord.id);
    
    if (result.status !== 200) {
      logger.warn('No signature found for landlord:', lease.landlord.id);
      return res.status(404).json({
        success: false,
        message: 'Landlord has no signature on file'
      });
    }

    logger.info('Successfully retrieved signature for lease:', leaseId);
    
    return res.status(200).json({
      success: true,
      data: result.payload
    });
  } catch (error) {
    logger.error('Error in getLandlordSignatureByLease:', {
      error: error.message,
      stack: error.stack,
      leaseId,
      token
    });
    
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch landlord signature',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});



module.exports = {
  createTenantInvitation,
  getPropertyInvitations,
  verifyInvitationToken,
  completeTenantOnboarding,
  cancelInvitation,
  resendInvitation,
  signLeaseDocument,
  finalizeTenantOnboarding,
  getLandlordSignatureByLease
};
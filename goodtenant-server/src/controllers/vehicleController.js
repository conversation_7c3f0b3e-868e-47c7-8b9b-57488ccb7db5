const catchAsync = require('../utils/catchAsync');
const VehicleService = require('../services/vehicleService');

/**
 * @desc    Create a new vehicle for the authenticated user
 * @route   POST /api/vehicles
 * @access  Private
 */
const createVehicle = catchAsync(async (req, res) => {
  const { status, payload } = await VehicleService.createVehicle(
    req.user.id, // User ID from auth middleware
    req.body
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get all vehicles for the authenticated user with optional pagination and search
 * @route   GET /api/vehicles
 * @access  Private
 */
const getUserVehicles = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  const { status, payload } = await VehicleService.getUserVehicles(
    req.user.id, // User ID from auth middleware
    { 
      page: parseInt(page), 
      limit: parseInt(limit), 
      search 
    }
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get a single vehicle by ID (must belong to the authenticated user)
 * @route   GET /api/vehicles/:id
 * @access  Private
 */
const getVehicleById = catchAsync(async (req, res) => {
  const { status, payload } = await VehicleService.getVehicleById(
    req.user.id, // User ID from auth middleware
    req.params.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update a vehicle (must belong to the authenticated user)
 * @route   PUT /api/vehicles/:id
 * @access  Private
 */
const updateVehicle = catchAsync(async (req, res) => {
  const { status, payload } = await VehicleService.updateVehicle(
    req.user.id, // User ID from auth middleware
    req.params.id,
    req.body
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete a vehicle (must belong to the authenticated user)
 * @route   DELETE /api/vehicles/:id
 * @access  Private
 */
const deleteVehicle = catchAsync(async (req, res) => {
  const { status, payload } = await VehicleService.deleteVehicle(
    req.user.id, // User ID from auth middleware
    req.params.id
  );
  res.status(status).json(payload);
});

module.exports = {
  createVehicle,
  getUserVehicles,
  getVehicleById,
  updateVehicle,
  deleteVehicle
};

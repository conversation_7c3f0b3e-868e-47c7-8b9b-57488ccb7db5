const catchAsync = require('../utils/catchAsync');
const userService = require('../services/userService');
const logger = require('../utils/logger');
/**
 * Get all users (admin only)
 * @route GET /api/users
 */
/**
 * Get all users (admin only)
 * @route GET /api/users
 * @query {number} [page=1] - Page number
 * @query {number} [limit=10] - Items per page
 * @query {string} [role] - Filter by role
 * @query {string} [status] - Filter by status
 * @query {string} [search] - Search term for name or email
 */
const getAllUsers = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, role, status, search } = req.query;
  
  const { status: responseStatus, payload } = await userService.getAllUsers({
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    search,  // Pass search as top-level parameter
    filters: { role },  // Only role in filters
    accountId: req.user.accountId
  });
  
  res.status(responseStatus).json(payload);
});

/**
 * Get current user profile
 * @route GET /api/users/me
 */
// Example in userController.js
const getCurrentUser = catchAsync(async (req, res) => {
  // If accountId is required for this operation
  if (!req.user.accountId) {
    return res.status(400).json({
      success: false,
      message: 'User account not found'
    });
  }
  
  const { status, payload } = await userService.getUserById(
    req.user.id, 
    req.user.accountId
  );
  res.status(status).json(payload);
});
/**
 * Get user by ID
 * @route GET /api/users/:id
 */
const getUserById = catchAsync(async (req, res) => {
  const { status, payload } = await userService.getUserById(
    req.params.id, 
    req.user.accountId
  );
  res.status(status).json(payload);
});

/**
 * Create a new user (admin only)
 * @route POST /api/users
 */
const createUser = catchAsync(async (req, res) => {
  // Extract role information from request body
  const { 
    primaryRole = 'tenant', 
    additionalRoles = [], 
    ...userData 
  } = req.body;
  
  // Prepare roleInfo object for the service
  const roleInfo = {
    primaryRole,
    additionalRoles: Array.isArray(additionalRoles) ? additionalRoles : []
  };

  const { status, payload } = await userService.createUser(
    userData, 
    roleInfo,
    req.user.accountId // Pass the logged-in user's account ID
  );
  
  res.status(status).json(payload);
});

/**
 * Update user
 * @route PUT /api/users/:id
 */
const updateUser = catchAsync(async (req, res) => {
  const { primaryRole, additionalRoles = [], ...userData } = req.body;
  
  const { status, payload } = await userService.updateUser(
    req.params.id,
    userData,
    req.user.accountId,
    { primaryRole, additionalRoles: Array.isArray(additionalRoles) ? additionalRoles : [] }
  );
  
  res.status(status).json(payload);
});

/**
 * Update current user profile
 * @route PUT /api/users/me
 */
const updateProfile = catchAsync(async (req, res) => {
  const { status, payload } = await userService.updateProfile(
    req.user.id,
    req.body
  );
  res.status(status).json(payload);
});

/**
 * Change user password
 * @route POST /api/users/me/change-password
 */
const changePassword = catchAsync(async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const { status, payload } = await userService.changePassword(
    req.user.id,
    currentPassword,
    newPassword,
    req.user.accountId
  );
  res.status(status).json(payload);
});

/**
 * Delete user (admin only)
 * @route DELETE /api/users/:id
 */
const deleteUser = catchAsync(async (req, res) => {
  logger.debug('Delete user request', { 
    targetUserId: req.params.id, 
    currentUserId: req.user.id,
    accountId: req.user.accountId
  });
  
  // Prevent users from deleting themselves
  if (req.params.id === req.user.id) {
    logger.warn('User attempted to delete their own account', { userId: req.user.id });
    return res.status(400).json({
      success: false,
      message: 'You cannot delete your own account'
    });
  }

  // Check if the current user is an admin for the account
  const isAdmin = await userService.isUserAdmin(req.user.id, req.user.accountId);
  logger.debug('Admin check result', { 
    userId: req.user.id, 
    accountId: req.user.accountId, 
    isAdmin: isAdmin 
  });
  
  if (!isAdmin) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to delete users',
      details: 'User is not an admin for the current account'
    });
  }

  // Check if the target user belongs to the same account
  logger.debug('Checking if target user belongs to the same account', {
    targetId: req.params.id,
    accountId: req.user.accountId
  });
  
  const { status: userStatus, payload: userPayload } = await userService.getUserById(req.params.id, req.user.accountId);
  logger.debug('Target user lookup result', { 
    targetId: req.params.id, 
    status: userStatus,
    success: userPayload?.success,
    hasData: !!userPayload?.data
  });
  
  if (userStatus !== 200 || !userPayload?.success) {
    return res.status(404).json({
      success: false,
      message: 'User not found or you do not have permission to delete this user',
      details: userPayload?.message || 'User lookup failed'
    });
  }

  logger.debug('Proceeding with user deletion', { targetId: req.params.id });
  const { status, payload } = await userService.deleteUser(req.params.id);
  res.status(status).json(payload);
});

module.exports = {
  getAllUsers,
  getCurrentUser,
  getUserById,
  createUser,
  updateUser,
  updateProfile,
  changePassword,
  deleteUser
};

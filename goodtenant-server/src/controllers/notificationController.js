const catchAsync = require('../utils/catchAsync');
const notificationService = require('../services/notificationService');

/**
 * @desc    Create a new notification
 * @route   POST /api/notifications
 * @access  Private
 */
const createNotification = catchAsync(async (req, res) => {
  const { status, payload } = await notificationService.createNotification(
    req.body,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get all notifications with optional filtering and pagination
 * @route   GET /api/notifications
 * @access  Private
 */
const getNotifications = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    isRead, 
    type, 
    entityType, 
    entityId, 
    startDate, 
    endDate,
    sortBy = 'createdAt',
    sortOrder = 'DESC'
  } = req.query;

  // Default to current user's notifications if not admin
  const userId = req.user.isAdmin && req.query.userId ? req.query.userId : req.user.id;

  const { status, payload } = await notificationService.getAllNotifications({
    page: parseInt(page),
    limit: parseInt(limit),
    isRead: isRead ? isRead === 'true' : undefined,
    userId,
    type,
    entityType,
    entityId,
    startDate,
    endDate,
    sortBy,
    sortOrder
  });

  res.status(status).json(payload);
});

/**
 * @desc    Get a single notification by ID
 * @route   GET /api/notifications/:id
 * @access  Private
 */
const getNotificationById = catchAsync(async (req, res) => {
  const { status, payload } = await notificationService.getNotificationById(
    req.params.id
  );
  
  // Verify the requesting user has access to this notification
  if (payload.data && !req.user.isAdmin && payload.data.userId !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to access this notification'
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Mark a notification as read
 * @route   PATCH /api/notifications/:id/read
 * @access  Private
 */
const markAsRead = catchAsync(async (req, res) => {
  // First get the notification to verify ownership
  const notification = await notificationService.getNotificationById(req.params.id);
  
  // Verify the requesting user owns this notification
  if (notification.payload.data && notification.payload.data.userId !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to update this notification'
    });
  }
  
  const { status, payload } = await notificationService.markAsRead(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Mark all user's notifications as read
 * @route   PATCH /api/notifications/read-all
 * @access  Private
 */
const markAllAsRead = catchAsync(async (req, res) => {
  const { status, payload } = await notificationService.markAllAsRead(req.user.id);
  res.status(status).json(payload);
});

/**
 * @desc    Delete a notification
 * @route   DELETE /api/notifications/:id
 * @access  Private
 */
const deleteNotification = catchAsync(async (req, res) => {
  // First get the notification to verify ownership
  const notification = await notificationService.getNotificationById(req.params.id);
  
  // Verify the requesting user owns this notification
  if (notification.payload.data && notification.payload.data.userId !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to delete this notification'
    });
  }
  
  const { status, payload } = await notificationService.deleteNotification(req.params.id);
  res.status(status).json(payload);
});

module.exports = {
  createNotification,
  getNotifications,
  getNotificationById,
  markAsRead,
  markAllAsRead,
  deleteNotification
};

const catchAsync = require('../utils/catchAsync');
const accountStatsService = require('../services/accountStatsService');

/**
 * @desc    Get account statistics
 * @route   GET /api/account/stats
 * @access  Private
 */
const getAccountStats = catchAsync(async (req, res) => {
  const { status, payload } = await accountStatsService.getAccountStats(req.user.accountId);
  res.status(status).json(payload);
});

module.exports = {
  getAccountStats
};

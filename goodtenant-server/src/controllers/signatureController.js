// controllers/signatureController.js
const {
    uploadUserSignature,
    deleteUserSignature,
    getUserSignature
  } = require('../services/userFileService');
  
  exports.uploadSignature = async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }
  
      const result = await uploadUserSignature(req.file, req.user.id);
      res.status(result.status).json(result.payload);
    } catch (error) {
      console.error('Error uploading signature:', error);
      res.status(500).json({ message: 'Error uploading signature', error: error.message });
    }
  };
  
  exports.deleteSignature = async (req, res) => {
    try {
      const result = await deleteUserSignature(req.user.id);
      res.status(result.status).json(result.payload);
    } catch (error) {
      console.error('Error deleting signature:', error);
      res.status(500).json({ message: 'Error deleting signature', error: error.message });
    }
  };
  
  exports.getSignature = async (req, res) => {
    try {
      const result = await getUserSignature(req.user.id);
      res.status(result.status).json(result.payload);
    } catch (error) {
      console.error('Error getting signature:', error);
      res.status(500).json({ message: 'Error getting signature', error: error.message });
    }
  };
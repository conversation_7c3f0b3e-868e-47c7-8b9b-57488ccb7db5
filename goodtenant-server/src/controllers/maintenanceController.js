// controllers/maintenanceController.js
const catchAsync = require('../utils/catchAsync');
const maintenanceService = require('../services/maintenanceService');
const eventEmitter = require('../utils/eventEmitter');

/**
 * @desc    Create a new maintenance ticket for a property
 * @route   POST /api/maintenance/property/:propertyId
 * @access  Private
 * @param   {string} req.params.propertyId - The ID of the property to create the ticket for
 */
const createTicket = catchAsync(async (req, res) => {
  const ticketData = {
    ...req.body,
    propertyId: req.params.propertyId, // Get propertyId from URL parameter
    reportedBy: req.user.id,
    accountId: req.user.accountId
  };

  const { status, payload } = await maintenanceService.createTicket(ticketData, req.user.id);
  
  // Only emit the event if the ticket was created successfully
  if (status === 201) {
    eventEmitter.emit('maintenance.created', { 
      maintenance: payload.data, 
      createdById: req.user.id 
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Get a single ticket by ID
 * @route   GET /api/maintenance/:id
 * @access  Private
 */
const getTicket = catchAsync(async (req, res) => {
  const { status, payload } = await maintenanceService.getTicketById(
    req.params.id,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update a ticket
 * @route   PUT /api/maintenance/:id
 * @access  Private
 */
const updateTicket = catchAsync(async (req, res) => {
  const { status, payload } = await maintenanceService.updateTicket(
    req.params.id,
    req.body,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Assign a ticket to a user
 * @route   PATCH /api/maintenance/:id/assign
 * @access  Private
 */
const assignTicket = catchAsync(async (req, res) => {
  const { assigneeId } = req.body;
  if (!assigneeId) {
    return res.status(400).json({
      success: false,
      message: 'Assignee ID is required'
    });
  }

  const { status, payload } = await maintenanceService.assignTicket(
    req.params.id,
    assigneeId,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update ticket status
 * @route   PATCH /api/maintenance/:id/status
 * @access  Private
 */
const updateStatus = catchAsync(async (req, res) => {
  const { status: newStatus } = req.body;
  if (!newStatus) {
    return res.status(400).json({
      success: false,
      message: 'Status is required'
    });
  }

  const { status, payload } = await maintenanceService.updateTicketStatus(
    req.params.id,
    newStatus,
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get tickets with optional filtering
 * @route   GET /api/maintenance
 * @access  Private
 */
const getTickets = catchAsync(async (req, res) => {
  const { 
    status, 
    priority, 
    assignedTo, 
    propertyId,
    page = 1, 
    limit = 10 
  } = req.query;

  const filters = {
    status,
    priority,
    assignedTo,
    propertyId,
    page: parseInt(page),
    limit: parseInt(limit)
  };

  // Only add accountId filter if user is not admin
  if (!req.user.roles.includes('account_owner')) {
    filters.accountId = req.user.accountId;
  }

  const { status: responseStatus, payload } = await maintenanceService.getTickets(filters);
  res.status(responseStatus).json(payload);
});

module.exports = {
  createTicket,
  getTicket,
  updateTicket,
  assignTicket,
  updateStatus,
  getTickets
};
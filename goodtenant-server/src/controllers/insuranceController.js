const catchAsync = require('../utils/catchAsync');
const insuranceService = require('../services/insuranceService');
const { Property } = require('../models'); // Import Property from models/index.js

/**
 * @desc    Create a new Insurance
 * @route   POST /api/insurances
 * @access  Private
 */
const createInsurance = catchAsync(async (req, res) => {
  // Add createdBy reference to the current user
  const insuranceData = {
    ...req.body,
    createdBy: req.user.id,
    accountId: req.user.accountId // Assuming user has accountId
  };
  
  const { status, payload } = await insuranceService.createInsurance(insuranceData);
  res.status(status).json(payload);
});

/**
 * @desc    Get all Insurances with optional filtering and pagination
 * @route   GET /api/insurances
 * @access  Private
 */
const getInsurances = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    propertyId,
    isActive
  } = req.query;
  
  // If user is not admin, only show insurances for their account
  const accountId = req.user.role === 'admin' ? null : req.user.accountId;
  
  const { status, payload } = await insuranceService.getAllInsurances({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    propertyId,
    accountId,
    isActive
  });
  
  res.status(status).json(payload);
});

/**
 * @desc    Get a single Insurance by ID
 * @route   GET /api/insurances/:id
 * @access  Private
 */
const getInsuranceById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { status, payload } = await insuranceService.getInsuranceById(id);
  
  // Check if the user has permission to view this insurance
  if (payload.success && req.user.role !== 'admin' && payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to view this insurance'
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing Insurance
 * @route   PUT /api/insurances/:id
 * @access  Private
 */
const updateInsurance = catchAsync(async (req, res) => {
  const { id } = req.params;
  
  // First, verify the insurance exists and user has permission
  const existingInsurance = await insuranceService.getInsuranceById(id);
  
  if (!existingInsurance.payload.success) {
    return res.status(existingInsurance.status).json(existingInsurance.payload);
  }
  
  // Check if user has permission (admin or owner of the account)
  if (req.user.role !== 'admin' && existingInsurance.payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to update this insurance'
    });
  }
  
  // Add updatedBy reference
  const updateData = {
    ...req.body,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await insuranceService.updateInsurance(
    id, 
    updateData,
    req.user.id
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Delete an Insurance (soft delete)
 * @route   DELETE /api/insurances/:id
 * @access  Private
 */
const deleteInsurance = catchAsync(async (req, res) => {
  const { id } = req.params;
  
  // First, verify the insurance exists and user has permission
  const existingInsurance = await insuranceService.getInsuranceById(id);
  
  if (!existingInsurance.payload.success) {
    return res.status(existingInsurance.status).json(existingInsurance.payload);
  }
  
  // Check if user has permission (admin or owner of the account)
  if (req.user.role !== 'admin' && existingInsurance.payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to delete this insurance'
    });
  }
  
  const { status, payload } = await insuranceService.deleteInsurance(id);
  res.status(status).json(payload);
});

/**
 * @desc    Get insurances for a specific property
 * @route   GET /api/properties/:propertyId/insurances
 * @access  Private
 */
const getPropertyInsurances = catchAsync(async (req, res) => {
  const { propertyId } = req.params;
  const { page = 1, limit = 10, isActive } = req.query;
  
  // If user is not admin, verify they have access to this property
  if (req.user.role !== 'admin') {
    const property = await Property.findByPk(propertyId, {
      attributes: ['id', 'accountId']
    });
    
    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }
    
    if (property.accountId !== req.user.accountId) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to view insurances for this property'
      });
    }
  }
  
  const { status, payload } = await insuranceService.getPropertyInsurances(
    propertyId,
    { 
      page: parseInt(page), 
      limit: parseInt(limit),
      isActive
    }
  );
  
  res.status(status).json(payload);
});

module.exports = {
  createInsurance,
  getInsurances,
  getInsuranceById,
  updateInsurance,
  deleteInsurance,
  getPropertyInsurances
};

const catchAsync = require('../utils/catchAsync');
const taskService = require('../services/taskService');

/**
 * @desc    Create a new task
 * @route   POST /api/tasks
 * @access  Private
 */
const createTask = catchAsync(async (req, res) => {
  const { status, payload } = await taskService.createTask(
    { 
      ...req.body,
      accountId: req.user.accountId, // Use the authenticated user's account
      createdById: req.user.id // Track who created the task
    },
    req.user.id
  );
  res.status(status).json(payload);
});

/**
 * @desc    Get all tasks with optional pagination and filters
 * @route   GET /api/tasks
 * @access  Private
 */
const getTasks = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    status,
    priority,
    type,
    assignedTo,
    propertyId,
    dueDateFrom,
    dueDateTo
  } = req.query;

  const { status: responseStatus, payload } = await taskService.getAllTasks({
    page: parseInt(page),
    limit: parseInt(limit),
    search,
    status,
    priority,
    type,
    assignedTo,
    propertyId,
    accountId: req.user.accountId, // Filter by user's account
    dueDateFrom,
    dueDateTo
  });

  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get a single task by ID
 * @route   GET /api/tasks/:id
 * @access  Private
 */
const getTaskById = catchAsync(async (req, res) => {
  const { status, payload } = await taskService.getTaskById(req.params.id);
  
  // Verify task belongs to user's account
  if (payload.success && payload.data && payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to access this task'
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Update a task
 * @route   PUT /api/tasks/:id
 * @access  Private
 */
const updateTask = catchAsync(async (req, res) => {
  // First get the task to verify ownership
  const { payload: taskData } = await taskService.getTaskById(req.params.id);
  
  if (!taskData.success || !taskData.data) {
    return res.status(404).json({
      success: false,
      message: 'Task not found'
    });
  }
  
  // Verify task belongs to user's account
  if (taskData.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to update this task'
    });
  }
  
  const { status, payload } = await taskService.updateTask(
    req.params.id,
    req.body,
    req.user.id // Pass the current user ID for tracking who made the update
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Delete a task
 * @route   DELETE /api/tasks/:id
 * @access  Private
 */
const deleteTask = catchAsync(async (req, res) => {
  // First get the task to verify ownership
  const { payload: taskData } = await taskService.getTaskById(req.params.id);
  
  if (!taskData.success || !taskData.data) {
    return res.status(404).json({
      success: false,
      message: 'Task not found'
    });
  }
  
  // Verify task belongs to user's account
  if (taskData.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to delete this task'
    });
  }
  
  const { status, payload } = await taskService.deleteTask(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get tasks for the current user (assigned to them)
 * @route   GET /api/tasks/me
 * @access  Private
 */
const getMyTasks = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    status,
    priority,
    type,
    dueDateFrom,
    dueDateTo
  } = req.query;

  const { status: responseStatus, payload } = await taskService.getAllTasks({
    page: parseInt(page),
    limit: parseInt(limit),
    status,
    priority,
    type,
    assignedTo: req.user.id, // Only tasks assigned to the current user
    accountId: req.user.accountId,
    dueDateFrom,
    dueDateTo
  });

  res.status(responseStatus).json(payload);
});

module.exports = {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  deleteTask,
  getMyTasks
};

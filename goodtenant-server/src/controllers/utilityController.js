const catchAsync = require('../utils/catchAsync');
const utilityService = require('../services/utilityService');

/**
 * @desc    Create a new utility
 * @route   POST /api/utilities
 * @access  Private
 */
const createUtility = catchAsync(async (req, res) => {
  // Add createdBy, updatedBy, and accountId from the authenticated user
  const utilityData = {
    ...req.body,
    accountId: req.user.accountId,
    createdBy: req.user.id,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await utilityService.createUtility(utilityData);
  res.status(status).json(payload);
});

/**
 * @desc    Get all utilities with optional filtering and pagination
 * @route   GET /api/utilities
 * @access  Private
 */
const getUtilities = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    propertyId,
    utilityType,
    isActive
  } = req.query;
  
  const { status, payload } = await utilityService.getAllUtilities({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    propertyId,
    utilityType,
    isActive
  });
  
  res.status(status).json(payload);
});

/**
 * @desc    Get a single utility by ID
 * @route   GET /api/utilities/:id
 * @access  Private
 */
const getUtilityById = catchAsync(async (req, res) => {
  const { status, payload } = await utilityService.getUtilityById(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing utility
 * @route   PUT /api/utilities/:id
 * @access  Private
 */
const updateUtility = catchAsync(async (req, res) => {
  // Add updatedBy from the authenticated user
  const updateData = {
    ...req.body,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await utilityService.updateUtility(
    req.params.id, 
    updateData
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete a utility
 * @route   DELETE /api/utilities/:id
 * @access  Private/Admin
 */
const deleteUtility = catchAsync(async (req, res) => {
  const { status, payload } = await utilityService.deleteUtility(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get utilities by property ID
 * @route   GET /api/utilities/properties/:propertyId
 * @access  Private
 */
const getUtilitiesByPropertyId = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, isActive } = req.query;
  const { status, payload } = await utilityService.getUtilitiesByPropertyId(
    req.params.propertyId, 
    { 
      page: parseInt(page), 
      limit: parseInt(limit),
      isActive: isActive ? isActive === 'true' : undefined
    }
  );
  res.status(status).json(payload);
});

module.exports = {
  createUtility,
  getUtilities,
  getUtilityById,
  updateUtility,
  deleteUtility,
  getUtilitiesByPropertyId
};
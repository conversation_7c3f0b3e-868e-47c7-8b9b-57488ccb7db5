const { getStripePublishableKeyForAccount } = require('../services/stripeService');
const logger = require('../utils/logger');

/**
 * Get Stripe publishable key for an account
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getStripePublishableKey = async (req, res) => {
  try {
    const accountId = req.query.accountId || req.user.accountId;
    
    if (!accountId) {
      return res.status(400).json({
        success: false,
        message: 'Account ID is required'
      });
    }

    const publishableKey = await getStripePublishableKeyForAccount(accountId);
    
    return res.status(200).json({
      success: true,
      publishableKey
    });
  } catch (error) {
    logger.error('Error getting Stripe publishable key:', {
      error: error.message,
      stack: error.stack
    });
    
    // Handle specific error cases
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        message: `Account not found: ${error.message}`
      });
    }
    
    if (error.message.includes('does not have a Stripe publishable key configured')) {
      return res.status(400).json({
        success: false,
        message: 'Stripe not configured for this account',
        error: 'Missing Stripe publishable key'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to get Stripe publishable key',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update Stripe configuration for an account
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateStripeConfig = async (req, res) => {
  try {
    const { Account } = require('../models');
    const accountId = req.params.accountId;
    const { stripeSecretKey, stripePublishableKey } = req.body;
    
    if (!accountId) {
      return res.status(400).json({
        success: false,
        message: 'Account ID is required'
      });
    }

    // Update account with new Stripe keys
    const [updated] = await Account.update({
      stripeSecretKey,
      stripePublishableKey
    }, {
      where: { id: accountId }
    });
    
    if (!updated) {
      return res.status(404).json({
        success: false,
        message: 'Account not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      message: 'Stripe configuration updated successfully'
    });
  } catch (error) {
    logger.error('Error updating Stripe configuration:', {
      error: error.message,
      stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Failed to update Stripe configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getStripePublishableKey,
  updateStripeConfig
};

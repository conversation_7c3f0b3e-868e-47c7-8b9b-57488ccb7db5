const catchAsync = require('../utils/catchAsync');
const contactService = require('../services/contactService');

/**
 * @desc    Create a new contact
 * @route   POST /api/contacts
 * @access  Private
 */
const createContact = catchAsync(async (req, res) => {
  // Add createdBy and updatedBy from the authenticated user
  const contactData = {
    ...req.body,
    createdBy: req.user.id,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await contactService.createContact(contactData);
  res.status(status).json(payload);
});

/**
 * @desc    Get all contacts with optional filtering and pagination
 * @route   GET /api/contacts
 * @access  Private
 */
const getContacts = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    accountId,
    propertyId,
    type,
    isActive
  } = req.query;
  
  const { status, payload } = await contactService.getAllContacts({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    accountId,
    propertyId,
    type,
    isActive
  });
  
  res.status(status).json(payload);
});

/**
 * @desc    Get a single contact by ID
 * @route   GET /api/contacts/:id
 * @access  Private
 */
const getContactById = catchAsync(async (req, res) => {
  const { status, payload } = await contactService.getContactById(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing contact
 * @route   PUT /api/contacts/:id
 * @access  Private
 */
const updateContact = catchAsync(async (req, res) => {
  // Add updatedBy from the authenticated user
  const updateData = {
    ...req.body,
    updatedBy: req.user.id
  };
  
  const { status, payload } = await contactService.updateContact(
    req.params.id, 
    updateData
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete a contact
 * @route   DELETE /api/contacts/:id
 * @access  Private/Admin
 */
const deleteContact = catchAsync(async (req, res) => {
  const { status, payload } = await contactService.deleteContact(req.params.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get contacts by property ID
 * @route   GET /api/properties/:propertyId/contacts
 * @access  Private
 */
const getContactsByPropertyId = catchAsync(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const { status, payload } = await contactService.getContactsByPropertyId(
    req.params.propertyId, 
    { page: parseInt(page), limit: parseInt(limit) }
  );
  res.status(status).json(payload);
});

module.exports = {
  createContact,
  getContacts,
  getContactById,
  updateContact,
  deleteContact,
  getContactsByPropertyId
};
const catchAsync = require('../utils/catchAsync');
const authService = require('../services/authService');
const logger = require('../utils/logger');

/**
 * Register a new landlord
 * @route POST /api/auth/register
 */
const register = catchAsync(async (req, res) => {
  const { status, payload } = await authService.register(req.body);
  res.status(status).json(payload);
});

/**
 * User login
 * @route POST /api/auth/login
 */
const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const { status, payload } = await authService.login(email, password);
  res.status(status).json(payload);
});

/**
 * Refresh access token
 * @route POST /api/auth/refresh-token
 */
const refreshToken = catchAsync(async (req, res) => {
  const authHeader = req.header('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No refresh token provided. Please provide a token in the Authorization header as: Bearer <token>',
    });
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token format. Please use: Bearer <token>',
    });
  }

  const { status, payload } = await authService.refreshToken(token);
  res.status(status).json(payload);
});

/**
 * User logout
 * @route POST /api/auth/logout
 */
const logout = catchAsync(async (req, res) => {
  const authHeader = req.header('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No access token provided',
    });
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token format',
    });
  }

  // Verify the token to get expiration
  const { verifyToken } = require('../utils/token');
  const decoded = verifyToken(token);
  
  if (!decoded) {
    return res.status(401).json({
      success: false,
      message: 'Invalid or expired token',
    });
  }

  // Add token to blacklist
  const tokenBlacklist = require('../services/tokenBlacklist');
  await tokenBlacklist.addToBlacklist(token, new Date(decoded.exp * 1000));

  res.status(200).json({
    success: true,
    message: 'Successfully logged out',
  });
});

/**
 * Request password reset
 * @route POST /api/auth/request-password-reset
 */
const requestPasswordReset = catchAsync(async (req, res) => {
  const { email } = req.body;
  const { status, payload } = await authService.requestPasswordReset(email);
  res.status(status).json(payload);
});

/**
 * Reset password
 * @route POST /api/auth/reset-password
 */
const resetPassword = catchAsync(async (req, res) => {
  const { token, newPassword } = req.body;
  const { status, payload } = await authService.resetPassword(token, newPassword);
  res.status(status).json(payload);
});

/**
 * Verify email
 * @route GET /api/auth/verify-email
 */
const verifyEmail = catchAsync(async (req, res) => {
  const { token } = req.query;
  if (!token) {
    return res.status(400).json({
      success: false,
      message: 'Verification token is required',
    });
  }
  
  const { status, payload } = await authService.verifyEmail(token);
  
  // Check if this is an API request (has Accept: application/json header)
  const isApiRequest = req.get('Accept')?.includes('application/json');
  
  if (isApiRequest) {
    // For API requests, return JSON response
    return res.status(status).json(payload);
  }
  
  // For browser requests, redirect to frontend with appropriate query params
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  const redirectUrl = new URL(`${frontendUrl}/auth/email-verified`);
  
  if (status === 200 && payload.success) {
    redirectUrl.searchParams.append('success', 'true');
  } else {
    redirectUrl.searchParams.append('success', 'false');
    if (payload.message) {
      redirectUrl.searchParams.append('error', payload.message);
    }
    if (payload.code) {
      redirectUrl.searchParams.append('code', payload.code);
    }
  }
  
  return res.redirect(redirectUrl.toString());
});

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
};
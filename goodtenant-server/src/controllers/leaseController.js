const catchAsync = require('../utils/catchAsync');
const LeaseService = require('../services/leaseService');

/**
 * @desc    Create a new lease
 * @route   POST /api/leases
 * @access  Private/Landlord
 */
const createLease = catchAsync(async (req, res) => {
  const { status, payload } = await LeaseService.createLease(req.body, req.user.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get all leases with optional pagination and search
 * @route   GET /api/leases
 * @access  Private
 */
const getLeases = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '', status, propertyId } = req.query;
  const { status: responseStatus, payload } = await LeaseService.getAllLeases({
    page: parseInt(page),
    limit: parseInt(limit),
    search,
    status,
    propertyId,
    user: req.user
  });
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get a single lease by ID
 * @route   GET /api/leases/:id
 * @access  Private
 */
const getLeaseById = catchAsync(async (req, res) => {
  const { status, payload } = await LeaseService.getLeaseById(
    req.params.id, 
    req.user
  );
  res.status(status).json(payload);
});

/**
 * @desc    Update an existing lease
 * @route   PUT /api/leases/:id
 * @access  Private/Landlord
 */
const updateLease = catchAsync(async (req, res) => {
  const { status, payload } = await LeaseService.updateLease(
    req.params.id,
    req.body,
    req.user
  );
  res.status(status).json(payload);
});

/**
 * @desc    Delete a lease
 * @route   DELETE /api/leases/:id
 * @access  Private/Landlord
 */
const deleteLease = catchAsync(async (req, res) => {
  const { status, payload } = await LeaseService.deleteLease(
    req.params.id,
    req.user
  );
  res.status(status).json(payload);
});

/**
 * @desc    Generate lease document content
 * @route   GET /api/public/leases/:id/document/:token?
 * @access  Public (with token validation) or Private (with auth)
 */
const generateLeaseDocument = catchAsync(async (req, res) => {
  const { token } = req.params;
  
  // If no token is provided, require authentication
  if (!token && !req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required or valid token must be provided'
    });
  }
  
  const { status, payload } = await LeaseService.generateLeaseDocumentContent(
    req.params.id,
    token || null
  );
  
  res.status(status).json(payload);
});

module.exports = {
  createLease,
  getLeases,
  getLeaseById,
  updateLease,
  deleteLease,
  generateLeaseDocument
};

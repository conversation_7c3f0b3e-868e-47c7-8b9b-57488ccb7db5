const catchAsync = require('../utils/catchAsync');
const paymentService = require('../services/paymentService');
const { Payment, User, AccountUser } = require('../models');
const { getStripeInstanceForAccount } = require('../services/stripeService');
const logger = require('../utils/logger');
const eventEmitter = require('../utils/eventEmitter');

// Only validate webhook secret and client URL
// We no longer need a default STRIPE_SECRET_KEY since each account has its own
if (!process.env.STRIPE_WEBHOOK_SECRET) {
  throw new Error('STRIPE_WEBHOOK_SECRET is not defined in environment variables');
}
if (!process.env.CLIENT_URL) {
  throw new Error('CLIENT_URL is not defined in environment variables');
}

/**
 * @desc    Create a new Stripe Checkout session
 * @route   POST /api/payments/checkout
 * @access  Private
 */
const createCheckoutSession = catchAsync(async (req, res) => {
  const { amount, payerId, ...paymentData } = req.body;
  
  if (!amount) {
    return res.status(400).json({
      success: false,
      message: 'Amount is required',
    });
  }
  
  // If payerId is provided, verify they belong to the same account
  if (payerId && payerId !== req.user.id) {
    const payer = await User.findByPk(payerId);
    if (!payer || payer.accountId !== req.user.accountId) {
      return res.status(403).json({
        success: false,
        message: 'Invalid payer selected',
      });
    }
  }
  
  const checkoutData = {
    ...paymentData,
    amount: amount,
    payerId: payerId || req.user.id, // Use provided payerId or current user
    accountId: req.user.accountId, // Always use the authenticated user's account
    createdBy: req.user.id,
    currency: paymentData.currency || 'usd',
    metadata: {
      ...(paymentData.metadata || {}),
      isSimplifiedFlow: true,
    },
  };
  
  const { status, payload } = await paymentService.createCheckoutSession(checkoutData, req.user.id);
  res.status(status).json(payload);
});

/**
 * @desc    Get payment by checkout session ID
 * @route   GET /api/payments/session/:sessionId
 * @access  Private
 */
const getPaymentBySession = catchAsync(async (req, res) => {
  const { sessionId } = req.params;
  
  const { status, payload } = await paymentService.getPaymentBySessionId(sessionId);
  
  // Check if the user has permission to view this payment
  if (payload.success && req.user.role !== 'admin' && 
      payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to view this payment'
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Get all payments with optional filtering and pagination
 * @route   GET /api/payments
 * @access  Private
 */
const getPayments = catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    status,
    paymentMethod,
    startDate,
    endDate,
    payerId,
    receiverId
  } = req.query;
  
  // If user is not admin, only show payments for their account
  const accountId = req.user.role === 'admin' ? null : req.user.accountId;
  
  const { status: responseStatus, payload } = await paymentService.getAllPayments({ 
    page: parseInt(page), 
    limit: parseInt(limit), 
    search,
    status,
    paymentMethod,
    startDate,
    endDate,
    accountId,
    payerId,
    receiverId
  });
  
  res.status(responseStatus).json(payload);
});

/**
 * @desc    Get a single payment by ID
 * @route   GET /api/payments/:id
 * @access  Private
 */
const getPaymentById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { status, payload } = await paymentService.getPaymentById(id);
  
  // Check if the user has permission to view this payment
  if (payload.success && req.user.role !== 'admin' && 
      payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to view this payment'
    });
  }
  
  res.status(status).json(payload);
});

/**
 * @desc    Update a payment
 * @route   PUT /api/payments/:id
 * @access  Private
 */
const updatePayment = catchAsync(async (req, res) => {
  const { id } = req.params;
  
  // First, verify the payment exists and user has permission
  const existingPayment = await paymentService.getPaymentById(id);
  
  if (!existingPayment.payload.success) {
    return res.status(existingPayment.status).json(existingPayment.payload);
  }
  
  // Check if user has permission (admin or owner of the account)
  if (req.user.role !== 'admin' && 
      existingPayment.payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to update this payment'
    });
  }
  
  // Add updatedBy reference
  const updateData = {
    ...req.body,
    updatedBy: req.user.id
  };
  
  const { status: updateStatus, payload } = await paymentService.updatePayment(
    id, 
    updateData, 
    req.user.id
  );
  
  res.status(updateStatus).json(payload);
});

/**
 * @desc    Delete a payment (soft delete)
 * @route   DELETE /api/payments/:id
 * @access  Private
 */
const deletePayment = catchAsync(async (req, res) => {
  const { id } = req.params;
  
  // First, verify the payment exists and user has permission
  const existingPayment = await paymentService.getPaymentById(id);
  
  if (!existingPayment.payload.success) {
    return res.status(existingPayment.status).json(existingPayment.payload);
  }
  
  // Check if user has permission (admin or owner of the account)
  if (req.user.role !== 'admin' && 
      existingPayment.payload.data.accountId !== req.user.accountId) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to delete this payment'
    });
  }
  
  const { status, payload } = await paymentService.deletePayment(id);
  res.status(status).json(payload);
});

/**
 * @desc    Handle Stripe webhook
 * @route   POST /api/stripe/webhook
 * @access  Public
 */
const handleWebhook = catchAsync(async (req, res) => {
  try {
    const sig = req.headers['stripe-signature'];
    
    if (!sig) {
      logger.error('No Stripe signature found in headers');
      return res.status(400).json({ success: false, message: 'No Stripe signature' });
    }

    // Get the raw body from the request
    const rawBody = req.rawBody;
    
    if (!rawBody) {
      logger.error('No raw body available for signature verification');
      return res.status(400).json({ success: false, message: 'No request body' });
    }
    
    logger.info('Verifying webhook signature with raw body length:', rawBody.length);
    logger.debug('Request headers:', JSON.stringify(req.headers, null, 2));
    
    // Log the first 200 chars of the raw body for debugging
    if (rawBody) {
      logger.debug('Raw body start:', rawBody.substring(0, 200) + (rawBody.length > 200 ? '...' : ''));
    }
    
    try {
      // For webhook signature verification, we still use a single webhook secret
      // We don't know which account it belongs to yet until we verify and parse the event
      // Create a minimal stripe instance just for webhook verification
      const stripeForVerification = require('stripe')(process.env.STRIPE_WEBHOOK_VERIFICATION_KEY || process.env.STRIPE_SECRET_KEY);
      
      // Verify the webhook signature
      const event = stripeForVerification.webhooks.constructEvent(
        rawBody,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET
      );
      
      logger.info(`Processing webhook event: ${event.type}`);
      
      // Once verified, extract account ID from the event metadata
      const metadata = event.data?.object?.metadata || {};
      logger.debug('Event metadata:', JSON.stringify(metadata, null, 2));
      
      // Handle the event with the payment service, which will use account-specific Stripe instance
      const { status, payload } = await paymentService.handleStripeWebhook(event);
      
      // Return a successful response to acknowledge receipt of the event
      return res.status(status || 200).json(payload || { received: true });
      
    } catch (err) {
      logger.error(`Webhook Error: ${err.message}`, {
        error: err.message,
        stack: err.stack,
        headers: Object.keys(req.headers).filter(k => k.toLowerCase().startsWith('stripe-')),
        rawBodyLength: rawBody ? rawBody.length : 0,
        rawBodyStart: rawBody ? rawBody.substring(0, 100) : ''
      });
      
      return res.status(400).json({ 
        success: false, 
        message: `Webhook Error: ${err.message}`,
        error: process.env.NODE_ENV === 'development' ? err.stack : undefined
      });
    }
  } catch (error) {
    logger.error('Unexpected error in webhook handler:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Handle successful payment redirect
 * @route   GET /api/payments/success
 * @access  Private
 */
const handleSuccess = catchAsync(async (req, res) => {
  const { session_id: sessionId } = req.query;
  
  if (!sessionId) {
    return res.redirect(`${process.env.CLIENT_URL}/payments?status=error`);
  }
  
  try {
    // Verify the session and payment
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (!session || session.payment_status !== 'paid') {
      return res.redirect(`${process.env.CLIENT_URL}/payments?status=error`);
    }
    
    // Get the payment details
    const { status, payload } = await paymentService.getPaymentBySessionId(sessionId);
    
    if (!payload.success || !payload.data) {
      return res.redirect(`${process.env.CLIENT_URL}/payments?status=error`);
    }
    
    // Redirect to success page with payment ID
    res.redirect(`${process.env.CLIENT_URL}/payments/success?paymentId=${payload.data.id}`);
    
  } catch (error) {
    logger.error('Error handling success redirect:', error);
    res.redirect(`${process.env.CLIENT_URL}/payments?status=error`);
  }
});

/**
 * @desc    Handle cancelled payment
 * @route   GET /api/payments/cancel
 * @access  Private
 */
const handleCancel = catchAsync(async (req, res) => {
  const { payment_id: paymentId } = req.query;
  
  if (paymentId) {
    // Optionally update the payment status to cancelled
    try {
      await paymentService.updatePayment(paymentId, {
        status: 'cancelled',
        updatedBy: req.user.id
      });
    } catch (error) {
      logger.error('Error updating cancelled payment:', error);
    }
  }
  
  res.redirect(`${process.env.CLIENT_URL}/payments?status=cancelled`);
});

/**
 * @desc    Create a manual payment
 * @route   POST /api/payments
 * @access  Private
 */
const createPayment = catchAsync(async (req, res) => {
  const { amount, payerId, paymentMethod, notes, metadata } = req.body;
  
  if (!amount || !payerId || !paymentMethod) {
    return res.status(400).json({
      success: false,
      message: 'Amount, payer, and payment method are required',
    });
  }

  // Validate amount is a positive number
  const paymentAmount = parseFloat(amount);
  if (isNaN(paymentAmount) || paymentAmount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Please enter a valid payment amount',
    });
  }

  try {
    // Verify the payer exists
    const payer = await User.findByPk(payerId, {
      include: [{
        model: AccountUser,
        as: 'accountUsers',
        attributes: ['accountId'],
        required: true
      }]
    });
    
    if (!payer) {
      return res.status(404).json({
        success: false,
        message: 'Payer not found',
      });
    }
    
    // Get the account IDs the payer is associated with
    const payerAccountIds = payer.accountUsers.map(au => au.accountId);
    
    // For non-admin users, verify the payer is in the same account
    if (req.user.role !== 'admin' && !payerAccountIds.includes(req.user.accountId)) {
      return res.status(403).json({
        success: false,
        message: 'You can only create payments for users in your account',
      });
    }

    const paymentData = {
      amount: paymentAmount,
      payerId,
      paymentMethod,
      notes,
      metadata: {
        ...(metadata || {}),
        createdBy: req.user.id,
        source: 'manual_payment',
      },
      paymentDate: new Date().toISOString(),
      accountId: req.user.accountId, // Set from the authenticated user's account
    };

    eventEmitter.emit('payment.created', { payment: paymentData, createdById: req.user.id });

    const { status, payload } = await paymentService.createPayment(paymentData, req.user.id);
    res.status(status).json(payload);
  } catch (error) {
    console.error('Error in createPayment controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
});

module.exports = {
  createCheckoutSession,
  getPaymentBySession,
  getPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment,
  handleWebhook,
  handleSuccess,
  handleCancel
};

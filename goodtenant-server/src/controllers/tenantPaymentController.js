const catchAsync = require('../utils/catchAsync');
const tenantPaymentService = require('../services/tenantPaymentService');
const logger = require('../utils/logger');

/**
 * @desc    Get payment status for a specific tenant (Admin/Manager)
 * @route   GET /api/tenant-payment/tenant/:tenantId
 * @access  Private/Admin
 */
const getTenantPaymentStatus = catchAsync(async (req, res) => {
  const { tenantId } = req.params;
  const { leaseId, asOfDate } = req.query;
  
  logger.info(`Admin fetching payment status for tenant ${tenantId}`);
  
  const { status, payload } = await tenantPaymentService.getTenantPaymentStatus(
    tenantId,
    {
      ...(leaseId && { leaseId }),
      ...(asOfDate && { asOfDate: new Date(asOfDate) })
    }
  );
  
  res.status(status).json(payload);
});

/**
 * @desc    Get payment status for the current user (tenant)
 * @route   GET /api/tenant-payment/status
 * @access  Private
 */
const getMyPaymentStatus = catchAsync(async (req, res) => {
  const { id: tenantId } = req.user;
  const { leaseId, asOfDate } = req.query;
  
  logger.info(`User ${tenantId} fetching their payment status`);
  
  const { status, payload } = await tenantPaymentService.getTenantPaymentStatus(
    tenantId,
    {
      ...(leaseId && { leaseId }),
      ...(asOfDate && { asOfDate: new Date(asOfDate) })
    }
  );
  
  res.status(status).json(payload);
});

module.exports = {
  getTenantPaymentStatus,
  getMyPaymentStatus
};

{"content": [{"text": "Rental Property Addendum for Service Animals Only", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "<strong>Rental Property Addendum for Service Animals Only</strong>"}, {"text": "This Service Animal Addendum is attached to and becomes part of the lease agreement between landlord.fullName (hereinafter referred to as \"Landlord\") and tenant.fullName(hereinafter referred to as \"Tenant.\" for the Property located at property.address (hereinafter referred to as the \"Property\").", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "This <strong>Service Animal Addendum </strong>is attached to and becomes part of the lease agreement between <span data-type=\"variable\" class=\"variable-tag\" id=\"landlord.fullName\">{{landlord.fullName}}</span><strong> </strong>(hereinafter referred to as \"<strong>Landlord</strong>\") and <span data-type=\"variable\" class=\"variable-tag\" id=\"tenant.fullName\">{{tenant.fullName}}</span>(hereinafter referred to as \"<strong>Tenant</strong>.\" for the Property located at <span data-type=\"variable\" class=\"variable-tag\" id=\"property.address\">{{property.address}}</span><strong> </strong>(hereinafter referred to as the \"<strong>Property</strong>\")."}, {"text": "1. Purpose: This addendum clarifies the landlord’s policy regarding animals on the Property, permitting only service animals as defined by law.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "1. <strong>Purpose</strong>: This addendum clarifies the landlord’s policy regarding animals on the Property, permitting only service animals as defined by law."}, {"text": "2. Effective Date: This addendum is effective from lease.startDate to lease.endDate and will be subject to renewal or review at the end of this period as part of the lease renewal process or as otherwise agreed in writing by both parties. ", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "2. <strong>Effective Date</strong>: This addendum is effective from <span data-type=\"variable\" class=\"variable-tag\" id=\"lease.startDate\">{{lease.startDate}}</span><strong> </strong>to <span data-type=\"variable\" class=\"variable-tag\" id=\"lease.endDate\">{{lease.endDate}}</span><strong> </strong>and will be subject to renewal or review at the end of this period as part of the lease renewal process or as otherwise agreed in writing by both parties. "}, {"text": "3. Service Animals Defined: For the purposes of this addendum, \"service animal\" refers specifically to animals that are individually trained to perform tasks for individuals with disabilities or medical conditions. Only animals meeting this legal definition are permitted on the Property.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "3. <strong>Service Animals Defined</strong>: For the purposes of this addendum, \"service animal\" refers specifically to animals that are individually trained to perform tasks for individuals with disabilities or medical conditions. Only animals meeting this legal definition are permitted on the Property."}, {"text": "4. Prohibition of Other Animals: Pets and emotional support animals (ESAs) are strictly prohibited unless required by applicable law. This includes any animal kept for comfort, companionship, or non-service-related purposes.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "4. <strong>Prohibition of Other Animals</strong>: Pets and emotional support animals (ESAs) are strictly prohibited unless required by applicable law. This includes any animal kept for comfort, companionship, or non-service-related purposes."}, {"text": "5. Documentation Requirement: If a service animal is required, the Tenant agrees to provide documentation or verification of the animal’s status as a service animal upon request, consistent with applicable laws.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "5. <strong>Documentation Requirement</strong>: If a service animal is required, the Tenant agrees to provide documentation or verification of the animal’s status as a service animal upon request, consistent with applicable laws."}, {"text": "6. Responsibilities of Tenant: The Tenant is responsible for: o Keeping the service animal in compliance with local ordinances, including registration and vaccination requirements.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "6. <strong>Responsibilities of Tenant</strong>: The Tenant is responsible for: o Keeping the service animal in compliance with local ordinances, including registration and vaccination requirements."}, {"text": "o Ensuring that the service animal is well-behaved and does not create disturbances or pose a threat to other tenants, guests, or Property. o Properly cleaning up after the service animal and maintaining the cleanliness of the Property.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": false}, {"text": "7. Pest Control: Tenant is responsible for any pest control costs associated with the presence of the service animal, should issues such as fleas or ticks arise.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "7. <strong>Pest Control</strong>: Tenant is responsible for any pest control costs associated with the presence of the service animal, should issues such as fleas or ticks arise."}, {"text": "8. Insurance Coverage: Tenant is advised to consult their renter’s insurance policy regarding coverage for incidents involving the service animal, such as accidental injury or Property damage to others.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "8. <strong>Insurance Coverage</strong>: Tenant is advised to consult their renter’s insurance policy regarding coverage for incidents involving the service animal, such as accidental injury or Property damage to others."}, {"text": "9. Termination: Any violation of the terms of this addendum or failure to comply with laws regarding service animals may be considered a breach of the lease agreement and may result in termination of the lease.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "9. <strong>Termination</strong>: Any violation of the terms of this addendum or failure to comply with laws regarding service animals may be considered a breach of the lease agreement and may result in termination of the lease."}, {"text": "10. Indemnity: Tenant agrees to indemnify, defend, and hold Landlord harmless from and against all claims, damages, losses, or expenses arising from the actions of the service animal, including but not limited to any injury to persons or Property.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "10. <strong>Indemnity</strong>: Tenant agrees to indemnify, defend, and hold Landlord harmless from and against all claims, damages, losses, or expenses arising from the actions of the service animal, including but not limited to any injury to persons or Property."}, {"text": "11. Fees and Security Deposit:", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "11. <strong>Fees and Security Deposit:</strong>"}, {"text": "No Pet Fee: As a service animal, no pet fee or additional pet deposit will be charged for the service animal on the Property.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "<strong>No Pet Fee: </strong>As a service animal, no pet fee or additional pet deposit will be charged for the service animal on the Property."}, {"text": "Use of Security Deposit for Damages: Although no pet-specific fees apply, any damage directly attributable to the service animal that affects the condition of the Property will be covered by the", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": true, "formattedContent": "<strong>Use of Security Deposit for Damages: </strong>Although no pet-specific fees apply, any damage directly attributable to the service animal that affects the condition of the Property will be covered by the"}, {"text": "security deposit as per the lease agreement. Should the service animal cause damages beyond normal wear and tear, the cost of repairs or cleaning will be deducted from the security deposit, following an itemized list provided to the Tenant at the lease’s end.", "type": "paragraph", "textAlign": "left", "variables": [], "hasFormatting": false}], "version": "1.0"}
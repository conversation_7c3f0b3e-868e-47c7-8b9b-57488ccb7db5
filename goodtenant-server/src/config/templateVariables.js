/**
 * System variables that can be used in templates
 * Each variable has a key, description, and optional validation
 */
const SYSTEM_VARIABLES = {
  // Tenant information
  'tenant.fullName': {
    description: 'Full name of the tenant',
    required: true,
    type: 'string'
  },

  // Landlord information
  'landlord.fullName': {
    description: 'Full name of the landlord',
    required: true,
    type: 'string'
  },
 
  // Property information
  'property.address': {
    description: 'Full address of the rental property',
    required: true,
    type: 'string'
  },
 
  // Lease information
  'lease.startDate': {
    description: 'Lease start date (YYYY-MM-DD)',
    required: true,
    type: 'date'
  },
  'lease.endDate': {
    description: 'Lease end date (YYYY-MM-DD)',
    required: true,
    type: 'date'
  },

  'lease.dueDate': {
    description: 'Lease due date (YYYY-MM-DD)',
    required: true,
    type: 'date'
  },
  'lease.depositAmount': {
    description: 'Security deposit amount',
    required: true,
    type: 'number'
  },
  'lease.monthlyRent': {
    description: 'Monthly rent amount',
    required: true,
    type: 'number'
  },
  'lease.lateFee': {
    description: 'Late fee amount',
    required: true,
    type: 'number'
  },
  'lease.nsfFee': {
    description: 'NSF fee amount',
    required: true,
    type: 'number'
  },
  'lease.earlyTerminationFee': {
    description: 'Early termination fee amount',
    required: true,
    type: 'number'
  },
  'lease.numberOfPeople': {
    description: 'Number of people living in the property',
    required: true,
    type: 'number'
  },
  
  // ====== Pets ======
  'pets.count': {
    description: 'Number of pets',
    required: false,
    type: 'number',
    default: 0
  },

  // Pet 1
  'pet1.name': { description: 'Name of the first pet', required: false, type: 'string' },
  'pet1.type': { description: 'Type of the first pet', required: false, type: 'string' },
  'pet1.breed': { description: 'Breed of the first pet', required: false, type: 'string' },
  'pet1.sex': { description: 'Sex of the first pet', required: false, type: 'string' },
  'pet1.age': { description: 'Age of the first pet', required: false, type: 'number' },
  'pet1.weight': { description: 'Weight of the first pet', required: false, type: 'number' },
  'pet1.isEmotionalSupport': { description: 'Is the first pet an emotional support animal? (Yes/No)', required: false, type: 'string' },
  'pet1.hasVaccinations': { description: 'Does the first pet have vaccinations? (Yes/No)', required: false, type: 'string' },

  // Pet 2
  'pet2.name': { description: 'Name of the second pet', required: false, type: 'string' },
  'pet2.type': { description: 'Type of the second pet', required: false, type: 'string' },
  'pet2.breed': { description: 'Breed of the second pet', required: false, type: 'string' },
  'pet2.sex': { description: 'Sex of the second pet', required: false, type: 'string' },
  'pet2.age': { description: 'Age of the second pet', required: false, type: 'number' },
  'pet2.weight': { description: 'Weight of the second pet', required: false, type: 'number' },
  'pet2.isEmotionalSupport': { description: 'Is the second pet an emotional support animal? (Yes/No)', required: false, type: 'string' },
  'pet2.hasVaccinations': { description: 'Does the second pet have vaccinations? (Yes/No)', required: false, type: 'string' },

  // Pet 3
  'pet3.name': { description: 'Name of the third pet', required: false, type: 'string' },
  'pet3.type': { description: 'Type of the third pet', required: false, type: 'string' },
  'pet3.breed': { description: 'Breed of the third pet', required: false, type: 'string' },
  'pet3.sex': { description: 'Sex of the third pet', required: false, type: 'string' },
  'pet3.age': { description: 'Age of the third pet', required: false, type: 'number' },
  'pet3.weight': { description: 'Weight of the third pet', required: false, type: 'number' },
  'pet3.isEmotionalSupport': { description: 'Is the third pet an emotional support animal? (Yes/No)', required: false, type: 'string' },
  'pet3.hasVaccinations': { description: 'Does the third pet have vaccinations? (Yes/No)', required: false, type: 'string' },



 
  
  // Vehicles count
  'vehicles.count': { description: 'Number of vehicles', required: false, type: 'number' },

  // Vehicle 1
  'vehicle1.make': { description: 'Make of the first vehicle', required: false, type: 'string' },
  'vehicle1.model': { description: 'Model of the first vehicle', required: false, type: 'string' },
  'vehicle1.year': { description: 'Year of the first vehicle', required: false, type: 'number' },
  'vehicle1.licensePlate': { description: 'License plate of the first vehicle', required: false, type: 'string' },
  'vehicle1.color': { description: 'Color of the first vehicle', required: false, type: 'string' },
 

  // Vehicle 2
  'vehicle2.make': { description: 'Make of the second vehicle', required: false, type: 'string' },
  'vehicle2.model': { description: 'Model of the second vehicle', required: false, type: 'string' },
  'vehicle2.year': { description: 'Year of the second vehicle', required: false, type: 'number' },
  'vehicle2.licensePlate': { description: 'License plate of the second vehicle', required: false, type: 'string' },
  'vehicle2.color': { description: 'Color of the second vehicle', required: false, type: 'string' },
 

  // Vehicle 3
  'vehicle3.make': { description: 'Make of the third vehicle', required: false, type: 'string' },
  'vehicle3.model': { description: 'Model of the third vehicle', required: false, type: 'string' },
  'vehicle3.year': { description: 'Year of the third vehicle', required: false, type: 'number' },
  'vehicle3.licensePlate': { description: 'License plate of the third vehicle', required: false, type: 'string' },
  'vehicle3.color': { description: 'Color of the third vehicle', required: false, type: 'string' },


  // Current date
  'currentDate': {
    description: 'Current date (formatted as full date)',
    required: false,
    type: 'date',
    default: () => new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
};

module.exports = {
  SYSTEM_VARIABLES
};

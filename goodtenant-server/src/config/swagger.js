const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const path = require('path');
const fs = require('fs');

// Base configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'GoodTenant API',
      version: '1.0.0',
      description: 'API for GoodTenant - Property Management System',
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        }
      },
      schemas: {}
    },
    security: [{
      bearerAuth: []
    }]
  },
  // This will pick up all YAML files in the docs directory
  apis: [
    path.join(__dirname, '../docs/**/*.yaml'),
    path.join(__dirname, '../docs/**/*.yml')
  ]
};

// Generate the Swagger specification
const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Function to setup our docs
const setupSwagger = (app) => {
  // Swagger page
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
  
  // Docs in JSON format
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });

  console.log(`📚 Swagger UI available at /api-docs`);
};

module.exports = setupSwagger;

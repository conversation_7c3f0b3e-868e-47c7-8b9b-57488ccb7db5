const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Configuration for database sync behavior
const DB_SYNC_OPTIONS = {
  // Set to true to force sync (drops all tables and recreates them)
  force: process.env.DB_FORCE_SYNC === 'true',
  // Set to true to alter tables to match models (adds new columns, etc.)
  alter: process.env.DB_ALTER_SYNC === 'true',
  // Set to false to disable sync completely
  syncEnabled: process.env.DB_SYNC_ENABLED !== 'false'
};

// Log sync options for debugging
console.log('DB Sync Options:', DB_SYNC_OPTIONS);

// Log environment variables for debugging
console.log('DB Configuration:', {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  username: process.env.DB_USER
});

// Initialize Sequelize with environment variables
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: (msg) => logger.debug(msg),
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    retry: {
      max: 3,
      timeout: 30000
    }
  }
);

// Test the database connection
const connectDB = async () => {
  try {
    console.log('Attempting to connect to database...');
    await sequelize.authenticate();
    logger.info('Database connection has been established successfully.');
    
    // Test the connection with a simple query
    const [results] = await sequelize.query('SELECT 1+1 as result');
    logger.info('Database test query result:', results[0]);
    
        // Only sync if explicitly enabled
    if (process.env.NODE_ENV === 'development' && DB_SYNC_OPTIONS.syncEnabled) {
      logger.info('Syncing database models...');
      
      // Import models to control sync order
      const models = require('../models');
      
      try {
        // If force sync is enabled, drop all tables and recreate
        if (DB_SYNC_OPTIONS.force) {
          logger.warn('Force sync enabled - this will drop all tables!');
          await sequelize.sync({ force: true });
          logger.info('Database force sync completed');
        } 
        // If alter is enabled, alter existing tables to match models
        else if (DB_SYNC_OPTIONS.alter) {
          logger.info('Altering tables to match models...');
          await sequelize.sync({ alter: true });
          logger.info('Database alter sync completed');
        }
        // Otherwise just check if tables exist, create them if they don't
        else {
          logger.info('Checking if database tables exist...');
          await sequelize.sync();
          logger.info('Database tables verified');
        }
      } catch (error) {
        logger.error('Error syncing database:', error);
        throw error;
      }
    }
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    console.error('Connection error details:', {
      message: error.message,
      code: error.parent?.code,
      errno: error.parent?.errno,
      sqlState: error.parent?.sqlState,
      sqlMessage: error.parent?.sqlMessage
    });
    process.exit(1);
  }
};

module.exports = {
  sequelize,
  connectDB,
  Sequelize
};

const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const config = {
  // Server configuration
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Client URL for generating links
  clientUrl: process.env.CLIENT_URL || 'http://localhost:3000',
  
  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },
  
  // Mailgun configuration for emails
  mailgun: {
    apiKey: process.env.MAILGUN_API_KEY,
    domain: process.env.MAILGUN_DOMAIN,
    defaultFrom: process.env.MAILGUN_FROM_EMAIL || '<EMAIL>',
    templates: {
      tenantInvitation: process.env.MAILGUN_TENANT_INVITATION_TEMPLATE,
      tenantWelcome: process.env.MAILGUN_TENANT_WELCOME_TEMPLATE,
      passwordReset: process.env.MAILGUN_PASSWORD_RESET_TEMPLATE,
      emailVerification: process.env.MAILGUN_EMAIL_VERIFICATION_TEMPLATE || 'email-verification',
      welcomeEmail: process.env.MAILGUN_WELCOME_EMAIL_TEMPLATE || 'welcome-email'
    }
  },
  
  // Tenant invitation configuration
  tenant: {
    invitationExpirationHours: parseInt(process.env.TENANT_INVITATION_EXPIRATION_HOURS || '48', 10),
    defaultPasswordLength: parseInt(process.env.DEFAULT_PASSWORD_LENGTH || '12', 10)
  },
  
  // Database configuration (handled separately in database.js)
  
  // File storage configuration
  storage: {
    uploadDir: process.env.UPLOAD_DIR || 'uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880', 10), // 5MB
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,application/pdf')
      .split(',')
      .map(type => type.trim())
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info'
  },
  
  // Pagination defaults
  pagination: {
    defaultLimit: parseInt(process.env.DEFAULT_PAGINATION_LIMIT || '10', 10),
    maxLimit: parseInt(process.env.MAX_PAGINATION_LIMIT || '100', 10)
  }
};

module.exports = config;

// Base error class
class BaseError extends Error {
  constructor(name, statusCode, isOperational, description) {
    super(description);
    
    Object.setPrototypeOf(this, new.target.prototype);
    this.name = name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    Error.captureStackTrace(this);
  }
}

// 400 Bad Request
class BadRequestError extends BaseError {
  constructor(description = 'Bad Request') {
    super('BAD_REQUEST', 400, true, description);
  }
}

// 401 Unauthorized
class AuthenticationError extends BaseError {
  constructor(description = 'Authentication Failed') {
    super('UNAUTHORIZED', 401, true, description);
  }
}

// 403 Forbidden
class AuthorizationError extends BaseError {
  constructor(description = 'Forbidden') {
    super('FORBIDDEN', 403, true, description);
  }
}

// 404 Not Found
class NotFoundError extends BaseError {
  constructor(description = 'Not Found') {
    super('NOT_FOUND', 404, true, description);
  }
}

// 409 Conflict
class ConflictError extends BaseError {
  constructor(description = 'Conflict') {
    super('CONFLICT', 409, true, description);
  }
}

// 422 Unprocessable Entity
class ValidationError extends BaseError {
  constructor(description = 'Validation Error') {
    super('VALIDATION_ERROR', 422, true, description);
  }
}

// 500 Internal Server Error
class InternalServerError extends BaseError {
  constructor(description = 'Internal Server Error') {
    super('INTERNAL_SERVER_ERROR', 500, false, description);
  }
}

// 503 Service Unavailable
class ServiceUnavailableError extends BaseError {
  constructor(description = 'Service Unavailable') {
    super('SERVICE_UNAVAILABLE', 503, true, description);
  }
}

// Export all error classes
module.exports = {
  BaseError,
  BadRequestError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  ValidationError,
  InternalServerError,
  ServiceUnavailableError,
};
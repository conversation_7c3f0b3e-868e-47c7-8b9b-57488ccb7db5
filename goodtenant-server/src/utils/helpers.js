/**
 * Generate a random password with specified length
 * @param {number} length - Length of the password
 * @returns {string} - Random password
 */
const generateRandomPassword = (length = 12) => {
  const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';  // Excluding I and O to avoid confusion
  const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz';  // Excluding l to avoid confusion
  const numberChars = '23456789';  // Excluding 0 and 1 to avoid confusion
  const specialChars = '!@#$%^&*_-+=';
  
  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
  
  // Ensure we have at least one of each type of character
  let password = '';
  password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
  password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
  password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
  password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));
  
  // Fill the rest of the password with random characters
  for (let i = 4; i < length; i++) {
    password += allChars.charAt(Math.floor(Math.random() * allChars.length));
  }
  
  // Shuffle the password characters
  password = password.split('').sort(() => 0.5 - Math.random()).join('');
  
  return password;
};

module.exports = {
  generateRandomPassword
};

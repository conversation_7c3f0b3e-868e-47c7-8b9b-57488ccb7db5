const jwt = require('jsonwebtoken');
const logger = require('./logger');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const ACCESS_TOKEN_EXPIRES_IN = process.env.ACCESS_TOKEN_EXPIRES_IN || '15m';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';
const RESET_TOKEN_EXPIRES_IN = process.env.RESET_TOKEN_EXPIRES_IN || '1h';
const EMAIL_VERIFICATION_TOKEN_EXPIRES_IN = process.env.EMAIL_VERIFICATION_TOKEN_EXPIRES_IN || '24h';

/**
 * Generate an access token
 * @param {Object} payload - Data to include in the token
 * @returns {string} JWT access token
 */
const generateAccessToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
  });
};

/**
 * Generate a refresh token
 * @param {Object} payload - Data to include in the token
 * @returns {string} JWT refresh token
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(
    { ...payload, isRefreshToken: true },
    JWT_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRES_IN }
  );
};

/**
 * Verify a JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload or null if invalid
 */
const verifyToken = (token, isRefreshToken = false) => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // If we're checking for a refresh token, verify it has the refresh token flag
    if (isRefreshToken && !decoded.isRefreshToken) {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    logger.error('Token verification failed:', error.message);
    return null;
  }
};

/**
 * Generate both access and refresh tokens
 * @param {Object} payload - Data to include in the tokens
 * @returns {Object} Object containing accessToken and refreshToken
 */
const generateTokens = (payload) => {
  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken(payload);
  return { accessToken, refreshToken };
};

/**
 * Generate a password reset token
 * @param {Object} payload - Data to include in the token (typically contains userId)
 * @returns {string} JWT reset token
 */
const generateResetToken = (payload) => {
  return jwt.sign(
    { ...payload, isResetToken: true },
    JWT_SECRET,
    { expiresIn: RESET_TOKEN_EXPIRES_IN }
  );
};

/**
 * Verify a password reset token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload or null if invalid
 */
const verifyResetToken = (token) => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Verify it's a reset token
    if (!decoded.isResetToken) {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    logger.error('Reset token verification failed:', error.message);
    return null;
  }
};

/**
 * Generate an email verification token
 * @param {Object} payload - Data to include in the token (typically contains userId)
 * @returns {string} JWT verification token
 */
const generateEmailVerificationToken = (payload) => {
  return jwt.sign(
    { ...payload, isEmailVerificationToken: true },
    JWT_SECRET,
    { expiresIn: EMAIL_VERIFICATION_TOKEN_EXPIRES_IN }
  );
};

/**
 * Verify an email verification token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload or null if invalid
 */
const verifyEmailVerificationToken = (token) => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Verify it's an email verification token
    if (!decoded.isEmailVerificationToken) {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    logger.error('Email verification token verification failed:', error.message);
    return null;
  }
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyToken,
  generateTokens,
  generateResetToken,
  verifyResetToken,
  generateEmailVerificationToken,
  verifyEmailVerificationToken
};

const { Pet, User, Property, Lease, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const pdfService = require('./pdfService');
const templateService = require('./templateService');

/**
 * Create a new pet
 * @param {Object} petData - Pet data
 * @param {string} userId - ID of the user creating the pet
 * @param {Object} options - Options for the create operation
 * @param {Transaction} options.transaction - Optional transaction to use
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createPet = async (petData, userId, options = {}) => {
  let transaction;
  
  try {
    // Only create a transaction if one wasn't provided
    if (options.transaction === undefined) {
      transaction = await sequelize.transaction();
    } else if (options.transaction) {
      transaction = options.transaction;
    }
    
    // Validate required fields
    if (!petData.name || !petData.type) {
      if (transaction && options.transaction === undefined) await transaction.rollback();
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Pet name and type are required'
        }
      };
    }

    // Validate weight if provided
    if (petData.weight !== undefined) {
      const weight = parseFloat(petData.weight);
      if (isNaN(weight) || weight < 0 || weight > 1000) {
        if (transaction && options.transaction === undefined) await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Weight must be a number between 0 and 1000'
          }
        };
      }
    }

    // Check if user exists
    const user = await User.findByPk(userId, { transaction });
    if (!user) {
      if (transaction && options.transaction === undefined) await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // Prepare pet data with proper type conversion
    const petDataToCreate = {
      ...petData,
      userId,
      // Ensure proper type conversion
      weight: petData.weight !== undefined ? parseFloat(petData.weight) : null,
      age: petData.age !== undefined ? parseInt(petData.age, 10) : null,
      // Ensure enum values are lowercase
      sex: petData.sex ? petData.sex.toLowerCase() : null
    };

    // Create the pet
    const pet = await Pet.create(petDataToCreate, { transaction });

    if (transaction && options.transaction === undefined) {
      await transaction.commit();
    }

    return {
      status: 201,
      payload: {
        success: true,
        data: pet
      }
    };
  } catch (error) {
    if (transaction && options.transaction === undefined) {
      await transaction.rollback();
    }
    
    logger.error('Error creating pet:', error);
    
    // Handle specific error types
    let errorMessage = 'Failed to create pet';
    if (error.name === 'SequelizeValidationError') {
      errorMessage = error.errors.map(e => e.message).join(', ');
    } else if (error.name === 'SequelizeUniqueConstraintError') {
      errorMessage = 'A pet with these details already exists';
    } else if (error.name === 'SequelizeDatabaseError') {
      errorMessage = 'Database error occurred while creating pet';
    }
    
    return {
      status: 500,
      payload: {
        success: false,
        message: errorMessage,
        error: error.message
      }
    };
  }
};

/**
 * Get all pets for a user with optional filtering and pagination
 * @param {string} userId - ID of the user
 * @param {Object} options - Query options (page, limit, search)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUserPets = async (userId, options = {}) => {
  try {
    const { page = 1, limit = 10, search = '' } = options;
    const offset = (page - 1) * limit;
    
    const whereClause = {
      userId,
      ...(search && {
        [Op.or]: [
          { name: { [Op.like]: `%${search}%` } },
          { notes: { [Op.like]: `%${search}%` } },
          { breed: { [Op.like]: `%${search}%` } },
          { registration: { [Op.like]: `%${search}%` } }
        ]
      })
    };

    const { count, rows } = await Pet.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching pets:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch pets',
        error: error.message
      }
    };
  }
};

/**
 * Get pet by ID
 * @param {string} id - Pet ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPetById = async (id, userId) => {
  try {
    const pet = await Pet.findOne({
      where: { id, userId },
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!pet) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Pet not found or access denied'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: pet
      }
    };
  } catch (error) {
    logger.error(`Error fetching pet ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch pet',
        error: error.message
      }
    };
  }
};

/**
 * Update pet
 * @param {string} id - Pet ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updatePet = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const pet = await Pet.findOne({
      where: { id, userId },
      transaction
    });

    if (!pet) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Pet not found or access denied'
        }
      };
    }

    // Prepare update data with proper type conversion
    const updateDataToUse = {
      ...updateData,
      // Ensure proper type conversion if these fields are being updated
      ...(updateData.weight !== undefined && { weight: parseFloat(updateData.weight) }),
      ...(updateData.age !== undefined && { age: parseInt(updateData.age, 10) }),
      // Ensure enum values are lowercase if updating sex
      ...(updateData.sex && { sex: updateData.sex.toLowerCase() })
    };

    const updatedPet = await pet.update(updateDataToUse, { transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: updatedPet
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating pet ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update pet',
        error: error.message
      }
    };
  }
};

/**
 * Delete pet
 * @param {string} id - Pet ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deletePet = async (id, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const pet = await Pet.findOne({
      where: { id, userId },
      transaction
    });

    if (!pet) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Pet not found or access denied'
        }
      };
    }

    await pet.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Pet deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting pet ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete pet',
        error: error.message
      }
    };
  }
};

/**
 * Generate pet document content for frontend rendering for all pets in a lease
 * @param {string} leaseId - ID of the lease
 * @param {string} [token] - Optional invitation token for public access
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with document content
 */
const generatePetDocument = async (leaseId, token = null) => {
  try {
    const { Lease, User, TenantInvitation } = require('../models');
    const { Op } = require('sequelize');
    let propertyId = null;
    
    // If using token validation, verify it first
    if (token) {      
      const invitation = await TenantInvitation.findOne({
        where: {
          token,
          status: 'pending',
          expiresAt: { [Op.gt]: new Date() }
        },
        include: [
          {
            model: require('../models').Property,
            as: 'property',
            attributes: ['id']
          }
        ]
      });

      if (!invitation || !invitation.property) {
        return {
          status: 401,
          payload: {
            success: false,
            message: 'Invalid or expired invitation token'
          }
        };
      }
      
      // Store the property ID from the invitation
      propertyId = invitation.property.id;
    }
    
    // Build the include array for the lease query
    const include = [
      {
        model: User,
        as: 'tenants',
        through: { attributes: [] },
        attributes: ['id', 'firstName', 'lastName', 'email'],
        include: [
          {
            model: Pet,
            as: 'pets',
            where: { status: 'active' }
          }
        ]
      },
      {
        model: Property,
        as: 'property',
        attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'zipCode']
      }
    ];

    // Add property filter if propertyId is provided
    if (propertyId) {
      include[0].include.push({
        model: require('../models').PropertyUser,
        as: 'propertyUsers',
        attributes: [],
        required: true,
        where: {
          propertyId: propertyId
        }
      });
    }

    let lease;
    try {
      logger.info('Starting lease query', { leaseId });
      
      // First, check if lease exists with minimal query
      const leaseExists = await Lease.findByPk(leaseId, {
        attributes: ['id'],
        raw: true
      });

      if (!leaseExists) {
        logger.warn('Lease not found', { leaseId });
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Lease not found'
          }
        };
      }

      // Then fetch with all includes
      lease = await Lease.findByPk(leaseId, { 
        include: [
          // Include tenants
          {
            model: User,
            as: 'tenants',
            through: { attributes: [] },
            attributes: ['id', 'firstName', 'lastName', 'email'],
            required: false,
            include: [
              {
                model: Pet,
                as: 'pets',
                required: false
              }
            ]
          },
          // Include property
          {
            model: Property,
            as: 'property',
            attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode'],
            required: false
          },
          // Include landlord
          {
            model: User,
            as: 'landlord',
            attributes: ['id', 'firstName', 'lastName', 'email'],
            required: false
          }
        ],
        attributes: ['id', 'startDate', 'endDate', 'monthlyRent', 'securityDeposit'],
        rejectOnEmpty: false
      });

      logger.info('Lease query completed', { 
        leaseFound: !!lease,
        hasProperty: !!(lease?.property),
        tenantCount: lease?.tenants?.length || 0
      });

      if (!lease) {
        logger.error('Lease not found after initial check', { leaseId });
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Lease not found'
          }
        };
      }


      if (!lease.property) {
        logger.error('Property not found for lease', { leaseId });
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Associated property not found'
          }
        };
      }

    } catch (error) {
      logger.error('Error in generatePetDocument:', {
        message: error.message,
        stack: error.stack,
        leaseId,
        errorName: error.name,
        errorCode: error.original?.code,
        sql: error.original?.sql,
        parameters: error.parameters
      });
      
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Error generating pet document',
          error: process.env.NODE_ENV === 'development' ? 
            `${error.message} (${error.original?.code || 'no-code'})` : 
            'Internal server error'
        }
      };
    }

    // Extract pets from tenants
    const pets = [];
    lease.tenants.forEach(tenant => {
      if (tenant.pets && tenant.pets.length > 0) {
        pets.push(...tenant.pets.map(pet => ({
          ...pet.get({ plain: true }),
          owner: {
            id: tenant.id,
            firstName: tenant.firstName,
            lastName: tenant.lastName,
            email: tenant.email
          }
        })));
      }
    });

    if (pets.length === 0) {
      return {
        status: 200,
        payload: {
          success: true,
          data: {
            hasPets: false,
            message: 'No pets found for this lease'
          }
        }
      };
    }

    // Get the template for pet addendum
    const templatesResponse = await templateService.getAllTemplates({
      type: 'PET',
      isActive: true,
      limit: 1
    });
    
    if (templatesResponse.status !== 200 || 
        !templatesResponse.payload?.success || 
        !templatesResponse.payload?.data?.length) {
      logger.warn('No active pet template found');
      return {
        status: 404,
        payload: {
          success: false,
          message: 'No active pet template found'
        }
      };
    }
    
    const template = templatesResponse.payload.data[0];

    // Generate standard variables for template replacement using the centralized function
    const variables = templateService.generateStandardVariables({
      lease,
      property: {
        ...lease.property.get({ plain: true }),
        // Add landlord info to property object for template
        landlord: lease.landlord ? {
          id: lease.landlord.id,
          firstName: lease.landlord.firstName,
          lastName: lease.landlord.lastName,
          email: lease.landlord.email
        } : null
      },
      tenants: lease.tenants,
      landlord: lease.landlord,
      // Add pets data for template
      pets: pets.map(pet => ({
        ...pet,
        isEmotionalSupport: pet.isEmotionalSupport ? 'Yes' : 'No',
        hasVaccinations: pet.hasVaccinations ? 'Yes' : 'No',
        owner: {
          fullName: `${pet.owner.firstName} ${pet.owner.lastName}`.trim(),
          ...pet.owner
        }
      }))
    });
    
    // Add individual pet variables for each pet in the format expected by templates
    // This is now handled by generateStandardVariables, so we don't need to add them here

    // Process the template content by replacing variables
    const documentContent = templateService.replaceVariablesInTemplate(template.content, variables);
    
    // Generate the document data with all variables replaced
    const documentData = {
      lease: {
        id: lease.id,
        startDate: lease.startDate ? new Date(lease.startDate).toLocaleDateString() : 'N/A',
        endDate: lease.endDate ? new Date(lease.endDate).toLocaleDateString() : 'N/A',
        monthlyRent: lease.monthlyRent || 0,
        securityDeposit: lease.securityDeposit || 0
      },
      pets: pets.map(pet => ({
        ...pet,
        type: pet.type || 'Not specified',
        breed: pet.breed || 'Not specified',
        weight: pet.weight ? `${pet.weight} lbs` : 'Not specified',
        age: pet.age || 'Not specified',
        sex: pet.sex || 'Not specified',
        notes: pet.notes || 'No additional notes'
      })),
      generatedOn: new Date().toLocaleDateString(),
      content: documentContent  // Add the processed template content
    };

    logger.info(`Successfully generated pet document content for lease ${leaseId}`);
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Pet addendum document content generated successfully',
        data: {
          documentContent: documentContent,
          documentName: `Pet Addendum - ${lease.property?.name || 'Lease ' + leaseId}`,
          templateId: template.id,
          leaseId: leaseId,
          pets: documentData.pets,
          lease: documentData.lease,
          generatedOn: documentData.generatedOn
        }
      }
    };

  } catch (error) {
    logger.error('Error generating pet document:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to generate pet document',
        error: error.message
      }
    };
  }
};

module.exports = {
  createPet,
  getUserPets,
  getPetById,
  updatePet,
  deletePet,
  generatePetDocument
};

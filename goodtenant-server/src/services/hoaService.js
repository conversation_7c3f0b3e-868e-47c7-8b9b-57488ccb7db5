const { HOA, Property, Lease, User, AccountUser, sequelize } = require('../models');
const templateService = require('./templateService');
const vehicleService = require('./vehicleService');
const occupantService = require('./occupantService');
const petService = require('./petService');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new HOA
 * @param {Object} data - HOA data
 * @param {string} accountId - The ID of the account creating the HOA
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createHOA = async (data, accountId) => {
  try {
    if (!accountId) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Account ID is required',
        },
      };
    }

    const transaction = await sequelize.transaction();
    
    try {
      // Extract address fields
      const { 
        name, fee, website, phone, email, 
        managerName, managerEmail, gateCode, comments, isActive,
        addressLine1, addressLine2, city, state, zipCode, country
      } = data;

      const hoa = await HOA.create({
        name, fee, website, phone, email,
        managerName, managerEmail, gateCode, comments, isActive,
        addressLine1, addressLine2, city, state, zipCode, country,
        accountId, // Add the account ID to the HOA
      }, { transaction });
      
      await transaction.commit();
      
      return {
        status: 201,
        payload: {
          success: true,
          data: hoa
        }
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating HOA:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to create HOA',
          error: error.message
        }
      };
    }
  } catch (error) {
    logger.error('Error creating HOA:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create HOA',
        error: error.message
      }
    };
  }
};

/**
 * Get all HOAs with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllHOAs = async (options = {}) => {
  try {
    const { page = 1, limit = 10, search = '' } = options;
    const offset = (page - 1) * limit;
    
    const whereClause = search ? {
      [Op.or]: [
        { name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { managerName: { [Op.like]: `%${search}%` } },
        { managerEmail: { [Op.like]: `%${search}%` } },
      ]
    } : {};

    const { count, rows } = await HOA.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Property,
          as: 'properties',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching HOAs:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch HOAs',
        error: error.message
      }
    };
  }
};

/**
 * Get HOA by ID
 * @param {string} id - HOA ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getHOAById = async (id) => {
  try {
    const hoa = await HOA.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'properties',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });

    if (!hoa) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'HOA not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: hoa
      }
    };
  } catch (error) {
    logger.error(`Error fetching HOA with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch HOA',
        error: error.message
      }
    };
  }
};

/**
 * Update HOA
 * @param {string} id - HOA ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateHOA = async (id, updateData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const hoa = await HOA.findByPk(id, { 
      transaction,
      include: [
        {
          model: Property,
          as: 'properties',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });
    
    if (!hoa) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'HOA not found'
        }
      };
    }

    // Update only allowed fields
    const allowedFields = [
      'name', 'fee', 'website', 'phone', 'email',
      'managerName', 'managerEmail', 'gateCode', 'comments', 'isActive',
      'addressLine1', 'addressLine2', 'city', 'state', 'zipCode', 'country'
    ];
    
    const updateFields = {};
    allowedFields.forEach(field => {
      if (field in updateData) {
        updateFields[field] = updateData[field];
      }
    });

    await hoa.update(updateFields, { transaction });
    await transaction.commit();

    // Refresh the hoa instance to get updated associations
    const updatedHOA = await HOA.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'properties',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: updatedHOA
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating HOA with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update HOA',
        error: error.message
      }
    };
  }
};

/**
 * Delete HOA
 * @param {string} id - HOA ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteHOA = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const hoa = await HOA.findByPk(id, { transaction });
    
    if (!hoa) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'HOA not found'
        }
      };
    }

    // Check if HOA has associated properties
    const propertyCount = await Property.count({
      where: { hoaId: id },
      transaction
    });

    if (propertyCount > 0) {
      await transaction.rollback();
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Cannot delete HOA with associated properties. Please remove properties from this HOA first.'
        }
      };
    }

    await hoa.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'HOA deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting HOA with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete HOA',
        error: error.message
      }
    };
  }
};

/**
 * Get properties by HOA ID
 * @param {string} hoaId - HOA ID
 * @param {Object} options - Query options (page, limit)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getHOAProperties = async (hoaId, options = {}) => {
  try {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;

    const hoa = await HOA.findByPk(hoaId);
    if (!hoa) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'HOA not found'
        }
      };
    }

    const { count, rows } = await Property.findAndCountAll({
      where: { hoaId },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error(`Error fetching properties for HOA ${hoaId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch HOA properties',
        error: error.message
      }
    };
  }
};

/**
 * Generate HOA document content for frontend rendering for tenants, vehicles, pets, and occupants in a lease
 * @param {string} leaseId - ID of the lease
 * @param {string} [token] - Optional invitation token for public access
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with document content
 */
const generateHOADocument = async (leaseId, token = null) => {
  try {
    let propertyId = null;
    
    // If using token validation, verify it first
    if (token) {
      const { TenantInvitation } = require('../models');
      const { Op } = require('sequelize');
      
      const invitation = await TenantInvitation.findOne({
        where: {
          token,
          status: 'pending',
          expiresAt: { [Op.gt]: new Date() }
        },
        include: [
          {
            model: Property,
            as: 'property',
            attributes: ['id']
          }
        ]
      });

      if (!invitation || !invitation.property) {
        return {
          status: 401,
          payload: {
            success: false,
            message: 'Invalid or expired invitation token'
          }
        };
      }
      
      // Store the property ID from the invitation
      propertyId = invitation.property.id;
    }

    let lease;
    try {
      logger.info('Starting lease query for HOA document', { leaseId });
      
      // First, check if lease exists with minimal query
      const leaseExists = await Lease.findByPk(leaseId, {
        attributes: ['id', 'landlordId'],
        raw: true
      });

      if (!leaseExists) {
        logger.warn('Lease not found', { leaseId });
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Lease not found'
          }
        };
      }


      // Then fetch with all includes
      lease = await Lease.findByPk(leaseId, { 
        include: [
          // Include tenants
          {
            model: User,
            as: 'tenants',
            through: { attributes: [] },
            attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
            required: false
          },
          // Include property with HOA
          {
            model: Property,
            as: 'property',
            attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country'],
            include: [{
              model: HOA,
              as: 'hoa',
              attributes: ['id', 'name', 'fee', 'website', 'phone', 'email', 'managerName', 'managerEmail', 'gateCode', 'comments', 'addressLine1', 'addressLine2', 'city', 'state', 'zipCode', 'country']
            }]
          },
          // Include landlord
          {
            model: User,
            as: 'landlord',
            attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
            required: false
          }
        ],
        attributes: ['id', 'startDate', 'endDate', 'monthlyRent', 'securityDeposit', 'landlordId'],
        rejectOnEmpty: false
      });

      logger.info('Lease query completed for HOA document', { 
        leaseFound: !!lease,
        hasProperty: !!(lease?.property),
        hasLandlord: !!(lease?.landlord),
        landlordId: lease?.landlordId,
        tenantCount: lease?.tenants?.length || 0
      });

      if (!lease) {
        logger.error('Lease not found after initial check', { leaseId });
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Lease not found'
          }
        };
      }


      if (!lease.property) {
        logger.error('Property not found for lease', { leaseId });
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Associated property not found'
          }
        };
      }
    } catch (error) {
      logger.error('Error in generateHOADocument:', {
        message: error.message,
        stack: error.stack,
        leaseId,
        errorName: error.name,
        errorCode: error.original?.code,
        sql: error.original?.sql,
        parameters: error.parameters
      });
      
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Error generating HOA document',
          error: process.env.NODE_ENV === 'development' ? 
            `${error.message} (${error.original?.code || 'no-code'})` : 
            'Internal server error'
        }
      };
    }
    
    // Debug log to check the lease data
    logger.debug('Lease data:', {
      leaseId: lease?.id,
      hasProperty: !!lease?.property,
      hasLandlord: !!lease?.landlord,
      landlordId: lease?.landlordId,
      landlordData: lease?.landlord,
      leaseRaw: JSON.parse(JSON.stringify(lease || {})), // Get plain object for debugging
      leaseKeys: Object.keys(lease || {})
    });

    // Log the actual data structure
    logger.debug('Lease object structure:', {
      dataValues: lease?.dataValues ? Object.keys(lease.dataValues) : 'no dataValues',
      _previousDataValues: lease?._previousDataValues ? Object.keys(lease._previousDataValues) : 'no _previousDataValues',
      _changed: lease?._changed || 'no _changed',
      _options: lease?._options ? Object.keys(lease._options) : 'no _options',
      hasLandlordGetter: !!lease?.landlord,
      getLandlordType: typeof lease?.landlord,
      isLandlordModel: lease?.landlord?.constructor?.name === 'Model' ? 'Yes' : 'No'
    });

    if (!lease) {
      logger.warn(`Lease ${leaseId} not found when generating HOA document`);
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found'
        }
      };
    }

    // Use default HOA values if no HOA is associated with the property
    const hoa = lease.property?.hoa || {
      name: 'Homeowners Association',
      fee: 0,
      website: '',
      phone: '',
      email: '',
      managerName: 'HOA Manager',
      managerEmail: '',
      gateCode: '',
      comments: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States'
    };
    
    logger.info(lease.property?.hoa 
      ? `Found HOA ${hoa.name} for property ${lease.property.id}`
      : `No HOA associated with property ${lease.property?.id}, using default values`
    );

    // Get all tenant IDs to fetch related data
    const tenantIds = lease.tenants.map(tenant => tenant.id);
    
    // Get vehicles for all tenants
    const vehiclesResponse = await Promise.all(
      tenantIds.map(tenantId => vehicleService.getUserVehicles(tenantId, { limit: 100 }))
    );
    
    // Get occupants for the lease (more efficient than per-tenant)
    const { payload: occupantsPayload } = await occupantService.getOccupantsByLease(leaseId, tenantIds[0], { 
      limit: 100, 
      isActive: true 
    });
    
    // Get pets for all tenants
    const petsResponse = await Promise.all(
      tenantIds.map(tenantId => petService.getUserPets(tenantId, { limit: 100 }))
    );

    // Format the data
    // Log vehicle response data for debugging
    logger.debug('Vehicle response data:', { 
      responses: vehiclesResponse.map(r => ({
        success: r.payload?.success,
        dataType: r.payload?.data ? typeof r.payload.data : 'undefined',
        isArray: Array.isArray(r.payload?.data),
        dataLength: Array.isArray(r.payload?.data) ? r.payload.data.length : 'n/a'
      }))
    });
    
    const vehicles = vehiclesResponse
      .filter(response => response.payload?.success && Array.isArray(response.payload?.data))
      .flatMap(response => response.payload.data.map(vehicle => ({
        ...vehicle,
        make: vehicle.make || 'Not specified',
        model: vehicle.model || 'Not specified',
        year: vehicle.year || 'Not specified',
        licensePlate: vehicle.licensePlate || 'Not specified',
        color: vehicle.color || 'Not specified',
        owner: vehicle.userId || 'Not specified'
      })));
      
    logger.debug(`Processed ${vehicles.length} vehicles for template replacement`);

    // Process occupants from the lease
    const occupants = occupantsPayload?.success && Array.isArray(occupantsPayload.data?.occupants)
      ? occupantsPayload.data.occupants.map(occupant => ({
          ...occupant,
          firstName: occupant.firstName || 'Not specified',
          lastName: occupant.lastName || 'Not specified',
          age: occupant.age || 'Not specified',
          relationship: occupant.relationship || 'Not specified',
          notes: occupant.notes || ''
        }))
      : [];

    let pets = [];
    try {
      logger.info('Starting pets processing', { responsesCount: petsResponse.length });
      
      // Process each response that was successful and has data
      pets = petsResponse
        .filter(response => {
          const hasData = response?.payload?.success && Array.isArray(response.payload?.data);
          logger.info('Filtering pet response', { 
            hasSuccess: !!response?.payload?.success,
            hasDataArray: Array.isArray(response.payload?.data),
            dataLength: response.payload?.data?.length
          });
          return hasData;
        })
        .flatMap(response => {
          logger.info('Processing pet response', { 
            dataLength: response.payload.data.length,
            firstPet: response.payload.data[0] 
          });
          
          // Get the user ID from the first pet's owner or use 'unknown'
          const userId = response.payload?.data[0]?.owner?.id || 'unknown';
          
          // Process each pet in the response
          return response.payload.data.map(pet => {
            if (!pet) {
              logger.warn('Found undefined pet in response data');
              return null;
            }
            return {
              ...pet,
              name: pet.name || 'Not specified',
              type: pet.type || 'Not specified',
              breed: pet.breed || 'Not specified',
              weight: pet.weight ? `${pet.weight} lbs` : 'Not specified',
              age: pet.age || 'Not specified',
              sex: pet.sex || 'Not specified',
              notes: pet.notes || 'No additional notes',
              owner: userId
            };
          }).filter(Boolean); // Remove any null entries
        });
      
      logger.info(`Successfully processed ${pets.length} pets for HOA document`);
    } catch (error) {
      logger.error('Error processing pets:', {
        error: error.message,
        stack: error.stack,
        petsResponse: JSON.stringify(petsResponse, null, 2)
      });
      // Continue with empty pets array if there's an error
      pets = [];
    }

    // Get the HOA template
    const templatesResponse = await templateService.getAllTemplates({
      type: 'HOA',
      isActive: true,
      limit: 1
    });
    
    if (templatesResponse.status !== 200 || 
        !templatesResponse.payload?.success || 
        !templatesResponse.payload?.data?.length) {
      logger.warn('No active HOA template found');
      return {
        status: 404,
        payload: {
          success: false,
          message: 'No active HOA template found'
        }
      };
    }
    
    const template = templatesResponse.payload.data[0];

    // Helper function to safely extract data from Sequelize instances
    const getPlainData = (data) => {
      if (!data) return null;
      if (typeof data.toJSON === 'function') {
        return data.toJSON();
      }
      return data;
    };

    // Prepare HOA data
    const hoaPlain = lease.property?.hoa ? getPlainData(lease.property.hoa) : null;
    
    const hoaData = hoaPlain ? {
      ...hoaPlain,
      fee: hoaPlain.fee ? templateService.formatMoney(hoaPlain.fee) : '0.00'
    } : {
      name: 'Homeowners Association',
      fee: '0.00',
      website: '',
      phone: '',
      email: '',
      managerName: 'HOA Manager',
      managerEmail: '',
      gateCode: '',
      comments: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States'
    };

    // Prepare lease data
    const leasePlain = getPlainData(lease) || {};
    const propertyPlain = getPlainData(lease.property) || {};
    
    // Try to get landlord from different possible sources
    let landlordPlain = {};
    
    // 1. First try to get from lease.landlord
    if (lease.landlord) {
      logger.debug('Using landlord from lease.landlord');
      landlordPlain = getPlainData(lease.landlord);
    } 
    // 2. Try to get from lease.landlordId
    else if (lease.landlordId) {
      logger.debug('Looking up landlord by lease.landlordId:', lease.landlordId);
      try {
        const landlord = await User.findByPk(lease.landlordId);
        if (landlord) {
          landlordPlain = getPlainData(landlord);
          logger.debug('Found landlord by ID:', { id: landlordPlain.id, name: `${landlordPlain.firstName} ${landlordPlain.lastName}` });
        }
      } catch (error) {
        logger.error('Error looking up landlord by ID:', error);
      }
    }
    
    // 3. Fallback to first property user if still no landlord
    if (!landlordPlain.id && propertyPlain.users?.length > 0) {
      logger.debug('Using first property user as landlord');
      landlordPlain = getPlainData(propertyPlain.users[0]);
    }
    
    // Get representative data for variable replacement
    // For simplicity, we'll take the first item from each collection if available
    const representativePet = pets.length > 0 ? pets[0] : null;
    const representativeOccupant = occupants.length > 0 ? occupants[0] : null;
    const representativeVehicle = vehicles.length > 0 ? vehicles[0] : null;

    // Generate standard variables with the correct landlord data
    const variables = templateService.generateStandardVariables({
      lease: leasePlain,
      property: propertyPlain,
      tenants: lease.tenants,
      landlord: landlordPlain, // Use the landlord data we just prepared
      pet: representativePet,
      pets: pets, // Pass the entire pets array
      occupant: representativeOccupant,
      vehicle: representativeVehicle, // Keep single vehicle for backward compatibility
      vehicles: vehicles // Pass the entire vehicles array
    });
    
    // Safe logging to avoid circular references
    try {
      // Only log a few key vehicle variables to avoid circular references
      const safeVariables = { ...variables };
      // Remove potentially circular references
      delete safeVariables.vehicles;
      delete safeVariables.pets;
      logger.debug('Template variables (partial):', JSON.stringify(safeVariables, null, 2));
    } catch (error) {
      logger.warn('Could not stringify template variables for logging');
    }

    // Process the template content
    const documentContent = templateService.replaceVariablesInTemplate(template.content, variables);
    
    logger.debug('Final landlord data:', landlordPlain);
    const tenantsPlain = Array.isArray(lease.tenants) 
      ? lease.tenants.map(t => getPlainData(t))
      : [];
    
    // Create a plain object with only the necessary data
    const documentData = {
      hoa: hoaData,
      property: {
        id: propertyPlain.id || 'N/A',
        name: propertyPlain.name || 'N/A',
        address: propertyPlain ? 
          `${propertyPlain.addressLine1 || ''}, ${propertyPlain.city || ''}, ${propertyPlain.state || ''} ${propertyPlain.postalCode || ''}`.trim() : 
          'N/A'
      },
      landlord: {
        id: landlordPlain.id || 'N/A',
        firstName: landlordPlain.firstName || 'N/A',
        lastName: landlordPlain.lastName || 'N/A',
        fullName: [landlordPlain.firstName, landlordPlain.lastName].filter(Boolean).join(' ') || 'N/A',
        email: landlordPlain.email || 'N/A',
        phone: landlordPlain.phone || 'N/A',
        source: lease.landlord ? 'lease' : lease.landlordId ? 'leaseId' : propertyPlain.users?.length > 0 ? 'property' : 'default'
      },
      tenants: tenantsPlain.map(t => ({
        id: t.id,
        firstName: t.firstName || 'N/A',
        lastName: t.lastName || 'N/A',
        fullName: [t.firstName, t.lastName].filter(Boolean).join(' ') || 'N/A',
        email: t.email || 'N/A',
        phone: t.phone || 'N/A'
      })),
      lease: {
        id: leasePlain.id || 'N/A',
        startDate: leasePlain.startDate ? new Date(leasePlain.startDate).toLocaleDateString() : 'N/A',
        endDate: leasePlain.endDate ? new Date(leasePlain.endDate).toLocaleDateString() : 'N/A',
        monthlyRent: leasePlain.monthlyRent ? `$${parseFloat(leasePlain.monthlyRent).toFixed(2)}` : '$0.00',
        securityDeposit: leasePlain.securityDeposit ? `$${parseFloat(leasePlain.securityDeposit).toFixed(2)}` : '$0.00',
        lateFee: leasePlain.lateFee ? `$${parseFloat(leasePlain.lateFee).toFixed(2)}` : '$0.00',
        nsfFee: leasePlain.nsfFee ? `$${parseFloat(leasePlain.nsfFee).toFixed(2)}` : '$0.00',
        earlyTerminationFee: leasePlain.earlyTerminationFee ? `$${parseFloat(leasePlain.earlyTerminationFee).toFixed(2)}` : '$0.00',
        status: leasePlain.status || 'N/A',
        notes: leasePlain.notes || ''
      },
      vehicles: vehicles.map(v => ({
        make: v.make || 'N/A',
        model: v.model || 'N/A',
        year: v.year || 'N/A',
        color: v.color || 'N/A',
        licensePlate: v.licensePlate || 'N/A',
        owner: v.owner || 'N/A'
      })),
      occupants: occupants.map(o => ({
        firstName: o.firstName || 'N/A',
        lastName: o.lastName || 'N/A',
        age: o.age || 'N/A',
        relationship: o.relationship || 'N/A',
        notes: o.notes || ''
      })),
      pets: pets.map(p => ({
        name: p.name || 'N/A',
        type: p.type || 'N/A',
        breed: p.breed || 'N/A',
        age: p.age || 'N/A',
        weight: p.weight || 'N/A',
        notes: p.notes || ''
      })),
      content: documentContent,
      generatedOn: new Date().toISOString()
    };

    logger.info(`Successfully generated HOA document for lease ${leaseId}`);

    return {
      status: 200,
      payload: {
        success: true,
        message: 'HOA document content generated successfully',
        data: {
          documentContent: documentContent,
          documentName: `HOA Information - ${lease.property?.name || 'Lease ' + leaseId}`,
          templateId: template?.id || null,
          leaseId: leaseId,
          hoa: {
            name: hoa.name,
            fee: hoa.fee,
            managerName: hoa.managerName,
            managerEmail: hoa.managerEmail,
            phone: hoa.phone,
            email: hoa.email,
            address: {
              line1: hoa.addressLine1,
              line2: hoa.addressLine2,
              city: hoa.city,
              state: hoa.state,
              postalCode: hoa.zipCode,
              country: hoa.country
            }
          },
          tenants: documentData.tenants,
          vehicles: documentData.vehicles,
          pets: documentData.pets,
          occupants: documentData.occupants,
          generatedOn: documentData.generatedOn
        }
      }
    };
  } catch (error) {
    logger.error(`Error generating HOA document for lease ${leaseId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Error generating HOA document',
        error: error.message
      }
    };
  }
};

module.exports = {
  createHOA,
  getAllHOAs,
  getHOAById,
  updateHOA,
  deleteHOA,
  generateHOADocument,
  getHOAProperties
};

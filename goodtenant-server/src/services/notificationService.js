const { Notification, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const { NOTIFICATION_TYPES, NOTIFICATION_ENTITIES } = require('../constants/notifications');

/**
 * Create a new notification
 * @param {Object} notificationData - Notification data
 * @param {string} createdById - ID of the user creating the notification
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createNotification = async (notificationData, createdById) => {
  const transaction = await sequelize.transaction();
  
  try {
    const notification = await Notification.create({
      ...notificationData,
      createdById,
    }, { transaction });
    
    // Fetch the created notification with related data
    const createdNotification = await Notification.findByPk(notification.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ],
      transaction
    });
    
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: createdNotification
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating notification:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create notification',
        error: error.message
      }
    };
  }
};

/**
 * Get all notifications with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, isRead, userId, type, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllNotifications = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      isRead,
      userId,
      type,
      entityType,
      entityId,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = options;

    const offset = (page - 1) * limit;
    const where = {};

    if (isRead !== undefined) {
      where.isRead = isRead;
    }

    if (userId) {
      where.userId = userId;
    }

    if (type) {
      where.type = type;
    }

    if (entityType) {
      where.entityType = entityType;
    }

    if (entityId) {
      where.entityId = entityId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt[Op.gte] = new Date(startDate);
      if (endDate) where.createdAt[Op.lte] = new Date(endDate);
    }

    const { count, rows } = await Notification.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ],
      order: [[sortBy, sortOrder]],
      limit: parseInt(limit, 10),
      offset: parseInt(offset, 10),
      distinct: true
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page, 10),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching notifications:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch notifications',
        error: error.message
      }
    };
  }
};

/**
 * Get notification by ID
 * @param {string} id - Notification ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getNotificationById = async (id) => {
  try {
    const notification = await Notification.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!notification) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Notification not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: notification
      }
    };
  } catch (error) {
    logger.error(`Error fetching notification with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch notification',
        error: error.message
      }
    };
  }
};

/**
 * Mark notification as read
 * @param {string} id - Notification ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const markAsRead = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const notification = await Notification.findByPk(id, { transaction });
    
    if (!notification) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Notification not found'
        }
      };
    }
    
    if (notification.isRead) {
      await transaction.rollback();
      return {
        status: 200,
        payload: {
          success: true,
          message: 'Notification is already marked as read',
          data: notification
        }
      };
    }
    
    await notification.update({ isRead: true }, { transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Notification marked as read',
        data: notification
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error marking notification ${id} as read:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to mark notification as read',
        error: error.message
      }
    };
  }
};

/**
 * Mark all user notifications as read
 * @param {string} userId - User ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const markAllAsRead = async (userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const [updatedCount] = await Notification.update(
      { isRead: true },
      {
        where: { 
          userId,
          isRead: false 
        },
        transaction
      }
    );
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: `Marked ${updatedCount} notifications as read`,
        data: { updatedCount }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error marking all notifications as read for user ${userId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to mark notifications as read',
        error: error.message
      }
    };
  }
};

/**
 * Delete notification (soft delete)
 * @param {string} id - Notification ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteNotification = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const notification = await Notification.findByPk(id, { transaction });
    
    if (!notification) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Notification not found'
        }
      };
    }
    
    await notification.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Notification deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting notification ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete notification',
        error: error.message
      }
    };
  }
};

module.exports = {
  createNotification,
  getAllNotifications,
  getNotificationById,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  NOTIFICATION_TYPES,
  NOTIFICATION_ENTITIES
};

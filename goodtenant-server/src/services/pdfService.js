const PDFDocument = require('pdfkit');
const logger = require('../utils/logger');
const { promisify } = require('util');
const stream = require('stream');

// Convert stream to buffer
const streamToBuffer = async (readableStream) => {
  const chunks = [];
  return new Promise((resolve, reject) => {
    readableStream.on('data', (chunk) => chunks.push(chunk));
    readableStream.on('error', reject);
    readableStream.on('end', () => resolve(Buffer.concat(chunks)));
  });
};

/**
 * Add signature to a PDF
 * @param {Buffer} pdfBytes - Original PDF as buffer
 * @param {Object} signature - Signature data { image: Buffer, x: number, y: number, width: number, height: number, pageNumber: number }
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const addSignatureToPdf = async (pdfBytes, signature) => {
  try {
    // For now, we'll return the original PDF as we need to implement proper PDF manipulation
    // with a library that supports PDF form filling and signing
    logger.warn('PDF signing not fully implemented yet - returning original PDF');
    
    return {
      status: 200,
      payload: {
        success: true,
        data: pdfBytes
      }
    };
  } catch (error) {
    logger.error('Error adding signature to PDF:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to add signature to PDF',
        error: error.message
      }
    };
  }
};

/**
 * Generate a PDF from template content and variables
 * @param {Object} templateContent - Structured content from the template
 * @param {Object} variables - Key-value pairs for template variables
 * @param {string} fileName - Desired file name for the PDF
 * @returns {Promise<{status: number, payload: Object}>} - Status and response with PDF buffer
 */
const generatePdfFromTemplate = async (templateContent, variables, fileName) => {
  const doc = new PDFDocument({
    bufferPages: true,
    autoFirstPage: false
  });
  
  // Create a buffer to store the PDF
  const buffers = [];
  doc.on('data', buffers.push.bind(buffers));
  
  // Helper to replace variables in text
  const replaceVariables = (text) => {
    if (!text) return '';
    return Object.entries(variables).reduce(
      (str, [key, value]) => str.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), String(value || '')),
      text
    );
  };
  
  // Add first page
  doc.addPage({
    size: 'LETTER',
    margins: { top: 50, left: 50, right: 50, bottom: 50 }
  });
  
  // Set default font
  doc.fontSize(12);
  
  // Process template sections
  if (templateContent && Array.isArray(templateContent.content)) {
    for (const section of templateContent.content) {
      // Check if we need a new page (leave at least 50pt at the bottom)
      if (doc.y > doc.page.height - 100) {
        doc.addPage({
          size: 'LETTER',
          margins: { top: 50, left: 50, right: 50, bottom: 50 }
        });
      }
      
      switch (section.type) {
        case 'header':
          doc.fontSize(16).font('Helvetica-Bold');
          doc.text(replaceVariables(section.text || ''), {
            align: 'left',
            continued: false
          });
          doc.moveDown();
          break;
          
        case 'paragraph':
          doc.fontSize(12).font('Helvetica');
          doc.text(replaceVariables(section.text || ''), {
            align: 'left',
            width: doc.page.width - 100, // Account for margins
            lineGap: 5,
            paragraphGap: 10
          });
          break;
      }
    }
  }
  
  // Finalize the PDF and get the buffer
  doc.end();
  
  try {
    const pdfBuffer = await new Promise((resolve, reject) => {
      const chunks = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
    });
    
    return {
      status: 200,
      payload: {
        success: true,
        data: {
          buffer: pdfBuffer,
          mimeType: 'application/pdf',
          size: pdfBuffer.length,
          fileName: fileName
        }
      }
    };
  } catch (error) {
    logger.error('Error generating PDF from template:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to generate PDF from template',
        error: error.message
      }
    };
  }
};

module.exports = {
  addSignatureToPdf,
  generatePdfFromTemplate
};

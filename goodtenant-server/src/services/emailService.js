const formData = require('form-data');
const Mailgun = require('mailgun.js');
const logger = require('../utils/logger');
const config = require('../config');

// Initialize Mailgun client
const mailgun = new Mailgun(formData);
const mg = mailgun.client({
  username: 'api',
  key: config.mailgun.apiKey,
  url: config.mailgun.url || 'https://api.mailgun.net',
  timeout: 60000
});

// Helper function to create message options
const createMessageOptions = (emailData) => {
  const msg = {
    to: emailData.to,
    from: emailData.from || config.mailgun.defaultFrom,
    subject: emailData.subject,
    text: emailData.text,
    html: emailData.html || emailData.text,
  };

  // Add template if specified
  if (emailData.templateName) {
    msg.template = emailData.templateName;
    msg['h:X-Mailgun-Variables'] = JSON.stringify(emailData.templateVars || {});
  }

  return msg;
};

/**
 * Send an email using Mailgun
 * @param {Object} emailData - Email data
 * @param {string} emailData.to - Recipient email
 * @param {string} emailData.from - Sender email (optional, uses default if not provided)
 * @param {string} emailData.subject - Email subject
 * @param {string} emailData.text - Plain text content
 * @param {string} emailData.html - HTML content
 * @returns {Promise<{success: boolean, error?: string, id?: string}>} - Success status
 */
const sendEmail = async (emailData) => {
  try {
    const msg = createMessageOptions(emailData);
    const result = await mg.messages.create(config.mailgun.domain, msg);
    
    logger.info(`Email sent to ${emailData.to}, ID: ${result.id}`);
    return { success: true, id: result.id };
  } catch (error) {
    logger.error('Error sending email:', error);
    return { 
      success: false,
      error: error.message 
    };
  }
};

/**
 * Send an email using a Mailgun template
 * @param {Object} emailData - Email data
 * @param {string} emailData.to - Recipient email
 * @param {string} emailData.from - Sender email (optional, uses default if not provided)
 * @param {string} emailData.subject - Email subject
 * @param {string} emailData.templateName - Mailgun template name
 * @param {Object} emailData.templateVars - Dynamic template variables
 * @returns {Promise<{success: boolean, error?: string, id?: string}>} - Success status
 */
const sendTemplateEmail = async (emailData) => {
  try {
    const msg = createMessageOptions({
      ...emailData,
      html: emailData.html || '' // Ensure html is defined
    });
    
    const result = await mg.messages.create(config.mailgun.domain, msg);
    logger.info(`Template email sent to ${emailData.to}, ID: ${result.id}`);
    return { success: true, id: result.id };
  } catch (error) {
    logger.error('Error sending template email:', error);
    return { 
      success: false,
      error: error.message 
    };
  }
};

/**
 * Send bulk emails using Mailgun
 * @param {Array<Object>} emailsData - Array of email data objects
 * @returns {Promise<{success: boolean, error?: string, results?: Array}>} - Success status
 */
const sendBulkEmails = async (emailsData) => {
  try {
    const results = [];
    
    // Process emails in parallel with a reasonable concurrency limit
    const BATCH_SIZE = 10;
    for (let i = 0; i < emailsData.length; i += BATCH_SIZE) {
      const batch = emailsData.slice(i, i + BATCH_SIZE);
      
      await Promise.all(batch.map(async (data) => {
        try {
          const msg = createMessageOptions(data);
          const result = await mg.messages.create(config.mailgun.domain, msg);
          results.push(result);
        } catch (error) {
          logger.error(`Error sending email to ${data.to}:`, error);
          // Continue with other emails even if one fails
        }
      }));
    }
    
    logger.info(`Bulk emails sent to ${emailsData.length} recipients`);
    return { success: true, results };
  } catch (error) {
    logger.error('Error sending bulk emails:', error);
    return { 
      success: false,
      error: error.message 
    };
  }
};

module.exports = {
  sendEmail,
  sendTemplateEmail,
  sendBulkEmails
};

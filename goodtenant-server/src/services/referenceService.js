const { Reference, User } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new reference
 * @param {Object} referenceData - Reference data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createReference = async (referenceData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const reference = await Reference.create(referenceData, { transaction });
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: reference
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating reference:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create reference',
        error: error.message
      }
    };
  }
};

/**
 * Get all references with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, userId, status, search, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllReferences = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      userId,
      status,
      isVerified
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Apply filters
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { company: { [Op.like]: `%${search}%` } },
        { propertyAddress: { [Op.like]: `%${search}%` } }
      ];
    }
    
    if (userId) whereClause.userId = userId;
    if (status) whereClause.status = status;
    if (isVerified !== undefined) whereClause.isVerified = isVerified;

    const { count, rows } = await Reference.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'tenant',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: true
        },
        {
          model: User,
          as: 'verifiedBy',
          attributes: ['id', 'firstName', 'lastName'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching references:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch references',
        error: error.message
      }
    };
  }
};

/**
 * Get reference by ID
 * @param {string} id - Reference ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getReferenceById = async (id) => {
  try {
    const reference = await Reference.findByPk(id, {
      include: [
        {
          model: User,
          as: 'tenant',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'verifiedBy',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    if (!reference) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Reference not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: reference
      }
    };
  } catch (error) {
    logger.error(`Error fetching reference with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch reference',
        error: error.message
      }
    };
  }
};

/**
 * Update reference
 * @param {string} id - Reference ID
 * @param {Object} updateData - Data to update
 * @param {Object} user - Current user making the update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateReference = async (id, updateData, user) => {
  const transaction = await sequelize.transaction();
  
  try {
    const reference = await Reference.findByPk(id, { transaction });
    
    if (!reference) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Reference not found'
        }
      };
    }

    // If updating verification status, record who verified it
    if (updateData.isVerified && updateData.isVerified !== reference.isVerified) {
      updateData.verifiedById = user.id;
      updateData.verificationDate = new Date();
    }

    await reference.update(updateData, { transaction });
    await transaction.commit();

    // Get the updated reference with associations
    const updatedReference = await Reference.findByPk(id, {
      include: [
        { model: User, as: 'tenant' },
        { model: User, as: 'verifiedBy' }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: updatedReference
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating reference with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update reference',
        error: error.message
      }
    };
  }
};

/**
 * Delete reference
 * @param {string} id - Reference ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteReference = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const reference = await Reference.findByPk(id, { transaction });
    
    if (!reference) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Reference not found'
        }
      };
    }

    await reference.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Reference deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting reference with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete reference',
        error: error.message
      }
    };
  }
};

/**
 * Get references by user ID
 * @param {string} userId - User ID
 * @param {Object} options - Query options (page, limit, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getReferencesByUserId = async (userId, options = {}) => {
  try {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;

    const { count, rows } = await Reference.findAndCountAll({
      where: { userId },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'verifiedBy',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error(`Error fetching references for user ${userId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch references',
        error: error.message
      }
    };
  }
};

module.exports = {
  createReference,
  getAllReferences,
  getReferenceById,
  updateReference,
  deleteReference,
  getReferencesByUserId
};
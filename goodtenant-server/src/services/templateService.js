const { sequelize } = require('../config/database');
const Template = require('../models/template')(sequelize);
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const { SYSTEM_VARIABLES } = require('../config/templateVariables');
const fs = require('fs');
const path = require('path');

/**
 * Get all available system variables
 * @returns {Object} Object containing system variables
 */
const getSystemVariables = () => {
  return {
    status: 200,
    payload: {
      success: true,
      data: SYSTEM_VARIABLES
    }
  };
};

/**
 * Create a new template
 * @param {Object} templateData - Template data
 * @param {string} userId - ID of the user creating the template
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createTemplate = async (templateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Create the template with the transaction
    const template = await Template.create(
      {
        ...templateData,
        createdBy: userId,
        updatedBy: userId
      },
      { transaction }
    );

    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: template
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating template:', error);
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create template',
        error: error.message
      }
    };
  }
};

/**
 * Get all templates with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, type, isActive, accountId)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllTemplates = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      type,
      isActive,
      accountId
    } = options;
    
    const offset = (page - 1) * limit;
    const where = {};
    
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (type) {
      where.type = type;
    }
    
    if (isActive !== undefined) {
      // Handle both string 'true'/'false' and boolean true/false
      where.isActive = isActive === true || isActive === 'true';
    }
    
    if (accountId) {
      where.accountId = accountId;
    }
    
    const { count, rows } = await Template.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });
    
    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit),
          limit: parseInt(limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching templates:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch templates',
        error: error.message
      }
    };
  }
};

/**
 * Get template by ID
 * @param {string} id - Template ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getTemplateById = async (id) => {
  try {
    const template = await Template.findByPk(id);
    
    if (!template) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Template not found'
        }
      };
    }
    
    return {
      status: 200,
      payload: {
        success: true,
        data: template
      }
    };
  } catch (error) {
    logger.error(`Error fetching template with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch template',
        error: error.message
      }
    };
  }
};

/**
 * Update template
 * @param {string} id - Template ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user updating the template
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateTemplate = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const template = await Template.findByPk(id, { transaction });
    
    if (!template) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Template not found'
        }
      };
    }
    
    // Update the template
    await template.update(
      {
        ...updateData,
        updatedBy: userId
      },
      { transaction }
    );
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: template
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating template with ID ${id}:`, error);
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update template',
        error: error.message
      }
    };
  }
};

/**
 * Delete template
 * @param {string} id - Template ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteTemplate = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const template = await Template.findByPk(id, { transaction });
    
    if (!template) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Template not found'
        }
      };
    }
    
    // Prevent deletion of system templates
    if (template.isSystem) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Cannot delete system templates'
        }
      };
    }
    
    await template.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Template deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting template with ID ${id}:`, error);
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete template',
        error: error.message
      }
    };
  }
};

/**
 * Process a template by replacing variables in its content
 * @param {Object|Array} content - Template content (structured JSON)
 * @param {Object} variables - Key-value pairs for variable replacement
 * @returns {Object|Array} - Processed template content with variables replaced
 */
const replaceVariablesInTemplate = (content, variables) => {
  if (!content) return content;
  
  // Make a deep copy of the content to avoid modifying the original
  const contentCopy = JSON.parse(JSON.stringify(content));
  
  // Helper function to recursively replace variables in the content
  const replaceVariablesInContent = (contentToProcess) => {
    if (!contentToProcess) return contentToProcess;
    
    // If this is an array, process each item
    if (Array.isArray(contentToProcess)) {
      return contentToProcess.map(item => replaceVariablesInContent(item));
    }
    
    // If this is an object, process each property
    if (typeof contentToProcess === 'object') {
      const result = {};
      for (const key in contentToProcess) {
        result[key] = replaceVariablesInContent(contentToProcess[key]);
      }
      return result;
    }
    
    // If this is a string, replace variables using both {{variableName}} syntax and HTML data attributes
    if (typeof contentToProcess === 'string') {
      let result = contentToProcess;
      
      // First replace {{variable}} syntax
      for (const varName in variables) {
        // Handle {{variable}} syntax
        const placeholder = `{{${varName}}}`;
        // Handle data-type="variable" attributes
        const htmlPlaceholder = new RegExp(
          `(<[^>]+data-type=["']variable["'][^>]*id=["']${varName}["'][^>]*>)[^<]*(<\/span>)`, 
          'gi'
        );
        
        const value = variables[varName] !== undefined && variables[varName] !== null 
          ? variables[varName] 
          : ''; // Use empty string for undefined/null variables
        
        // Replace {{variable}} syntax
        result = result.replace(new RegExp(placeholder, 'g'), value);
        
        // Replace HTML data attributes
        result = result.replace(htmlPlaceholder, `$1${value}$2`);
      }
      
      // Remove any remaining pet placeholders (e.g., {{pet2.name}})
      result = result.replace(/\{\{pet\d+\.[^}]*\}\}/g, '');
      
      // Remove any remaining vehicle placeholders (e.g., {{vehicle1.make}})
      result = result.replace(/\{\{vehicle\d+\.[^}]*\}\}/g, '');
      
      return result;
    }
    
    // Otherwise return the content as is
    return contentToProcess;
  };
  
  return replaceVariablesInContent(contentCopy);
};

/**
 * Helper function to safely format monetary values
 * @param {string|number} value - Value to format
 * @returns {string} - Formatted value with 2 decimal places
 */
const formatMoney = (value) => {
  if (value === null || value === undefined || value === '') {
    return '0.00';
  }
  
  // Convert to number and handle any conversion issues
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0.00';
  }
  
  return num.toFixed(2);
};

/**
 * Generate standard template variables from common data objects
 * @param {Object} data - Data object containing lease, property, tenants, and other information
 * @returns {Object} - Object containing standardized variables for template replacement
 */
const generateStandardVariables = (data = {}) => {
  const { 
    lease = {}, 
    property = {}, 
    tenants = [], 
    landlord = {},
    pets = [],
    pet = null,
    occupant = null,
    vehicle = null,
    vehicles = [],
    ...rest
  } = data;
  
  // Extract individual pet variables (pet1, pet2, etc.) from rest
  const petVars = {};
  Object.keys(rest).forEach(key => {
    if (key.startsWith('pet') && !isNaN(parseInt(key.substring(3), 10))) {
      petVars[key] = rest[key];
    }
  });
  
  // Get primary tenant (first one if available)
  const primaryTenant = Array.isArray(tenants) && tenants.length > 0 ? tenants[0] : null;
  
  // Format dates
  const dateOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
  
  return {
    // Property variables
    'property.address': property ? 
      `${property.addressLine1 || ''}, ${property.city || ''}, ${property.state || ''} ${property.postalCode || ''}`.trim() : 'N/A',
    'property.name': property?.name || 'N/A',
    'property.city': property?.city || 'N/A',
    'property.state': property?.state || 'N/A',
    'property.postalCode': property?.postalCode || 'N/A',
      
    // Tenant variables
    'tenant.fullName': primaryTenant ? `${primaryTenant.firstName || ''} ${primaryTenant.lastName || ''}`.trim() : 'N/A',
    'tenant.firstName': primaryTenant?.firstName || 'N/A',
    'tenant.lastName': primaryTenant?.lastName || 'N/A',
    'tenant.email': primaryTenant?.email || 'N/A',
    'tenant.phone': primaryTenant?.phone || 'N/A',
      
    // Landlord variables
    'landlord.fullName': landlord ? `${landlord.firstName || ''} ${landlord.lastName || ''}`.trim() : 'N/A',
    'landlord.firstName': landlord?.firstName || 'N/A',
    'landlord.lastName': landlord?.lastName || 'N/A',
    'landlord.email': landlord?.email || 'N/A',
    'landlord.phone': landlord?.phone || 'N/A',
      
    // Lease variables
    'lease.startDate': lease?.startDate ? new Date(lease.startDate).toLocaleDateString('en-US', dateOptions) : 'N/A',
    'lease.endDate': lease?.endDate ? new Date(lease.endDate).toLocaleDateString('en-US', dateOptions) : 'N/A',
    'lease.depositAmount': formatMoney(lease?.securityDeposit || 0),
    'lease.monthlyRent': formatMoney(lease?.monthlyRent || 0),
    'lease.lateFee': formatMoney(lease?.lateFee || 0),
    'lease.nsfFee': formatMoney(lease?.nsfFee || 0),
    'lease.numberOfPeople': Array.isArray(tenants) ? tenants.length : 0,
    'lease.paymentDueDate': lease?.paymentDueDate || 1,

    // Pets collection - only include if there are pets
    ...(Array.isArray(pets) && pets.length > 0 ? { 'pets': pets } : {}),
    'pets.count': Array.isArray(pets) ? pets.length : 0,
    
    // Individual pet variables (pet1.name, pet2.name, etc.)
    ...(Array.isArray(pets) ? pets.reduce((acc, pet, index) => {
      // Only include pet variables if the pet object exists and has data
      if (!pet || !pet.name) return acc;
      
      const petNumber = index + 1;
      const petVars = {
        [`pet${petNumber}.name`]: pet.name,
        [`pet${petNumber}.type`]: pet?.type || '',
        [`pet${petNumber}.breed`]: pet?.breed || '',
        [`pet${petNumber}.sex`]: pet?.sex || '',
        [`pet${petNumber}.age`]: pet?.age || '',
        [`pet${petNumber}.weight`]: pet?.weight || '',
        [`pet${petNumber}.isEmotionalSupport`]: pet?.isEmotionalSupport ? 'Yes' : 'No',
        [`pet${petNumber}.hasVaccinations`]: pet?.hasVaccinations ? 'Yes' : 'No',
        [`pet${petNumber}.notes`]: pet?.notes || ''
      };
      
      // Add owner info if available
      if (pet?.owner) {
        petVars[`pet${petNumber}.owner.fullName`] = pet.owner.fullName || '';
        petVars[`pet${petNumber}.owner.firstName`] = pet.owner.firstName || '';
        petVars[`pet${petNumber}.owner.lastName`] = pet.owner.lastName || '';
        petVars[`pet${petNumber}.owner.email`] = pet.owner.email || '';
      }
      
      return { ...acc, ...petVars };
    }, {}) : {}),

    // Occupant variables
    'occupant.firstName': occupant?.firstName || 'N/A',
    'occupant.lastName': occupant?.lastName || 'N/A',
    'occupant.age': occupant?.age || 'N/A',
    'occupant.relationship': occupant?.relationship || 'N/A',
    'occupant.notes': occupant?.notes || 'N/A',

    // Vehicles collection - only include if there are vehicles
    ...(Array.isArray(vehicles) && vehicles.length > 0 ? { 'vehicles': vehicles } : {}),
    'vehicles.count': Array.isArray(vehicles) ? vehicles.length : 0,
    
    // Individual vehicle variables (vehicle1.make, vehicle2.make, etc.)
    ...(Array.isArray(vehicles) ? vehicles.reduce((acc, vehicle, index) => {
      // Only include vehicle variables if the vehicle object exists and has data
      if (!vehicle) return acc;
      
      const vehicleNumber = index + 1;
      const vehicleVars = {
        [`vehicle${vehicleNumber}.make`]: vehicle.make || '',
        [`vehicle${vehicleNumber}.model`]: vehicle.model || '',
        [`vehicle${vehicleNumber}.licensePlate`]: vehicle.licensePlate || '',
        [`vehicle${vehicleNumber}.year`]: vehicle.year || '',
        [`vehicle${vehicleNumber}.color`]: vehicle.color || '',
        [`vehicle${vehicleNumber}.notes`]: vehicle.notes || ''
      };
      
      // Add owner info if available
      if (vehicle.owner) {
        vehicleVars[`vehicle${vehicleNumber}.owner`] = vehicle.owner || '';
      }
      
      return { ...acc, ...vehicleVars };
    }, {}) : {}),
    
    // Legacy/single vehicle variables (for backward compatibility)
    'vehicle.make': vehicle?.make || 'N/A',
    'vehicle.model': vehicle?.model || 'N/A',
    'vehicle.licensePlate': vehicle?.licensePlate || 'N/A',
    'vehicle.year': vehicle?.year || 'N/A',
    'vehicle.color': vehicle?.color || 'N/A',
    'vehicle.notes': vehicle?.notes || 'N/A',
      
    // Current date
    'currentDate': new Date().toLocaleDateString('en-US', dateOptions),
    
    // Legacy pet variables (for backward compatibility)
    ...(pet ? {
      'pet.name': pet?.name || 'N/A',
      'pet.type': pet?.type || 'N/A',
      'pet.breed': pet?.breed || 'N/A',
      'pet.sex': pet?.sex || 'N/A',
      'pet.age': pet?.age || 'N/A',
      'pet.weight': pet?.weight || 'N/A',
      'pet.isEmotionalSupport': pet?.isEmotionalSupport ? 'Yes' : 'No',
      'pet.hasVaccinations': pet?.hasVaccinations ? 'Yes' : 'No',
      'pet.notes': pet?.notes || 'N/A',
      'pet.owner.fullName': pet?.owner?.fullName || 'N/A',
      'pet.owner.firstName': pet?.owner?.firstName || 'N/A',
      'pet.owner.lastName': pet?.owner?.lastName || 'N/A',
      'pet.owner.email': pet?.owner?.email || 'N/A'
    } : {})
  };
};

/**
 * Creates default templates for a new account
 * @param {string} accountId - Account ID to create templates for
 * @param {string} userId - User ID creating the templates (for attribution)
 * @param {Object} options - Additional options
 * @param {Object} [options.transaction] - Sequelize transaction to use
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createDefaultTemplates = async (accountId, userId, options = {}) => {
  const shouldCommit = !options.transaction;
  const transaction = options.transaction || await sequelize.transaction();
  
  try {
    const templatesDir = path.join(__dirname, '..', 'templates');
    const templateFiles = fs.readdirSync(templatesDir)
      .filter(file => file.endsWith('.json'));
    
    const createdTemplates = [];
    
    for (const file of templateFiles) {
      try {
        const templateData = JSON.parse(
          fs.readFileSync(path.join(templatesDir, file), 'utf8')
        );
        
        // Create template with account ID and attribution
        const template = await Template.create({
          ...templateData,
          accountId,
          createdBy: userId,
          updatedBy: userId,
          isSystem: true // Mark as system template
        }, { transaction });
        
        createdTemplates.push(template);
        
        logger.info(`Created default template: ${templateData.name} for account ${accountId}`);
      } catch (templateError) {
        logger.error(`Error creating template from file ${file}:`, templateError);
        // Continue with other templates even if one fails
      }
    }
    
    if (shouldCommit) {
      await transaction.commit();
    }
    
    return {
      status: 201,
      payload: {
        success: true,
        message: `Created ${createdTemplates.length} default templates`,
        data: createdTemplates
      }
    };
  } catch (error) {
    if (shouldCommit) {
      await transaction.rollback();
    }
    
    logger.error('Error creating default templates:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create default templates',
        error: error.message
      }
    };
  }
};

module.exports = {
  createTemplate,
  getAllTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate,
  getSystemVariables,
  replaceVariablesInTemplate,
  formatMoney,
  generateStandardVariables,
  createDefaultTemplates
};

const stripe = require('stripe');
const { Account } = require('../models');
const logger = require('../utils/logger');

/**
 * Gets a Stripe instance configured with account-specific API key
 * Requires the account to have a Stripe secret key configured
 * 
 * @param {string} accountId - The account ID to get Stripe instance for
 * @returns {Promise<Object>} - Stripe instance
 * @throws {Error} - If accountId is not provided or account doesn't have a Stripe key
 */
async function getStripeInstanceForAccount(accountId) {
  try {
    if (!accountId) {
      throw new Error('Account ID is required to initialize Stripe');
    }
    
    // Find the account and check if it has a custom Stripe key
    const account = await Account.findByPk(accountId, {
      attributes: ['stripeSecretKey']
    });
    
    if (!account) {
      throw new Error(`Account ${accountId} not found`);
    }
    
    if (!account.stripeSecretKey) {
      throw new Error(`Account ${accountId} does not have a Stripe secret key configured`);
    }
    
    return stripe(account.stripeSecretKey);
  } catch (error) {
    logger.error('Error getting Stripe instance for account', {
      error: error.message,
      accountId
    });
    throw error;
  }
}

/**
 * Gets the Stripe publishable key for an account
 * Requires the account to have a Stripe publishable key configured
 * 
 * @param {string} accountId - The account ID to get publishable key for
 * @returns {Promise<string>} - Stripe publishable key
 * @throws {Error} - If accountId is not provided or account doesn't have a publishable key
 */
async function getStripePublishableKeyForAccount(accountId) {
  try {
    if (!accountId) {
      throw new Error('Account ID is required to get Stripe publishable key');
    }
    
    const account = await Account.findByPk(accountId, {
      attributes: ['stripePublishableKey']
    });
    
    if (!account) {
      throw new Error(`Account ${accountId} not found`);
    }
    
    if (!account.stripePublishableKey) {
      throw new Error(`Account ${accountId} does not have a Stripe publishable key configured`);
    }
    
    return account.stripePublishableKey;
  } catch (error) {
    logger.error('Error getting Stripe publishable key for account', {
      error: error.message,
      accountId
    });
    throw error;
  }
}

module.exports = {
  getStripeInstanceForAccount,
  getStripePublishableKeyForAccount
};

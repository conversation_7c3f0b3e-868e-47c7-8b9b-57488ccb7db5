// services/fileService.js
const { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { 
  sequelize, 
  PropertyFile, 
  HOAFIle, 
  InsuranceFile, 
  LoanFile, 
  TaxFile, 
  MaintenanceFile, 
  UtilityFile, 
  InventoryFile, 
  LeaseFile,
  UserFile 
} = require('../models');
const logger = require('../utils/logger');

const s3Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CF_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.CF_ACCESS_KEY_ID,
    secretAccessKey: process.env.CF_SECRET_ACCESS_KEY,
  },
});

const bucketName = process.env.CF_BUCKET_NAME;

/**
 * Get a signed URL for a file
 * @param {string} fileKey - The S3 key of the file
 * @param {number} [expiresIn=3600] - Expiration time in seconds
 * @returns {Promise<string>} - Returns a signed URL
 */
const getSignedFileUrl = async (fileKey, expiresIn = 3600) => {
  try {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });
    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    logger.error('Error generating signed URL:', error);
    throw error;
  }
};

/**
 * Upload a file to S3 and save its metadata to the appropriate model's file table
 * @param {Object} file - File object containing buffer, originalname, mimetype, and size
 * @param {string} modelName - Model name (HOA, Insurance, Loan, Tax, Property, Maintenance)
 * @param {string} modelId - ID of the model instance this file belongs to
 * @param {string} userId - ID of the user uploading the file
 * @param {string} [description=''] - Optional file description
 * @returns {Promise<Object>} - Returns status and payload with file details
 */
const uploadModelFile = async (file, modelName, modelId, userId, description = '') => {
  // Use provided key or generate a new one
  const fileKey = file.key || `${modelName.toLowerCase()}s/${modelId}/${Date.now()}-${file.originalname}`;
  
  logger.info(`Uploading file with key: ${fileKey}, model: ${modelName}, modelId: ${modelId}`);
  
  const modelFileMap = {
    'Property': PropertyFile,
    'HOA': HOAFIle,
    'Insurance': InsuranceFile,
    'Loan': LoanFile,
    'Tax': TaxFile,
    'Maintenance': MaintenanceFile,
    'Utility': UtilityFile,
    'Inventory': InventoryFile,
    'Lease': LeaseFile,
    'User': UserFile,
  };

  const modelFile = modelFileMap[modelName];
  if (!modelFile) {
    throw new Error(`Unsupported model type: ${modelName}`);
  }

  // Upload to R2
  const uploadParams = {
    Bucket: bucketName,
    Key: fileKey,
    Body: file.buffer,
    ContentType: file.mimetype,
    Metadata: {
      'original-filename': file.originalname,
      'uploaded-by': userId,
    },
  };

  try {
    logger.debug('Uploading file to S3...');
    const uploadResult = await s3Client.send(new PutObjectCommand(uploadParams));
    logger.debug('File uploaded to S3 successfully', { uploadResult });

    // Special case for Maintenance files which use 'ticketId' as the foreign key
    const fileData = {
      fileName: file.originalname,
      fileKey,
      fileType: file.mimetype,
      fileSize: file.size,
      description,
      uploadedBy: userId,
    };

    // Set the appropriate foreign key based on model type
    if (modelName === 'Maintenance') {
      fileData.ticketId = modelId;
    } else {
      fileData[`${modelName.toLowerCase()}Id`] = modelId;
    }

    const fileRecord = await modelFile.create(fileData);
    
    logger.debug('File record created successfully:', JSON.stringify(fileRecord.toJSON(), null, 2));

    return {
      status: 201,
      payload: {
        success: true,
        fileKey: fileRecord.fileKey,
        fileId: fileRecord.id,
        fileName: fileRecord.fileName,
        fileType: fileRecord.fileType,
        fileSize: fileRecord.fileSize,
        createdAt: fileRecord.createdAt
      }
    };
  } catch (error) {
    logger.error(`Error uploading ${modelName} file:`, error);
    
    // Attempt to clean up if the database record was created but the upload failed
    try {
      await s3Client.send(new DeleteObjectCommand({
        Bucket: bucketName,
        Key: fileKey,
      }));
    } catch (cleanupError) {
      logger.error('Error cleaning up failed upload:', cleanupError);
    }
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Error uploading file',
        error: error.message
      }
    };
  }
};

/**
 * Delete a file from S3 and its metadata from the appropriate model's file table
 * @param {string} modelName - Model name (HOA, Insurance, Loan, Tax, Property, Maintenance)
 * @param {string} fileId - ID of the file to delete
 * @param {string} userId - ID of the user performing the deletion
 * @returns {Promise<Object>} - Returns status and payload
 */
const deleteModelFile = async (modelName, fileKey, userId) => {
  logger.debug(`Deleting file with key: ${fileKey}, model: ${modelName}, userId: ${userId}`);
  
  const modelFileMap = {
    'Property': PropertyFile,
    'HOA': HOAFIle,
    'Insurance': InsuranceFile,
    'Loan': LoanFile,
    'Tax': TaxFile,
    'Maintenance': MaintenanceFile,
    'Utility': UtilityFile,
    'Inventory': InventoryFile,
    'Lease': LeaseFile,
    'User': UserFile,
    'UserFile': UserFile, // Also support direct UserFile model name
  };

  const modelFile = modelFileMap[modelName];
  if (!modelFile) {
    throw new Error(`Unsupported model type: ${modelName}`);
  }

  const queryOptions = {
    where: { fileKey },
  };

  const t = await sequelize.transaction();
  
  try {
    // First find the file record
    const fileRecord = await modelFile.findOne(queryOptions);
    if (!fileRecord) {
      logger.warn(`File not found with key: ${fileKey} for model: ${modelName}`);
      return { 
        status: 404, 
        payload: { 
          success: false,
          message: 'File not found' 
        } 
      };
    }
    
    logger.debug('Found file record to delete:', JSON.stringify(fileRecord.toJSON(), null, 2));

   

    // Delete from S3
    try {
      logger.debug(`Deleting file from S3: ${fileRecord.fileKey}`);
      await s3Client.send(new DeleteObjectCommand({
        Bucket: bucketName,
        Key: fileRecord.fileKey,
      }));
      logger.debug('Successfully deleted file from S3');
    } catch (s3Error) {
      logger.error('Error deleting file from S3:', s3Error);
      // Continue with deleting the DB record even if S3 delete fails
    }

    // Delete the record from the database
    await fileRecord.destroy({ transaction: t });
    logger.debug('Successfully deleted file record from database');

    // Commit the transaction
    await t.commit();

    return {
      status: 200,
      payload: { 
        success: true,
        message: 'File deleted successfully',
        fileKey: fileRecord.fileKey
      },
    };
  } catch (error) {
    await t.rollback();
    logger.error(`Error deleting ${modelName} file:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete file',
        error: error.message,
      },
    };
  }
};

/**
 * Get files for a specific model instance
 * @param {string} modelName - Model name (HOA, Insurance, Loan, Tax, Property, Maintenance)
 * @param {string} modelId - ID of the model instance
 * @returns {Promise<Array>} - Returns array of file records
 */
const getModelFiles = async (modelName, modelId) => {
  try {
    const modelFileMap = {
      'Property': PropertyFile,
      'HOA': HOAFIle,
      'Insurance': InsuranceFile,
      'Loan': LoanFile,
      'Tax': TaxFile,
      'Maintenance': MaintenanceFile,
      'Utility': UtilityFile,
      'Inventory': InventoryFile,
      'Lease': LeaseFile,
      'User': UserFile,
    };

    const modelFile = modelFileMap[modelName];
    if (!modelFile) {
      return {
        status: 400,
        payload: {
          success: false,
          message: `Unsupported model type: ${modelName}`
        }
      };
    }

    const files = await modelFile.findAll({
      where: { [`${modelName.toLowerCase()}Id`]: modelId },
      order: [['createdAt', 'DESC']],
    });

    // Generate signed URLs for each file
    const filesWithUrls = await Promise.all(
      files.map(async (file) => {
        const url = await getSignedFileUrl(file.fileKey);
        return {
          ...file.toJSON(),
          url,
        };
      })
    );

    return {
      status: 200,
      payload: {
        success: true,
        data: filesWithUrls
      }
    };
  } catch (error) {
    logger.error(`Error getting ${modelName} files:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to retrieve files',
        error: error.message
      }
    };
  }
};

/**
 * Get a file by ID with model information
 * @param {string} fileId - ID of the file to retrieve
 * @returns {Promise<Object>} - Returns file with model information or null if not found
 */
const getFileById = async (fileId) => {
  try {
    // Try to find the file in each model
    const models = [
      { name: 'Property', model: PropertyFile },
      { name: 'HOA', model: HOAFIle },
      { name: 'Insurance', model: InsuranceFile },
      { name: 'Loan', model: LoanFile },
      { name: 'Tax', model: TaxFile },
      { name: 'Maintenance', model: MaintenanceFile },
      { name: 'Utility', model: UtilityFile },
      { name: 'Inventory', model: InventoryFile },
      { name: 'Lease', model: LeaseFile },
      { name: 'User', model: UserFile },
    ];

    // Search through each model to find the file
    for (const { name, model } of models) {
      const file = await model.findByPk(fileId);
      if (file) {
        return {
          ...file.toJSON(),
          modelName: name,
          modelId: file[`${name.toLowerCase()}Id`]
        };
      }
    }
    
    return null; // File not found in any model
  } catch (error) {
    logger.error('Error getting file by ID:', error);
    throw error; // Let the controller handle the error
  }
};

/**
 * Check if a user has ownership of a file
 * @param {Object} file - File object
 * @param {string} userId - ID of the user
 * @returns {Promise<boolean>} - Returns true if the user has ownership, false otherwise
 */
const checkFileOwnership = async (file, userId) => {
  // Check if user is admin, property owner, or file uploader
  const [isAdmin, isPropertyOwner] = await Promise.all([
    // Check if user is admin
    isUserAdmin(userId),
    // Check if user is property owner
    isPropertyOwner(file.propertyId, userId),
  ]);

  return isAdmin || isPropertyOwner || file.uploadedBy === userId;
};

/**
 * Check if a user is an admin
 * @param {string} userId - ID of the user
 * @returns {Promise<boolean>} - Returns true if the user is an admin, false otherwise
 */
const isUserAdmin = async (userId) => {
  // Implement your admin check logic
  return false;
};

/**
 * Check if a user is the owner of a property
 * @param {string} propertyId - ID of the property
 * @param {string} userId - ID of the user
 * @returns {Promise<boolean>} - Returns true if the user is the owner, false otherwise
 */
const isPropertyOwner = async (propertyId, userId) => {
  // Check if user is owner of the property
  const propertyUser = await PropertyUser.findOne({
    where: {
      propertyId,
      userId,
    },
    include: [{
      model: Role,
      where: { name: 'owner' },
      required: true,
    }],
  });

  return !!propertyUser;
};

module.exports = {
  uploadModelFile,
  deleteModelFile,
  getSignedFileUrl,
  getModelFiles,
  getFileById,
  checkFileOwnership,
  isUserAdmin,
  isPropertyOwner,
};
const { Property, User, AccountUser, Payment, Tax, Loan, Insurance, Utility, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Get account statistics including total properties and total tenants
 * @param {string} accountId - The account ID to get statistics for
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with stats
 */
const getAccountStats = async (accountId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Get total properties count for the account
    const totalProperties = await Property.count({
      where: { accountId },
      transaction
    });

    // Get total active users with tenant role for the account
    const totalTenants = await AccountUser.count({
      where: { 
        accountId,
        [Op.or]: [
          { primaryRole: 'tenant' },
          { 
            additionalRoles: {
              [Op.overlap]: ['tenant']
            }
          }
        ]
      },
      include: [
        {
          model: User,
          as: 'user',
          required: true,
          // No need for isActive check as paranoid: true handles soft deletes
        }
      ],
      transaction,
      // This ensures we only count non-deleted users
      paranoid: true
    });

    // Calculate total revenue from completed payments
    const totalRevenue = await Payment.sum('amount', {
      where: {
        accountId,
        status: 'completed',
      },
      transaction
    }) || 0; // Default to 0 if no payments found

    // Calculate total paid taxes
    const totalTaxesPaid = await Tax.sum('amount', {
      where: {
        accountId,
        status: 'paid',
      },
      transaction
    }) || 0;

    // Calculate total loan amounts
    const totalLoanAmount = await Loan.sum('loanAmount', {
      where: {
        accountId
      },
      transaction
    }) || 0;

    // Calculate total insurance premiums
    const totalInsurancePremiums = await Insurance.sum('premiumAmount', {
      where: {
        accountId,
        isActive: true
      },
      transaction
    }) || 0;

    // Calculate financial summary
    const financialSummary = {
      totalRevenue,
      totalTaxesPaid,
      totalLoanAmount,
      totalInsurancePremiums,
      netIncome: totalRevenue - totalTaxesPaid
    };

    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: {
          totalProperties,
          totalTenants,
          financialSummary
        }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error fetching account stats:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch account statistics',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

module.exports = {
  getAccountStats
};

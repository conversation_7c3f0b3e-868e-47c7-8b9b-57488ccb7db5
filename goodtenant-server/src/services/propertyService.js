const { Property, User, PropertyUser, Account, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Get all properties with pagination and filtering
 * @param {Object} options - Query options (page, limit, search, etc.)
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllProperties = async (options = {}, userId) => {
  try {
    const { page = 1, limit = 10, search = '', propertyType = '', status = '' } = options;
    const offset = (page - 1) * limit;

    // Build the where clause for search
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { addressLine1: { [Op.like]: `%${search}%` } },
        { city: { [Op.like]: `%${search}%` } },
        { state: { [Op.like]: `%${search}%` } },
        { postalCode: { [Op.like]: `%${search}%` } }
      ];
    }

    if (propertyType) {
      whereClause.propertyType = propertyType;
    }

    if (status) {
      whereClause.status = status;
    }

    // Get properties where the user has access
    const { count, rows } = await Property.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: PropertyUser,
          as: 'propertyUsers',
          where: { userId },
          required: true,
          attributes: ['userId', 'propertyId'] // Include only necessary fields
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      distinct: true
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching properties:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch properties',
        error: error.message
      }
    };
  }
};

/**
 * Create a new property
 * @param {Object} propertyData - Property data including accountId
 * @param {string} userId - ID of the user creating the property
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createProperty = async (propertyData, userId) => {
    const transaction = await sequelize.transaction();
    
    try {
      logger.info('Starting property creation with data:', { propertyData, userId });
      
      // Validate price fields if any are provided
      const priceValidation = _validatePriceFields(propertyData);
      if (!priceValidation.isValid) {
        await transaction.rollback();
        logger.warn('Price validation failed:', priceValidation.message);
        return {
          status: 400,
          payload: {
            success: false,
            message: priceValidation.message
          }
        };
      }

      // Verify account exists and user has access to it
      logger.debug('Verifying account access for accountId:', propertyData.accountId);
      const account = await Account.findByPk(propertyData.accountId, { transaction });
      if (!account) {
        await transaction.rollback();
        logger.warn('Account not found:', propertyData.accountId);
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Account not found'
          }
        };
      }

      // If hoaId is provided, verify the HOA exists
      if (propertyData.hoaId) {
        logger.debug('Verifying HOA with ID:', propertyData.hoaId);
        const hoa = await sequelize.models.HOA.findByPk(propertyData.hoaId, { transaction });
        if (!hoa) {
          await transaction.rollback();
          logger.warn('HOA not found:', propertyData.hoaId);
          return {
            status: 400,
            payload: {
              success: false,
              message: 'HOA not found'
            }
          };
        }
      }

      // Create the property with account association
      logger.debug('Creating property with data:', propertyData);
      const property = await Property.create(propertyData, { transaction });
      logger.info('Property created successfully:', property.id);
      
      // Assign the creator as the owner of the property
      logger.debug('Assigning user as property owner', { userId, propertyId: property.id });
      await PropertyUser.create({
        propertyId: property.id,
        userId,
        isPrimary: true,
        startDate: new Date()
      }, { transaction });

      await transaction.commit();
      logger.info('Transaction committed successfully');

      // Get the full property with associations
      logger.debug('Fetching full property details for property:', property.id);
      const createdProperty = await getPropertyById(property.id, userId);
      logger.debug('getPropertyById response:', { status: createdProperty.status });
      
      if (createdProperty.status === 200 && createdProperty.payload && createdProperty.payload.data) {
        logger.info('Successfully created and retrieved property:', property.id);
        return {
          status: 201,
          payload: {
            success: true,
            data: createdProperty.payload.data
          }
        };
      } else {
        logger.error('Failed to retrieve created property', { 
          status: createdProperty.status,
          payload: createdProperty.payload 
        });
        return createdProperty;
      }
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating property:', {
        error: error.message,
        stack: error.stack,
        userId,
        propertyData
      });
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to create property',
          error: error.message
        }
      };
    }
  }

/**
 * Get property by ID with associated users and account
 * @param {string} propertyId - Property ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPropertyById = async (propertyId, userId) => {
    try {
      // First check if user has access to this property
      const hasAccess = await PropertyUser.findOne({
        where: {
          propertyId,
          userId
        },
        raw: true
      });

      if (!hasAccess) {
        return {
          status: 403,
          payload: {
            success: false,
            message: 'You do not have permission to access this property'
          }
        };
      }

      // Get property with all relevant associations
      const property = await Property.findByPk(propertyId, {
        include: [
          {
            model: PropertyUser,
            as: 'propertyUsers',
            attributes: ['userId', 'propertyId'],
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'email']
              }
            ]
          },
          {
            model: sequelize.models.HOA,
            as: 'hoa',
            attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'zipCode', 'country', 'website', 'phone', 'email']
          }
        ],
        attributes: {
          exclude: ['accountId', 'createdAt', 'updatedAt']
        }
      });

      if (!property) {
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Property not found'
          }
        };
      }

      // Format the response
      const propertyData = property.get({ plain: true });
      
      // Format users data
      propertyData.users = propertyData.propertyUsers.map(pu => ({
        id: pu.user.id,
        firstName: pu.user.firstName,
        lastName: pu.user.lastName,
        email: pu.user.email,
        isPrimary: pu.isPrimary
      }));
      
      // Remove the propertyUsers array as it's no longer needed
      delete propertyData.propertyUsers;

      return {
        status: 200,
        payload: {
          success: true,
          data: propertyData
        }
      };
    } catch (error) {
      logger.error('Error getting property:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to fetch property',
          error: error.message,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      };
    }
  }

/**
 * Get all properties for the current user with optional search and filters
 * @param {string} userId - ID of the user making the request
 * @param {Object} options - Query options (page, limit, search, propertyType, status)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPropertiesByAccount = async (userId, options = {}) => {
    try {
      const { 
        page = 1, 
        limit = 10,
        search = '',
        propertyType = '',
        status = ''
      } = options;
      
      const offset = (page - 1) * limit;

      // Build the where clause for search
      const whereClause = {};
      
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { addressLine1: { [Op.like]: `%${search}%` } },
          { city: { [Op.like]: `%${search}%` } },
          { state: { [Op.like]: `%${search}%` } },
          { postalCode: { [Op.like]: `%${search}%` } }
        ];
      }

      if (propertyType) {
        whereClause.propertyType = propertyType;
      }

      if (status) {
        whereClause.status = status;
      }

      // Get properties where the user has access
      const { count, rows } = await Property.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: PropertyUser,
            as: 'propertyUsers',
            where: { userId },
            required: true,
            attributes: ['userId', 'propertyId'],
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'firstName', 'lastName']
            }]
          }
        ],
        attributes: {
          include: ['createdAt'],
          exclude: ['accountId', 'updatedAt']
        },
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']],
        distinct: true
      });

      // Format the response to include only necessary user data
      const formattedRows = rows.map(property => {
        const propertyData = property.get({ plain: true });
        
        // Remove the createdAt field from the response
        const { createdAt, propertyUsers, ...rest } = propertyData;
        
        // Extract and format user data
        rest.users = propertyUsers.map(pu => ({
          id: pu.user.id,
          firstName: pu.user.firstName,
          lastName: pu.user.lastName,
          isPrimary: pu.isPrimary
        }));
        
        return rest;
      });

      const totalPages = Math.ceil(count / limit);

      return {
        status: 200,
        payload: {
          success: true,
          data: formattedRows,
          pagination: {
            total: count,
            totalPages,
            currentPage: parseInt(page),
            hasNextPage: page < totalPages,
            hasPreviousPage: page > 1
          }
        }
      };
    } catch (error) {
      logger.error('Error getting properties by account:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to get properties',
          error: error.message
        }
      };
    }
  }

/**
 * Update a property
 * @param {string} propertyId - Property ID to update
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateProperty = async (propertyId, updateData, userId) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Validate price fields if any are being updated
      const priceValidation = _validatePriceFields(updateData);
      if (!priceValidation.isValid) {
        await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: priceValidation.message
          }
        };
      }

      // Check if user has permission to update this property
      // Note: Permission checking should now be done at the account level
      
      // If hoaId is being updated, verify the HOA exists
      if (updateData.hoaId !== undefined) {
        if (updateData.hoaId === null) {
          // Allow setting hoaId to null to remove the association
          updateData.hoaId = null;
        } else {
          const hoa = await sequelize.models.HOA.findByPk(updateData.hoaId, { transaction });
          if (!hoa) {
            await transaction.rollback();
            return {
              status: 400,
              payload: {
                success: false,
                message: 'HOA not found'
              }
            };
          }
        }
      }

      // If accountId is being updated, verify the new account exists and user has access
      if (updateData.accountId) {
        const account = await Account.findByPk(updateData.accountId, { transaction });
        if (!account) {
          await transaction.rollback();
          return {
            status: 404,
            payload: {
              success: false,
              message: 'Account not found'
            }
          };
        }
      }

      const property = await Property.findByPk(propertyId, { transaction });
      
      if (!property) {
        await transaction.rollback();
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Property not found'
          }
        };
      }

      await property.update(updateData, { transaction });
      await transaction.commit();

      // Get the updated property with associations
      const updatedProperty = await getPropertyById(propertyId, userId);
      
      return {
        status: 200,
        payload: {
          success: true,
          message: 'Property updated successfully',
          data: updatedProperty.payload.data
        }
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error updating property:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to update property',
          error: error.message
        }
      };
    }
  }

/**
 * Delete a property
 * @param {string} propertyId - Property ID to delete
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteProperty = async (propertyId, userId) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Check if user is either an owner of the property or an account owner
      // Note: Permission checking should now be done at the account level
      
      const property = await Property.findByPk(propertyId, { transaction });
      
      if (!property) {
        await transaction.rollback();
        return {
          status: 404,
          payload: {
            success: false,
            message: 'Property not found'
          }
        };
      }

      // Delete property users first (due to foreign key constraints)
      await PropertyUser.destroy({
        where: { propertyId },
        transaction
      });

      // Then delete the property
      await property.destroy({ transaction });
      await transaction.commit();

      return {
        status: 200,
        payload: {
          success: true,
          message: 'Property deleted successfully'
        }
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error deleting property:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to delete property',
          error: error.message
        }
      };
    }
  }

/**
 * Get all users associated with a property
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (pagination, filters, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPropertyUsers = async (propertyId, options = {}) => {
    try {
      const { page = 1, limit = 10 } = options;
      const offset = (page - 1) * limit;

      const { count, rows: propertyUsers } = await PropertyUser.findAndCountAll({
        where: { propertyId },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
          }
        ],
        offset,
        limit: parseInt(limit, 10),
        distinct: true,
        order: [['createdAt', 'DESC']]
      });

      // Transform the data to flatten the user object
      const users = propertyUsers.map(pu => ({
        ...pu.user.get({ plain: true }),
        isPrimary: pu.isPrimary,
        startDate: pu.startDate,
        endDate: pu.endDate,
        notes: pu.notes,
        addedAt: pu.createdAt
      }));

      return {
        status: 200,
        payload: {
          success: true,
          data: users,
          pagination: {
            total: count,
            page: parseInt(page, 10),
            totalPages: Math.ceil(count / limit),
            limit: parseInt(limit, 10)
          }
        }
      };
    } catch (error) {
      logger.error('Error getting property users:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to fetch property users',
          error: error.message
        }
      };
    }
  };

/**
 * Get all properties for a user
 * @param {string} userId - User ID
 * @param {Object} options - Query options (pagination, filters, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUserProperties = async (userId, options = {}) => {
    try {
      const { page = 1, limit = 10 } = options;
      const offset = (page - 1) * limit;

      const { count, rows: properties } = await Property.findAndCountAll({
        include: [
          {
            model: PropertyUser,
            where: { userId },
            attributes: [],
            required: true
          }
        ],
        offset,
        limit: parseInt(limit, 10),
        distinct: true,
        order: [['createdAt', 'DESC']]
      });

      return {
        status: 200,
        payload: {
          success: true,
          data: properties,
          pagination: {
            total: count,
            page: parseInt(page, 10),
            totalPages: Math.ceil(count / limit),
            limit: parseInt(limit, 10)
          }
        }
      };
    } catch (error) {
      logger.error('Error getting user properties:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to fetch user properties',
          error: error.message
        }
      };
    }
  }

/**
 * Check if a user has access to a property
 * @param {string} userId - User ID
 * @param {string} propertyId - Property ID
 * @returns {Promise<boolean>} - Whether the user has access
 */
const userHasAccess = async (userId, propertyId) => {
    try {
      const count = await PropertyUser.count({
        where: {
          userId,
          propertyId
        }
      });

      return count > 0;
    } catch (error) {
      logger.error('Error checking user access:', error);
      return false;
    }
  }

/**
 * Add a user to a property
 * @param {string} propertyId - Property ID
 * @param {string} userId - User ID to add
 * @param {Object} [options] - Additional options for the association
 * @param {boolean} [options.isPrimary=false] - Whether this is the primary property for the user
 * @param {string} currentUserId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const addUserToProperty = async (propertyId, userId, options = {}, currentUserId) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Check if current user has permission to add users
      // Note: Permission checking should now be done at the account level
      
      // Check if user is already assigned to this property
      const existingAssignment = await PropertyUser.findOne({
        where: {
          propertyId,
          userId
        },
        transaction
      });

      if (existingAssignment) {
        await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'User is already associated with this property'
          }
        };
      }

      // Create the property user association
      await PropertyUser.create({
        propertyId,
        userId,
        isPrimary: options.isPrimary || false,
        startDate: options.startDate || new Date(),
        endDate: options.endDate || null,
        notes: options.notes || null
      }, { transaction });

      await transaction.commit();

      return {
        status: 200,
        payload: {
          success: true,
          message: 'User added to property successfully'
        }
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error adding user to property:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to add user to property',
          error: error.message
        }
      };
    }
  }

/**
 * Remove a user from a property
 * @param {string} propertyId - Property ID
 * @param {string} userId - User ID to remove
 * @param {string} currentUserId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const removeUserFromProperty = async (propertyId, userId, currentUserId) => {
    const transaction = await sequelize.transaction();
    
    try {
      // Check if current user has permission to remove users
      // Note: Permission checking should now be done at the account level
      
      // Check if trying to remove the last owner
      // Note: This check is no longer necessary since role-related functionality has been moved to the AccountUser model
      
      // Remove all role assignments for this user and property
      await PropertyUser.destroy({
        where: {
          propertyId,
          userId
        },
        transaction
      });

      await transaction.commit();

      return {
        status: 200,
        payload: {
          success: true,
          message: 'User removed from property successfully'
        }
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error removing user from property:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to remove user from property',
          error: error.message
        }
      };
    }
  }

/**
 * Validate price fields in property data
 * @private
 * @param {Object} propertyData - Property data to validate
 * @returns {{isValid: boolean, message: string|null}} - Validation result
 */
const _validatePriceFields = (propertyData) => {
    const priceFields = ['priceAmount', 'priceCurrency', 'priceInterval'];
    const hasAnyPriceField = priceFields.some(field => field in propertyData);
    
    // If no price fields are being set, validation passes
    if (!hasAnyPriceField) return { isValid: true };
    
    // Check if all required price fields are present
    const missingFields = priceFields.filter(field => !(field in propertyData));
    if (missingFields.length > 0) {
      return {
        isValid: false,
        message: `Missing required price fields: ${missingFields.join(', ')}`
      };
    }

    // Validate price amount
    if (propertyData.priceAmount !== undefined && (isNaN(propertyData.priceAmount) || propertyData.priceAmount < 0)) {
      return {
        isValid: false,
        message: 'Price amount must be a non-negative number'
      };
    }

    // Validate currency format (3 uppercase letters)
    if (propertyData.priceCurrency && !/^[A-Z]{3}$/.test(propertyData.priceCurrency)) {
      return {
        isValid: false,
        message: 'Currency must be a 3-letter uppercase ISO currency code (e.g., USD, EUR)'
      };
    }

    // Validate price interval
    const validIntervals = ['hour', 'day', 'week', 'month', 'year'];
    if (propertyData.priceInterval && !validIntervals.includes(propertyData.priceInterval)) {
      return {
        isValid: false,
        message: `Invalid price interval. Must be one of: ${validIntervals.join(', ')}`
      };
    }

    return { isValid: true };
  }


module.exports = {
  createProperty,
  getPropertyById,
  getAllProperties,
  getPropertiesByAccount,
  updateProperty,
  deleteProperty,
  getUserProperties,
  getPropertyUsers,
  addUserToProperty,
  removeUserFromProperty,
  userHasAccess
};

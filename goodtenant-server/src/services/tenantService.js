const { User, Property, Account, Role, TenantInvitation, PropertyUser, Lease, LeaseFile, sequelize } = require('../models');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');
const emailService = require('./emailService');
const config = require('../config');
const fileService = require('./fileService');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

/**
 * Create a new tenant invitation
 * @param {string} accountId - Account ID
 * @param {string} propertyId - Property ID
 * @param {Object} invitationData - Invitation data
 * @param {string} currentUserId - ID of user creating invitation
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createTenantInvitation = async (accountId, propertyId, invitationData, currentUserId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Validate property belongs to account
    const property = await Property.findOne({
      where: {
        id: propertyId,
        accountId
      },
      transaction
    });
    
    if (!property) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Property not found or does not belong to this account'
        }
      };
    }
    
    // Tenant role is now a direct string
    
    // Check if there's already a pending invitation for this email and property
    const existingInvitation = await TenantInvitation.findOne({
      where: {
        propertyId,
        email: invitationData.email.toLowerCase(),
        status: 'pending'
      },
      transaction
    });
    
    if (existingInvitation) {
      await transaction.rollback();
      return {
        status: 409,
        payload: {
          success: false,
          message: 'A pending invitation already exists for this email address'
        }
      };
    }
    
    // Create invitation
    const invitation = await TenantInvitation.create({
      ...invitationData,
      accountId,
      propertyId,
      role: 'tenant', // Use direct role string instead of roleId
      email: invitationData.email.toLowerCase(),
      createdBy: currentUserId,
      status: 'pending'
    }, { transaction });
    
    // Generate invitation link
    const baseUrl = config.clientUrl;
    const invitationLink = `${baseUrl}/en/tenant-onboarding/${invitation.token}`;
    
    // Send invitation email
    const emailResult = await sendInvitationEmail(invitation, property, invitationLink);
    
    if (!emailResult.success) {
      logger.error('Failed to send invitation email:', emailResult.error);
      // Continue despite email failure, but log it
    }
    
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        message: 'Tenant invitation created successfully',
        data: {
          invitation: {
            id: invitation.id,
            email: invitation.email,
            firstName: invitation.firstName,
            lastName: invitation.lastName,
            status: invitation.status,
            expiresAt: invitation.expiresAt,
            createdAt: invitation.createdAt
          },
          invitationLink
        }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating tenant invitation:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create tenant invitation',
        error: error.message
      }
    };
  }
};

/**
 * Send invitation email to tenant
 * @param {Object} invitation - Invitation data
 * @param {Object} property - Property data
 * @param {string} invitationLink - Invitation link with token
 * @returns {Promise<{success: boolean, error?: string}>} - Success status
 * @private
 */
const sendInvitationEmail = async (invitation, property, invitationLink) => {
  try {
    await emailService.sendTemplateEmail({
      to: invitation.email,
      subject: `You've been invited as a tenant for ${property.name}`,
      templateName: config.mailgun.templates.tenantInvitation,
      templateVars: {
        firstName: invitation.firstName,
        propertyName: property.name,
        propertyAddress: `${property.addressLine1}${property.addressLine2 ? ', ' + property.addressLine2 : ''}, ${property.city}, ${property.state} ${property.postalCode}`,
        invitationLink,
        expiresAt: invitation.expiresAt.toLocaleString(),
      }
    });
    return { success: true };
  } catch (error) {
    logger.error('Error sending invitation email:', error);
    return { 
      success: false,
      error: error.message 
    };
  }
};

/**
 * Get tenant invitations for a property
 * @param {string} accountId - Account ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPropertyInvitations = async (accountId, propertyId, options = {}) => {
  try {
    const { page = 1, limit = 10, status } = options;
    const offset = (page - 1) * limit;
    
    // Validate property belongs to account
    const property = await Property.findOne({
      where: {
        id: propertyId,
        accountId
      }
    });
    
    if (!property) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Property not found or does not belong to this account'
        }
      };
    }
    
    const whereClause = {
      propertyId,
      accountId
    };
    
    if (status) {
      whereClause.status = status;
    }
    
    const { count, rows } = await TenantInvitation.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      attributes: {
        exclude: ['roleId'] // Remove roleId as it's no longer used
      }
    });
    
    // Add role information to each invitation (role is now a direct field)
    const invitationsWithRole = rows.map(invitation => ({
      ...invitation.get({ plain: true }),
      role: {
        name: invitation.role // Role is now a direct field in the model
      }
    }));
    
    return {
      status: 200,
      payload: {
        success: true,
        data: invitationsWithRole,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error getting tenant invitations:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to get tenant invitations',
        error: error.message
      }
    };
  }
};

/**
 * Verify invitation token
 * @param {string} token - Invitation token
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const verifyInvitationToken = async (token) => {
  try {
    const invitation = await TenantInvitation.findOne({
      where: {
        token,
        status: 'pending',
        expiresAt: {
          [Op.gt]: new Date()
        }
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country']
        }
      ]
    });
    
    if (!invitation) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Invalid or expired invitation'
        }
      };
    }
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Valid invitation',
        data: {
          invitation: {
            id: invitation.id,
            email: invitation.email,
            firstName: invitation.firstName,
            lastName: invitation.lastName,
            expiresAt: invitation.expiresAt
          },
          property: invitation.property
        }
      }
    };
  } catch (error) {
    logger.error('Error verifying invitation token:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to verify invitation token',
        error: error.message
      }
    };
  }
};

/**
 * Complete tenant onboarding process
 * @param {string} token - Invitation token
 * @param {Object} userData - User data for account creation (including password, firstName, lastName, phoneNumber)
 * @param {Object} additionalData - Additional data (vehicles, pets, occupants)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const completeTenantOnboarding = async (token, userData, additionalData = {}) => {
  // Initialize response data object
  const responseData = {};
  const transaction = await sequelize.transaction();
  
  try {
    // Validate required user data
    const requiredFields = ['password', 'firstName', 'lastName'];
    const missingFields = requiredFields.filter(field => !userData[field]);
    
    if (missingFields.length > 0) {
      return {
        status: 400,
        payload: {
          success: false,
          message: `Missing required fields: ${missingFields.join(', ')}`
        }
      };
    }

    // Password validation
    if (userData.password.length < 8) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Password must be at least 8 characters long'
        }
      };
    }
    
    // Validate phone number format if provided
    if (userData.phoneNumber) {
      const phoneRegex = /^\+?[1-9]\d{1,14}$/; // E.164 format
      if (!phoneRegex.test(userData.phoneNumber)) {
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Please provide a valid phone number in E.164 format (e.g., +**********)'
          }
        };
      }
    }

    // Find and validate the invitation
    const invitation = await TenantInvitation.findOne({
      where: {
        token,
        status: 'pending',
        expiresAt: { [Op.gt]: new Date() }
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'accountId', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country'],
          include: [
            {
              model: User,
              as: 'users',
              attributes: ['id', 'firstName', 'lastName', 'email']
            }
          ]
        },
        // Role is stored directly in the TenantInvitation model as a string, not as a relation
      ],
      transaction
    });

    // Find associated lease and update status to 'active'
    let lease = null;
    let leaseData = null;
    
    if (invitation.propertyId) {
      // Find the lease for this property
      lease = await Lease.findOne({
        where: {
          propertyId: invitation.propertyId,
          status: 'draft' // Only update draft leases
        },
        include: [{
          model: User,
          as: 'tenants',
          where: { email: invitation.email },
          required: false
        }],
        transaction
      });
      
      if (lease) {
        // Update lease status to active
        await lease.update({ 
          status: 'active',
          startDate: new Date() // Optionally set start date to now
        }, { transaction });
        
        // If the user isn't already associated with this lease, add them
        if (lease.tenants.length === 0) {
          const user = await User.findOne({
            where: { email: invitation.email },
            transaction
          });
          
          if (user) {
            await lease.addTenant(user, { transaction });
            logger.info(`Added user ${user.id} to lease ${lease.id}`, {
              userId: user.id,
              leaseId: lease.id,
              email: invitation.email
            });
          }
        }
        
        leaseData = {
          id: lease.id,
          status: 'active',
          startDate: lease.startDate,
          endDate: lease.endDate
        };
        
        logger.info(`Lease ${lease.id} activated for property ${invitation.propertyId}`, { 
          leaseId: lease.id,
          propertyId: invitation.propertyId,
          status: 'active' 
        });
      } else {
        logger.warn(`No draft lease found for property ${invitation.propertyId} to activate`);
      }
    }

    // Check if user already exists with this email
    const existingUser = await User.findOne({
      where: { email: invitation.email },
      transaction
    });

    if (existingUser) {
      await transaction.rollback();
      return {
        status: 409,
        payload: {
          success: false,
          message: 'A user with this email already exists'
        }
      };
    }

    // Hash the provided password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    // Create user with the provided data
    const user = await User.create({
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: invitation.email,
      phoneNumber: userData.phoneNumber || null,
      password: hashedPassword,
      accountId: invitation.property.accountId,
      emailVerified: true
    }, { transaction });

    // Add user to account with tenant role
    await sequelize.models.AccountUser.create({
      accountId: invitation.property.accountId,
      userId: user.id,
      primaryRole: 'tenant',
      additionalRoles: []
    }, { transaction });

    // Associate user with property and role
    await PropertyUser.create({
      userId: user.id,
      propertyId: invitation.propertyId,
      role: invitation.role, // Use direct role field instead of roleId
      status: 'active',
      addedBy: invitation.createdBy
    }, { transaction });

    // Skip updating invitation status as requested
    // The invitation will remain in 'pending' status

    // Associate tenant with lease if applicable
    if (lease) {
      logger.info(`Associating tenant ${user.id} with lease ${lease.id}`);
      
      await sequelize.models.LeaseTenant.create({
        leaseId: lease.id,
        userId: user.id,
        isPrimaryTenant: true,
        moveInDate: new Date(),
        status: 'active'
      }, { transaction });
      
      logger.info(`Successfully associated tenant ${user.id} with lease ${lease.id}`);
      
      // Add leaseId to the response for the frontend to request the document if needed
      responseData.leaseId = lease.id;
    }

    // Process additional data if lease exists
    if (lease) {
      const { vehicles = [], pets = [], occupants = [] } = additionalData;
      
      // Create vehicles if provided
      if (vehicles.length > 0) {
        const { createVehicle } = require('./vehicleService');
        for (const vehicle of vehicles) {
          await createVehicle(
            user.id,
            { ...vehicle },
            { transaction }
          );
        }
      }
      
      // Create pets if provided
      if (pets.length > 0) {
        const { createPet } = require('./petService');
        for (const pet of pets) {
          await createPet(
            { ...pet, userId: user.id },
            user.id,
            { transaction }
          );
        }
      }
      
      // Create occupants if provided and track adult occupants (18+)
      const adultOccupants = [];
      if (occupants.length > 0) {
        const { createOccupant } = require('./occupantService');
        
        for (const occupant of occupants) {
          const createdOccupant = await createOccupant(
            { ...occupant, leaseId: lease.id },
            user.id,
            { transaction }
          );
          
          // Check if occupant is 18 or older using the age field
          if (occupant.age && occupant.age >= 18) {
            adultOccupants.push({
              firstName: occupant.firstName,
              lastName: occupant.lastName
            });
          }
        }
      }
      
      // Check if property has HOA by including it in the property query
      const propertyWithHoa = await Property.findOne({
        where: { id: invitation.property.id },
        include: [{
          model: sequelize.models.HOA,
          as: 'hoa',
          required: false
        }],
        transaction
      });

      responseData.additionalData = {
        vehicles: vehicles.length,
        pets: pets.length,
        occupants: occupants.length,
        hasHoa: !!propertyWithHoa?.hoa,
        adultOccupants: adultOccupants
      };
    }

    // Commit the transaction
    await transaction.commit();

    logger.info(`Successfully completed onboarding for user ${user.id}`);

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Tenant onboarding completed successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phoneNumber: user.phoneNumber
          },
          property: {
            id: invitation.property.id,
            name: invitation.property.name
          },
          lease: lease ? {
            leaseId: lease.id,
            ...(responseData.additionalData || {})
          } : null,
          message: lease ? 'Lease document is ready to view' : 'Tenant onboarded successfully without lease association'
        }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error completing tenant onboarding:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to complete tenant onboarding',
        error: error.message
      }
    };
  }
};

/**
 * Send welcome email to tenant with temporary password
 * @param {Object} user - User data
 * @param {string} temporaryPassword - Temporary password
 * @returns {Promise<{success: boolean, error?: string}>} - Success status
 * @private
 */
const sendWelcomeEmail = async (user, temporaryPassword) => {
  try {
    const loginUrl = `${config.clientUrl}/login`;
      
    await emailService.sendTemplateEmail({
      to: user.email,
      subject: `Welcome to GoodTenant`,
      templateName: config.mailgun.templates.tenantWelcome,
      templateVars: {
        firstName: user.firstName,
        lastName: user.lastName,
        loginUrl,
        email: user.email,
        temporaryPassword
      }
    });
    return { success: true };
  } catch (error) {
    logger.error('Error sending welcome email:', error);
    return { 
      success: false,
      error: error.message 
    };
  }
};

/**
 * Cancel a tenant invitation
 * @param {string} accountId - Account ID
 * @param {string} invitationId - Invitation ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const cancelInvitation = async (accountId, invitationId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const invitation = await TenantInvitation.findOne({
      where: {
        id: invitationId,
        accountId,
        status: 'pending'
      },
      transaction
    });
    
    if (!invitation) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Invitation not found or already processed'
        }
      };
    }
    
    await invitation.update({
      status: 'cancelled'
    }, { transaction });
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Invitation cancelled successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error cancelling invitation:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to cancel invitation',
        error: error.message
      }
    };
  }
};

/**
 * Resend invitation email
 * @param {string} accountId - Account ID
 * @param {string} invitationId - Invitation ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const resendInvitation = async (accountId, invitationId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const invitation = await TenantInvitation.findOne({
      where: {
        id: invitationId,
        accountId,
        status: 'pending'
      },
      include: [
        {
          model: Property,
          as: 'property'
        }
      ],
      transaction
    });
    
    if (!invitation) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Invitation not found or already processed'
        }
      };
    }
    
    // Generate new token and reset expiration
    invitation.token = uuidv4();
    invitation.expiresAt = new Date(Date.now() + 48 * 60 * 60 * 1000);
    
    await invitation.save({ transaction });
    
    // Generate invitation link
    const baseUrl = config.clientUrl;
    const invitationLink = `${baseUrl}/tenant-onboarding?token=${invitation.token}`;
    
    // Send invitation email
    const emailResult = await sendInvitationEmail(invitation, invitation.property, invitationLink);
    
    if (!emailResult.success) {
      logger.error('Failed to send invitation email:', emailResult.error);
      // Continue despite email failure, but log it
    }
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Invitation resent successfully',
        data: {
          invitation: {
            id: invitation.id,
            email: invitation.email,
            expiresAt: invitation.expiresAt
          }
        }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error resending invitation:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to resend invitation',
        error: error.message
      }
    };
  }
};

/**
 * Sign a document with signature data from React Signature Canvas
 * @param {string} documentId - Document ID to sign
 * @param {Object} signatureData - Signature data { image: string, positions: Array<{x, y, width, height, pageIndex}> }
 * @param {string} userId - ID of the user signing the document
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const signDocument = async (documentId, signatureData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Find the document
    const document = await Document.findByPk(documentId, {
      include: [
        {
          model: User,
          as: 'tenant',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'landlord',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'fullAddress']
        }
      ],
      transaction
    });
    
    if (!document) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Document not found'
        }
      };
    }
    
    // Check if the user has permission to sign
    const isLandlord = document.landlordId === userId;
    const isTenant = document.tenantId === userId;
    
    if (!isLandlord && !isTenant) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'You do not have permission to sign this document'
        }
      };
    }
    
    // Get document file content from storage
    const fileResult = await fileService.getFileContent(document.fileKey);
    
    if (!fileResult.payload.success) {
      await transaction.rollback();
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to retrieve document content',
          error: fileResult.payload.message
        }
      };
    }
    
    const pdfBytes = fileResult.payload.data;
    
    // Process and add signatures to the PDF
    const signatureImageBuffer = Buffer.from(
      signatureData.image.replace(/^data:image\/png;base64,/, ''), 
      'base64'
    );
    
    // Process each signature position
    let updatedPdfBytes = pdfBytes;
    
    for (const position of signatureData.positions) {
      // Add the signature to the PDF
      const signatureResult = await pdfService.addSignatureToPDF(
        updatedPdfBytes, 
        {
          image: signatureImageBuffer,
          x: position.x,
          y: position.y,
          width: position.width || 200,
          height: position.height || 100,
          pageIndex: position.pageIndex || 0
        }
      );
      
      if (!signatureResult.payload.success) {
        await transaction.rollback();
        return {
          status: 500,
          payload: {
            success: false,
            message: 'Failed to add signature to document',
            error: signatureResult.payload.message
          }
        };
      }
      
      // Update the PDF for next iteration
      updatedPdfBytes = signatureResult.payload.data;
    }
    
    // Update the file in storage
    const updatedFile = {
      buffer: updatedPdfBytes,
      originalname: `signed_${document.name.replace(/\s+/g, '_').toLowerCase()}.pdf`,
      mimetype: 'application/pdf',
      size: updatedPdfBytes.length
    };
    
    const uploadResult = await fileService.updateFile(
      document.fileKey,
      updatedFile,
      'Document',
      documentId,
      userId,
      'Updated with signature'
    );
    
    if (!uploadResult.payload.success) {
      await transaction.rollback();
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to update signed document',
          error: uploadResult.payload.message
        }
      };
    }
    
    // Update document status
    const signatureField = isLandlord ? 'landlordSignedAt' : 'tenantSignedAt';
    const statusUpdate = {};
    statusUpdate[signatureField] = new Date();
    
    // If both parties have signed, update the status to COMPLETED
    if (
      (isLandlord && document.tenantSignedAt) || 
      (isTenant && document.landlordSignedAt)
    ) {
      statusUpdate.status = 'COMPLETED';
      statusUpdate.completedAt = new Date();
    }
    
    await document.update(statusUpdate, { transaction });
    
    // Check if all required documents are signed and update tenant status if needed
    if (statusUpdate.status === 'COMPLETED') {
      const pendingDocuments = await Document.count({
        where: {
          tenantId: document.tenantId,
          status: 'PENDING_SIGNATURE'
        }
      });
      
      if (pendingDocuments === 0) {
        // Update tenant status to ACTIVE if all documents are signed
        const propertyUser = await PropertyUser.findOne({
          where: {
            userId: document.tenantId,
            propertyId: document.propertyId
          },
          transaction
        });
        
        if (propertyUser) {
          await propertyUser.update({
            status: 'active',
            updatedBy: userId
          }, { transaction });
        }
      }
    }
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: `Document ${isLandlord ? 'landlord signature' : 'tenant signature'} added successfully`,
        data: {
          documentId,
          url: uploadResult.payload.data.url,
          status: document.status,
          signedBy: isLandlord ? 'landlord' : 'tenant',
          isCompleted: statusUpdate.status === 'COMPLETED'
        }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error signing document:', error);
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to sign document',
        error: error.message
      }
    };
  }
};

/**
 * Sign a lease document
 * @param {string} leaseId - Lease ID to sign
 * @param {Object} signatureData - Signature data from React Signature Canvas
 * @param {string} userId - ID of the user signing
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const signLeaseDocument = async (leaseId, signatureData, userId) => {
  // Delegate to the signDocument function with lease document ID
  return signDocument(leaseId, signatureData, userId);
};

/**
 * Finalize tenant onboarding by updating the invitation status to 'accepted'
 * @param {string} invitationId - The ID of the tenant invitation
 * @param {string} userId - The ID of the user finalizing the onboarding
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
/**
 * Finalize tenant onboarding by invitation token
 * @param {string} token - The invitation token
 * @param {string} [userId] - Optional user ID if available
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const finalizeTenantOnboardingByToken = async (token, userId = null) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Find the invitation by token
    const invitation = await TenantInvitation.findOne({
      where: { token },
      transaction
    });

    if (!invitation) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Invitation not found or token is invalid'
        }
      };
    }

    // Check if invitation is already accepted
    if (invitation.status === 'accepted') {
      await transaction.rollback();
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Invitation has already been accepted'
        }
      };
    }

    // Find the associated lease for the property and check if the invited user is a tenant
    const user = await User.findOne({
      where: { email: invitation.email },
      transaction
    });

    let leaseData = null;
    
    if (user && invitation.propertyId) {
      // Find the lease for this property where the user is a tenant
      const lease = await Lease.findOne({
        where: { 
          propertyId: invitation.propertyId,
          status: 'draft' // Only update draft leases
        },
        include: [{
          model: User,
          as: 'tenants',
          where: { id: user.id },
          required: true
        }],
        transaction
      });
      
      if (lease) {
        // Update lease status to active
        await lease.update({ 
          status: 'active',
          startDate: new Date() // Optionally set start date to now
        }, { transaction });
        
        leaseData = {
          leaseId: lease.id, 
          status: 'active',
          startDate: lease.startDate,
          endDate: lease.endDate
        };
        
        logger.info(`Lease ${lease.id} activated for property ${invitation.propertyId} during tenant onboarding`, { 
          leaseId: lease.id,
          propertyId: invitation.propertyId,
          userId: user.id,
          status: 'active' 
        });
      } else {
        logger.warn(`No draft lease found for property ${invitation.propertyId} and user ${user.id} to activate`);
      }
    }

    // Update the invitation status to accepted
    await invitation.update(
      { 
        status: 'accepted',
        acceptedAt: new Date(),
        // Only update acceptedBy if we have a user ID
        ...(userId && { acceptedBy: userId })
      },
      { transaction }
    );

    // Update user's email verification status if user exists
    if (user) {
      await user.update(
        { 
          isEmailVerified: true,
          emailVerificationToken: null // Clear the verification token if it exists
        },
        { transaction }
      );
      
      logger.info(`Email verified for user ${user.id} (${user.email}) during tenant onboarding`);
    }

    let response;
    try {
      await transaction.commit();
      
      logger.info(`Tenant onboarding finalized for invitation ${invitation.id} with token ${token}${leaseData ? ', lease activated' : ''}`);
      
      response = {
        status: 200,
        payload: {
          success: true,
          message: 'Tenant onboarding finalized successfully' + (leaseData ? ' and lease activated' : ''),
          data: {
            invitationId: invitation.id,
            status: 'accepted',
            acceptedAt: invitation.acceptedAt,
            ...(leaseData && { lease: leaseData })
          }
        }
      };
    } catch (commitError) {
      logger.error('Error committing transaction:', commitError);
      // Don't try to rollback here as the transaction might already be committed
      throw commitError;
    }
    
    return response;
  } catch (error) {
    logger.error('Error finalizing tenant onboarding by token:', error);
    
    // Only rollback if transaction is still active
    if (transaction && !transaction.finished) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        logger.error('Error rolling back transaction:', rollbackError);
      }
    }
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to finalize tenant onboarding',
        error: error.message
      }
    };
  }
};

/**
 * Finalize tenant onboarding by invitation ID
 * @param {string} invitationId - The ID of the tenant invitation
 * @param {string} [userId] - Optional user ID if available
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const finalizeTenantOnboarding = async (invitationId, userId = null) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Find the invitation
    const invitation = await TenantInvitation.findByPk(invitationId, {
      transaction
    });

    if (!invitation) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Invitation not found'
        }
      };
    }

    // Check if invitation is already accepted
    if (invitation.status === 'accepted') {
      await transaction.rollback();
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Invitation has already been accepted'
        }
      };
    }

    // Find the associated lease for the property and check if the invited user is a tenant
    const user = await User.findOne({
      where: { email: invitation.email },
      transaction
    });

    let leaseData = null;
    
    if (user && invitation.propertyId) {
      // Find the lease for this property where the user is a tenant
      const lease = await Lease.findOne({
        where: { 
          propertyId: invitation.propertyId,
          status: 'draft' // Only update draft leases
        },
        include: [{
          model: User,
          as: 'tenants',
          where: { id: user.id },
          required: true
        }],
        transaction
      });
      
      if (lease) {
        // Update lease status to active
        await lease.update({ 
          status: 'active',
          startDate: new Date() // Optionally set start date to now
        }, { transaction });
        
        leaseData = {
          leaseId: lease.id, 
          status: 'active',
          startDate: lease.startDate,
          endDate: lease.endDate
        };
        
        logger.info(`Lease ${lease.id} activated for property ${invitation.propertyId} during tenant onboarding`, { 
          leaseId: lease.id,
          propertyId: invitation.propertyId,
          userId: user.id,
          status: 'active' 
        });
      } else {
        logger.warn(`No draft lease found for property ${invitation.propertyId} and user ${user.id} to activate`);
      }
    }

    // Update the invitation status to accepted
    await invitation.update(
      { 
        status: 'accepted',
        acceptedAt: new Date()
      },
      { transaction }
    );

    let response;
    try {
      await transaction.commit();
      
      logger.info(`Tenant onboarding finalized for invitation ${invitationId} by user ${userId}${leaseData ? ', lease activated' : ''}`);
      
      response = {
        status: 200,
        payload: {
          success: true,
          message: 'Tenant onboarding finalized successfully' + (leaseData ? ' and lease activated' : ''),
          data: {
            invitationId: invitation.id,
            status: 'accepted',
            acceptedAt: invitation.acceptedAt,
            ...(leaseData && { lease: leaseData })
          }
        }
      };
    } catch (commitError) {
      logger.error('Error committing transaction:', commitError);
      // Don't try to rollback here as the transaction might already be committed
      throw commitError;
    }
    
    return response;
  } catch (error) {
    logger.error('Error finalizing tenant onboarding:', error);
    
    // Only rollback if transaction is still active
    if (transaction && !transaction.finished) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        logger.error('Error rolling back transaction:', rollbackError);
      }
    }
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to finalize tenant onboarding',
        error: error.message
      }
    };
  }
};

module.exports = {
  createTenantInvitation,
  verifyInvitationToken,
  completeTenantOnboarding,
  getPropertyInvitations,
  cancelInvitation,
  resendInvitation,
  signLeaseDocument,
  signDocument,
  finalizeTenantOnboarding,
  finalizeTenantOnboardingByToken
};

// goodtenant-server/src/services/eventService.js
const EventEmitter = require('events');
const { Op } = require('sequelize');
const notificationService = require('./notificationService');
const { User, AccountUser } = require('../models');
const logger = require('../utils/logger');

class NotificationEventEmitter extends EventEmitter {}
const notificationEvents = new NotificationEventEmitter();

// Handle maintenance events
notificationEvents.on('maintenance.created', async (data) => {
  try {
    logger.info('maintenance.created event received', { 
      maintenanceId: data.maintenance?.id,
      accountId: data.maintenance?.accountId,
      createdById: data.createdById 
    });

    const { maintenance, createdById } = data;
    
    // Skip if maintenance data is not complete
    if (!maintenance || !maintenance.accountId) {
      logger.warn('Incomplete maintenance data in maintenance.created event', { 
        maintenance: maintenance ? 'present' : 'missing',
        accountId: maintenance?.accountId || 'missing',
        createdById
      });
      return;
    }
    
    // Log the query we're about to make
    logger.info('Fetching managers for account', { 
      accountId: maintenance.accountId,
      roles: ['property_manager','account_owner']
    });

    // Get property managers/admins for this account
    const managers = await User.findAll({
      include: [{
        model: AccountUser,
        as: 'accountUsers',
        where: { 
          primaryRole: { [Op.in]: ['property_manager','account_owner'] },
          accountId: maintenance.accountId
        },
        required: true
      }]
    });

    logger.info(`Found ${managers.length} managers to notify`);

    // Create notifications
    const notifications = managers.map(user => ({
      userId: user.id,
      type: 'maintenance_request',
      title: 'New Maintenance Request',
      message: `New maintenance request has been created`,
      entityType: 'MAINTENANCE',
      entityId: maintenance.id,
      metadata: {
        priority: maintenance.priority,
        status: maintenance.status
      }
    }));

    await Promise.all(
      notifications.map(n => 
        notificationService.createNotification(n, createdById)
      )
    );
  } catch (error) {
    logger.error('Error handling maintenance.created event:', error);
  }
});

// Handle payment events
notificationEvents.on('payment.due', async (data) => {
  const { payment, createdById = 'system' } = data;
  
  try {
    await notificationService.createNotification({
      userId: payment.userId,
      type: 'payment_due',
      title: 'Payment Due Soon',
      message: `Payment of $${payment.amount} is due on ${new Date(payment.dueDate).toLocaleDateString()}`,
      entityType: 'PAYMENT',
      entityId: payment.id,
      metadata: {
        amount: payment.amount,
        dueDate: payment.dueDate
      }
    }, createdById);
  } catch (error) {
    logger.error('Error handling payment.due event:', error);
  }
});

// Add more event handlers as needed

module.exports = notificationEvents;
// src/services/inventoryService.js
const { Inventory, Property, Contact, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new inventory item
 * @param {Object} inventoryData - Inventory item data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createInventoryItem = async (inventoryData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const inventory = await Inventory.create(inventoryData, { transaction });
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: await getInventoryItemWithAssociations(inventory.id)
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating inventory item:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create inventory item',
        error: error.message
      }
    };
  }
};

/**
 * Get all inventory items with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, propertyId, status, category)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllInventoryItems = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      propertyId,
      status,
      category
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Add search conditions
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { serialNumber: { [Op.like]: `%${search}%` } },
        { modelNumber: { [Op.like]: `%${search}%` } },
        { brand: { [Op.like]: `%${search}%` } }
      ];
    }
    
    // Add filter conditions
    if (propertyId) whereClause.propertyId = propertyId;
    if (status) whereClause.status = status;
    if (category) whereClause.category = category;

    const { count, rows } = await Inventory.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']],
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: Contact,
          as: 'vendor',
          attributes: ['id', 'firstName', 'lastName', 'company'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching inventory items:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch inventory items',
        error: error.message
      }
    };
  }
};

/**
 * Get inventory item by ID
 * @param {string} id - Inventory item ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getInventoryItemById = async (id) => {
  try {
    const inventoryItem = await Inventory.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: Contact,
          as: 'vendor',
          attributes: ['id', 'firstName', 'lastName', 'company', 'email', 'phone'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!inventoryItem) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Inventory item not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: inventoryItem
      }
    };
  } catch (error) {
    logger.error('Error fetching inventory item:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch inventory item',
        error: error.message
      }
    };
  }
};

/**
 * Update inventory item
 * @param {string} id - Inventory item ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateInventoryItem = async (id, updateData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const inventoryItem = await Inventory.findByPk(id);
    
    if (!inventoryItem) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Inventory item not found'
        }
      };
    }

    await inventoryItem.update(updateData, { transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: await getInventoryItemWithAssociations(id)
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating inventory item:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update inventory item',
        error: error.message
      }
    };
  }
};

/**
 * Delete inventory item
 * @param {string} id - Inventory item ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteInventoryItem = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const inventoryItem = await Inventory.findByPk(id);
    
    if (!inventoryItem) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Inventory item not found'
        }
      };
    }

    await inventoryItem.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Inventory item deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error deleting inventory item:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete inventory item',
        error: error.message
      }
    };
  }
};

/**
 * Get inventory items by property ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (page, limit)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getInventoryItemsByPropertyId = async (propertyId, options = {}) => {
  try {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;

    const { count, rows } = await Inventory.findAndCountAll({
      where: { propertyId },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']],
      include: [
        {
          model: Contact,
          as: 'vendor',
          attributes: ['id', 'firstName', 'lastName', 'company'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching property inventory items:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch property inventory items',
        error: error.message
      }
    };
  }
};

// Helper function to get inventory item with associations
const getInventoryItemWithAssociations = async (id) => {
  return await Inventory.findByPk(id, {
    include: [
      {
        model: Property,
        as: 'property',
        attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
        required: false
      },
      {
        model: Contact,
        as: 'vendor',
        attributes: ['id', 'firstName', 'lastName', 'company', 'email', 'phone'],
        required: false
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'email'],
        required: false
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'firstName', 'lastName', 'email'],
        required: false
      }
    ]
  });
};

module.exports = {
  createInventoryItem,
  getAllInventoryItems,
  getInventoryItemById,
  updateInventoryItem,
  deleteInventoryItem,
  getInventoryItemsByPropertyId
}; 
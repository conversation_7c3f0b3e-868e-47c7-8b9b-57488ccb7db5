const { Loan, Property, Account, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new loan
 * @param {Object} loanData - Loan data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createLoan = async (loanData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const loan = await Loan.create(loanData, { transaction });
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: loan
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating loan:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create loan',
        error: error.message
      }
    };
  }
};

/**
 * Get all loans with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, accountId, propertyId)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllLoans = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      accountId,
      propertyId
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {
      ...(search && {
        [Op.or]: [
          { loanNumber: { [Op.like]: `%${search}%` } },
          { lenderName: { [Op.like]: `%${search}%` } },
          { customerServicePhone: { [Op.like]: `%${search}%` } }
        ]
      }),
      ...(accountId && { accountId }),
      ...(propertyId && { propertyId })
    };

    const { count, rows } = await Loan.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });

    // Clean up the response data
    const cleanedData = rows.map(loan => {
      const loanData = loan.get({ plain: true });
      // Remove any account-related data that might be included
      const { accountId, account, ...cleanLoan } = loanData;
      return cleanLoan;
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: cleanedData,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching loans:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch loans',
        error: error.message
      }
    };
  }
};

/**
 * Get loan by ID
 * @param {string} id - Loan ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getLoanById = async (id) => {
  try {
    const loan = await Loan.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });

    if (!loan) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Loan not found'
        }
      };
    }

    // Convert to plain object and remove accountId
    const loanData = loan.get({ plain: true });
    const { accountId, ...loanWithoutAccountId } = loanData;

    return {
      status: 200,
      payload: {
        success: true,
        data: loanWithoutAccountId
      }
    };
  } catch (error) {
    logger.error(`Error fetching loan with id ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch loan',
        error: error.message
      }
    };
  }
};

/**
 * Update loan
 * @param {string} id - Loan ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateLoan = async (id, updateData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const loan = await Loan.findByPk(id, { 
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ],
      transaction 
    });
    
    if (!loan) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Loan not found'
        }
      };
    }

    await loan.update(updateData, { transaction });
    await transaction.commit();

    // Convert to plain object and remove accountId from the response
    const loanData = loan.get({ plain: true });
    const { accountId, ...loanWithoutAccountId } = loanData;

    return {
      status: 200,
      payload: {
        success: true,
        data: loanWithoutAccountId,
        _accountId: accountId // Include accountId for permission checks
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating loan with id ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update loan',
        error: error.message
      }
    };
  }
};

/**
 * Delete loan
 * @param {string} id - Loan ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteLoan = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const loan = await Loan.findByPk(id, { transaction });
    
    if (!loan) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Loan not found'
        }
      };
    }

    await loan.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Loan deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting loan with id ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete loan',
        error: error.message
      }
    };
  }
};

/**
 * Get loans by property ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (page, limit)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getLoansByPropertyId = async (propertyId, options = {}) => {
  try {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;

    const { count, rows } = await Loan.findAndCountAll({
      where: { propertyId },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error(`Error fetching loans for property ${propertyId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch loans for property',
        error: error.message
      }
    };
  }
};

module.exports = {
  createLoan,
  getAllLoans,
  getLoanById,
  updateLoan,
  deleteLoan,
  getLoansByPropertyId
};
const { Utility, Property, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new utility
 * @param {Object} utilityData - Utility data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createUtility = async (utilityData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const utility = await Utility.create(utilityData, { transaction });
    await transaction.commit();
    
    // Fetch the created utility with relationships
    const createdUtility = await Utility.findByPk(utility.id, {
      attributes: {
        exclude: ['accountId']
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });
    
    return {
      status: 201,
      payload: {
        success: true,
        data: createdUtility
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating utility:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create utility',
        error: error.message
      }
    };
  }
};

/**
 * Get all utilities with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, propertyId, utilityType, isActive)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllUtilities = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      propertyId,
      utilityType,
      isActive
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Add search conditions
    if (search) {
      whereClause[Op.or] = [
        { providerName: { [Op.like]: `%${search}%` } },
        { accountNumber: { [Op.like]: `%${search}%` } },
        { notes: { [Op.like]: `%${search}%` } }
      ];
    }
    
    // Add filter conditions
    if (propertyId) whereClause.propertyId = propertyId;
    if (utilityType) whereClause.utilityType = utilityType;
    if (isActive !== undefined) whereClause.isActive = isActive === true || isActive === 'true';

    const { count, rows } = await Utility.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      attributes: {
        exclude: ['accountId']
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit),
          limit: parseInt(limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching utilities:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch utilities',
        error: error.message
      }
    };
  }
};

/**
 * Get utility by ID
 * @param {string} id - Utility ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUtilityById = async (id) => {
  try {
    const utility = await Utility.findByPk(id, {
      attributes: {
        exclude: ['accountId']
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!utility) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Utility not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: utility
      }
    };
  } catch (error) {
    logger.error('Error fetching utility:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch utility',
        error: error.message
      }
    };
  }
};

/**
 * Update utility
 * @param {string} id - Utility ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateUtility = async (id, updateData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const utility = await Utility.findByPk(id);
    
    if (!utility) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Utility not found'
        }
      };
    }

    await utility.update(updateData, { transaction });
    
    // Fetch the updated utility with relationships
    const updatedUtility = await Utility.findByPk(utility.id, {
      attributes: {
        exclude: ['accountId']
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ],
      transaction
    });
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: updatedUtility
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating utility:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update utility',
        error: error.message
      }
    };
  }
};

/**
 * Delete utility
 * @param {string} id - Utility ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteUtility = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const utility = await Utility.findByPk(id);
    
    if (!utility) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Utility not found'
        }
      };
    }
    
    await utility.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Utility deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error deleting utility:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete utility',
        error: error.message
      }
    };
  }
};

/**
 * Get utilities by property ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (page, limit, isActive)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUtilitiesByPropertyId = async (propertyId, options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10,
      isActive 
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = { propertyId };
    if (isActive !== undefined) {
      whereClause.isActive = isActive === true || isActive === 'true';
    }
    
    const { count, rows } = await Utility.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      attributes: {
        exclude: ['accountId']
      },
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });
    
    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit),
          limit: parseInt(limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching utilities by property ID:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch utilities by property',
        error: error.message
      }
    };
  }
};

module.exports = {
  createUtility,
  getAllUtilities,
  getUtilityById,
  updateUtility,
  deleteUtility,
  getUtilitiesByPropertyId
};
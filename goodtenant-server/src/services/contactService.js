const { Contact, Property, Account, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new contact
 * @param {Object} contactData - Contact data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createContact = async (contactData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const contact = await Contact.create(contactData, { transaction });
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: contact
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating contact:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create contact',
        error: error.message
      }
    };
  }
};

/**
 * Get all contacts with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, accountId, propertyId, type, isActive)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllContacts = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      accountId,
      propertyId,
      type,
      isActive
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Add search conditions
    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.like]: `%${search}%` } },
        { lastName: { [Op.like]: `%${search}%` } },
        { company: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { phone: { [Op.like]: `%${search}%` } }
      ];
    }
    
    // Add filter conditions
    if (accountId) whereClause.accountId = accountId;
    if (propertyId) whereClause.propertyId = propertyId;
    if (type) whereClause.type = type;
    if (isActive !== undefined) whereClause.isActive = isActive === 'true';

    const { count, rows } = await Contact.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['lastName', 'ASC'], ['firstName', 'ASC']],
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: Account,
          as: 'account',
          attributes: ['id', 'name'],
          required: true
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching contacts:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch contacts',
        error: error.message
      }
    };
  }
};

/**
 * Get contact by ID
 * @param {string} id - Contact ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getContactById = async (id) => {
  try {
    const contact = await Contact.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: Account,
          as: 'account',
          attributes: ['id', 'name'],
          required: true
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!contact) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Contact not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: contact
      }
    };
  } catch (error) {
    logger.error('Error fetching contact:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch contact',
        error: error.message
      }
    };
  }
};

/**
 * Update contact
 * @param {string} id - Contact ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateContact = async (id, updateData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const contact = await Contact.findByPk(id);
    
    if (!contact) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Contact not found'
        }
      };
    }

    await contact.update(updateData, { transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: contact
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating contact:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update contact',
        error: error.message
      }
    };
  }
};

/**
 * Delete contact
 * @param {string} id - Contact ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteContact = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const contact = await Contact.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id'],
          required: false
        }
      ]
    });
    
    if (!contact) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Contact not found'
        }
      };
    }

    await contact.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Contact deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error deleting contact:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete contact',
        error: error.message
      }
    };
  }
};

/**
 * Get contacts by property ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (page, limit)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getContactsByPropertyId = async (propertyId, options = {}) => {
  try {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;

    const { count, rows } = await Contact.findAndCountAll({
      where: { propertyId },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['lastName', 'ASC'], ['firstName', 'ASC']]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching property contacts:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch property contacts',
        error: error.message
      }
    };
  }
};

module.exports = {
  createContact,
  getAllContacts,
  getContactById,
  updateContact,
  deleteContact,
  getContactsByPropertyId
};
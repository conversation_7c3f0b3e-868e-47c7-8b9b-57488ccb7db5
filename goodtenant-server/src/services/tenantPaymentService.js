const { Op, literal, ValidationError } = require('sequelize');
const { Lease, Payment, Property, User } = require('../models');
const logger = require('../utils/logger');
const moment = require('moment');
const { sequelize } = require('../models');
const { v4: uuidv4, validate: isUuid } = require('uuid');

// Helper to handle date formatting for queries
const formatDateForQuery = (date) => {
  return moment(date).format('YYYY-MM-DD');
};

// Validate UUID format
const isValidUuid = (id) => {
  return id && typeof id === 'string' && isUuid(id);
};

/**
 * Get payment status for a specific tenant
 * @param {string} tenantId - ID of the tenant
 * @param {Object} options - Additional options
 * @param {string} [options.leaseId] - Optional specific lease ID to filter by
 * @param {Date|string} [options.asOfDate] - Date to calculate status as of (defaults to current date)
 * @returns {Promise<{status: number, payload: {success: boolean, data?: Object, message?: string, error?: string}}>} - Status and response payload
 */
const getTenantPaymentStatus = async (tenantId, options = {}) => {
  // Validate tenantId
  if (!isValidUuid(tenantId)) {
    logger.error(`Invalid tenant ID format: ${tenantId}`);
    return {
      status: 400,
      payload: {
        success: false,
        message: 'Invalid tenant ID format',
        error: 'Tenant ID must be a valid UUID'
      }
    };
  }

  const transaction = await sequelize.transaction();
  
  try {
    const { leaseId, asOfDate = new Date() } = options;
    const currentDate = moment(asOfDate);
    
    logger.info(`Fetching leases for tenant ID: ${tenantId}`, { 
      leaseId,
      asOfDate: currentDate.format('YYYY-MM-DD')
    });
    
    // Get all active leases for the tenant
    const leases = await Lease.findAll({
      attributes: [
        'id', 
        'monthly_rent', 
        'payment_due_date', 
        'start_date', 
        'end_date', 
        'lease_type', 
        'status',
        'property_id'
      ],
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode']
        },
        {
          model: User,
          as: 'tenants',
          where: { id: tenantId },
          required: true,
          through: { 
            where: { user_id: tenantId },
            attributes: [] 
          }
        }
      ],
      where: {
        status: 'active',
        start_date: { [Op.lte]: currentDate.toDate() },
        [Op.or]: [
          { end_date: { [Op.gte]: currentDate.toDate() } },
          { end_date: null },
          { lease_type: 'month-to-month' }
        ],
        ...(leaseId && { id: leaseId })
      },
      transaction,
      raw: false,
      logging: (sql) => logger.debug(`Lease query: ${sql}`) // Log the generated SQL
    });
    
    logger.info(`Found ${leases.length} active leases for tenant ${tenantId}`, {
      leaseIds: leases.map(lease => lease.id)
    });

    if (leases.length === 0) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'No active leases found for this tenant',
          debug: {
            tenantId,
            currentDate: currentDate.format('YYYY-MM-DD'),
            leaseId: leaseId || 'not specified'
          }
        }
      };
    }

    // Convert to array of lease objects for consistent processing
    const tenantLeases = leases.map(lease => {
      logger.debug(`Processing lease ${lease.id} for tenant ${tenantId}`);
      return {
        lease,
        getDataValue: (field) => {
          const map = {
            'lease_id': lease.id,
            'user_id': tenantId
          };
          return map[field];
        }
      };
    });



    // Process each lease to get payment status
    const paymentStatuses = await Promise.all(
      tenantLeases.map(async (tenantLease) => {
        const lease = tenantLease.lease;
        const paymentDay = lease.getDataValue('payment_due_date') || 1;
        const monthlyRent = parseFloat(lease.getDataValue('monthly_rent') || 0);
        
        // Calculate current period
        let periodStart, periodEnd, dueDate;
        const today = currentDate.clone();
        
        // Determine the payment due date for the current month
        dueDate = today.clone().date(paymentDay);
        
        // If today is before the due date, the payment period is from last month to now
        if (today.date() < paymentDay) {
          periodStart = today.clone().subtract(1, 'month').date(paymentDay);
          periodEnd = today.clone().date(paymentDay);
        } else {
          // If today is on or after the due date, the payment period is from this month to next month
          periodStart = today.clone().date(paymentDay);
          periodEnd = periodStart.clone().add(1, 'month');
        }

        // Format dates for query
        const periodStartStr = periodStart.format('YYYY-MM-DD');
        const periodEndStr = periodEnd.format('YYYY-MM-DD');

        // Get payments for this tenant in the current period
        const payments = await Payment.findAll({
          where: {
            status: 'completed',
            payer_id: tenantId,
            payment_date: {
              [Op.between]: [formatDateForQuery(periodStart), formatDateForQuery(periodEnd)]
            }
          },
          attributes: [
            [sequelize.fn('SUM', sequelize.col('amount')), 'totalPaid']
          ],
          raw: true,
          transaction
        });

        const totalPaid = parseFloat(payments[0]?.totalPaid || 0);
        const isPaid = totalPaid >= monthlyRent;
        const balance = Math.max(0, monthlyRent - totalPaid);
        
        // Get payment history for this tenant
        const paymentHistory = await Payment.findAll({
          where: { 
            payer_id: tenantId
          },
          order: [['payment_date', 'DESC']],
          limit: 3,
          attributes: ['id', 'amount', 'payment_date', 'status', 'payment_method', 'payment_date'],
          transaction
        });
        
        return {
          leaseId: lease.id,
          property: {
            id: lease.property.id,
            name: lease.property.name,
            addressLine1: lease.property.addressLine1,
            addressLine2: lease.property.addressLine2,
            city: lease.property.city,
            state: lease.property.state,
            zipCode: lease.property.postalCode
          },
          monthlyRent: monthlyRent,
          paymentDueDate: paymentDay,
          currentPeriod: {
            start: periodStart.toDate(),
            end: periodEnd.toDate()
          },
          paymentStatus: {
            isPaid,
            totalPaid,
            balance,
            dueDate: dueDate.format('YYYY-MM-DD'),
            // Payment is late if today is after the due date and not paid
            isLate: !isPaid && today.isAfter(dueDate, 'day')
          },
          paymentHistory: paymentHistory.map(payment => {
            // Format the payment date for consistent output
            const paymentDate = payment.getDataValue ? 
              moment(payment.getDataValue('paymentDate')).format('YYYY-MM-DD') :
              moment(payment.payment_date).format('YYYY-MM-DD');
              
            return {
              id: payment.id,
              amount: payment.amount,
              paymentDate: paymentDate,
              status: payment.status,
              paymentMethod: payment.payment_method
            };
          })
        };
      })
    );

    // Calculate overall status if multiple leases
    const overallStatus = {
      totalMonthlyRent: paymentStatuses.reduce((sum, status) => sum + (status.monthlyRent || 0), 0),
      totalPaid: paymentStatuses.reduce((sum, status) => sum + (status.paymentStatus?.totalPaid || 0), 0),
      totalBalance: paymentStatuses.reduce((sum, status) => sum + (status.paymentStatus?.balance || 0), 0),
      allPaid: paymentStatuses.length > 0 && paymentStatuses.every(status => status.paymentStatus?.isPaid === true),
      anyLate: paymentStatuses.some(status => status.paymentStatus?.isLate === true)
    };

    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: {
          tenantId,
          asOfDate: currentDate.toDate(),
          overallStatus,
          leases: paymentStatuses
        }
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error getting tenant payment status:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to get payment status',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

module.exports = {
  getTenantPaymentStatus
};

const { Tax, Property, Account, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new tax record
 * @param {Object} taxData - Tax data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createTax = async (taxData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const tax = await Tax.create(taxData, { 
      transaction,
      attributes: { exclude: ['accountId'] } // Exclude accountId from response
    });
    
    await transaction.commit();
    
    // Remove accountId from the response without causing naming conflict
    const { accountId, ...taxResponse } = tax.get({ plain: true });
    
    return {
      status: 201,
      payload: {
        success: true,
        data: taxResponse
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating tax record:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create tax record',
        error: error.message
      }
    };
  }
};

/**
 * Get all taxes with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, propertyId, status, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllTaxes = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      propertyId,
      status,
      taxType
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Add search conditions
    if (search) {
      whereClause[Op.or] = [
        { description: { [Op.like]: `%${search}%` } },
        { accountNumber: { [Op.like]: `%${search}%` } },
        { taxId: { [Op.like]: `%${search}%` } }
      ];
    }
    
    // Add filter conditions
    if (propertyId) whereClause.propertyId = propertyId;
    if (status) whereClause.status = status;
    if (taxType) whereClause.taxType = taxType;

    const { count, rows } = await Tax.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['dueDate', 'ASC']],
      attributes: { exclude: ['accountId'] }, // Exclude accountId from response
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching taxes:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch taxes',
        error: error.message
      }
    };
  }
};

/**
 * Get tax by ID
 * @param {string} id - Tax ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getTaxById = async (id) => {
  try {
    const tax = await Tax.findByPk(id, {
      attributes: { exclude: ['accountId'] }, // Exclude accountId from response
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!tax) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Tax record not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: tax
      }
    };
  } catch (error) {
    logger.error(`Error fetching tax with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch tax record',
        error: error.message
      }
    };
  }
};

/**
 * Update tax record
 * @param {string} id - Tax ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of user making the update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateTax = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // First find the tax record with accountId for permission checking
    const tax = await Tax.findByPk(id, { 
      transaction,
      attributes: { include: ['accountId'] } // Include accountId for permission check
    });
    
    if (!tax) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Tax record not found'
        }
      };
    }

    // Update the tax record
    await tax.update(updateData, { transaction });
    
    // Get the updated tax record without accountId for the response
    const updatedTax = await Tax.findByPk(id, {
      transaction,
      attributes: { exclude: ['accountId'] }, // Exclude accountId from response
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: updatedTax
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating tax with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update tax record',
        error: error.message
      }
    };
  }
};

/**
 * Delete tax record (soft delete)
 * @param {string} id - Tax ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteTax = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    // First find the tax record with accountId for permission checking
    const tax = await Tax.findByPk(id, {
      transaction,
      attributes: { include: ['accountId'] } // Include accountId for permission check
    });
    
    if (!tax) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Tax record not found'
        }
      };
    }

    // Delete the record
    await tax.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Tax record deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting tax with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete tax record',
        error: error.message
      }
    };
  }
};

/**
 * Get taxes by property ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (page, limit, status)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPropertyTaxes = async (propertyId, options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10,
      status
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = { propertyId };
    if (status) whereClause.status = status;

    const { count, rows } = await Tax.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['dueDate', 'ASC']],
      attributes: { exclude: ['accountId'] } // Exclude accountId from response
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error(`Error fetching taxes for property ${propertyId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch property taxes',
        error: error.message
      }
    };
  }
};

module.exports = {
  createTax,
  getAllTaxes,
  getTaxById,
  updateTax,
  deleteTax,
  getPropertyTaxes
};
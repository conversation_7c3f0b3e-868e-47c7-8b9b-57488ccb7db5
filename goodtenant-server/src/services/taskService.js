const { Task, User, Property, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new Task
 * @param {Object} taskData - Task data
 * @param {string} userId - ID of the user creating the task
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createTask = async (taskData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const task = await Task.create({
      ...taskData,
      createdById: userId,
      accountId: taskData.accountId || null,
      propertyId: taskData.propertyId || null,
      assignedToId: taskData.assignedToId || null,
    }, { transaction });

    // Fetch the created task with related data
    const createdTask = await Task.findByPk(task.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1'],
          required: false
        }
      ],
      transaction
    });

    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: createdTask
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating task:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create task',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Get all tasks with pagination and filters
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {string} options.search - Search term
 * @param {string} options.status - Filter by status
 * @param {string} options.priority - Filter by priority
 * @param {string} options.type - Filter by type
 * @param {string} options.assignedTo - Filter by assignee ID
 * @param {string} options.propertyId - Filter by property ID
 * @param {string} options.accountId - Filter by account ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllTasks = async ({
  page = 1,
  limit = 10,
  search = '',
  status,
  priority,
  type,
  assignedTo,
  propertyId,
  accountId,
  dueDateFrom,
  dueDateTo
}) => {
  try {
    const offset = (page - 1) * limit;
    
    const whereClause = {
      ...(search && {
        [Op.or]: [
          { title: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } }
        ]
      }),
      ...(status && { status }),
      ...(priority && { priority }),
      ...(type && { type }),
      ...(assignedTo && { assignedToId: assignedTo }),
      ...(propertyId && { propertyId }),
      ...(accountId && { accountId }),
      ...(dueDateFrom || dueDateTo ? {
        dueDate: {
          ...(dueDateFrom && { [Op.gte]: new Date(dueDateFrom) }),
          ...(dueDateTo && { [Op.lte]: new Date(dueDateTo) })
        }
      } : {})
    };

    const { count, rows: tasks } = await Task.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['dueDate', 'ASC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1']
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: tasks,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching tasks:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch tasks',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Get task by ID
 * @param {string} id - Task ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getTaskById = async (id) => {
  try {
    const task = await Task.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state']
        }
      ]
    });

    if (!task) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Task not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: task
      }
    };
  } catch (error) {
    logger.error('Error fetching task:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch task',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Update a task
 * @param {string} id - Task ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user updating the task
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateTask = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const task = await Task.findByPk(id, { transaction });
    
    if (!task) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Task not found'
        }
      };
    }

    // If status is being updated to completed, set completedById
    if (updateData.status === 'completed' && task.status !== 'completed') {
      updateData.completedById = userId;
    }

    await task.update(updateData, { transaction });
    
    // Fetch the updated task with related data
    const updatedTask = await Task.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1']
        }
      ],
      transaction
    });

    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        data: updatedTask
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating task:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update task',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Delete a task
 * @param {string} id - Task ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteTask = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const task = await Task.findByPk(id, { transaction });
    
    if (!task) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Task not found'
        }
      };
    }

    await task.destroy({ transaction });
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Task deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error deleting task:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete task',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Create tasks for recurring patterns
 * @param {Object} task - The recurring task
 * @returns {Promise<void>}
 */
const createRecurringTasks = async (task) => {
  if (!task.isRecurring || !task.recurrencePattern || !task.nextRecurrenceDate) {
    return;
  }

  const now = new Date();
  
  // Only create new tasks if the next recurrence is due
  if (task.nextRecurrenceDate <= now) {
    const transaction = await sequelize.transaction();
    
    try {
      // Create next occurrence
      const nextDueDate = calculateNextDueDate(task.dueDate, task.recurrencePattern);
      
      // Create a new task for the next occurrence
      await Task.create({
        ...task.get({ plain: true }),
        id: undefined, // Let DB generate new ID
        status: 'pending',
        dueDate: nextDueDate,
        completedAt: null,
        completedById: null,
        nextRecurrenceDate: calculateNextDueDate(nextDueDate, task.recurrencePattern)
      }, { transaction });
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating recurring task:', error);
    }
  }
};

/**
 * Calculate next due date based on recurrence pattern
 * @param {Date} currentDueDate - Current due date
 * @param {string} pattern - Recurrence pattern (daily, weekly, monthly, yearly)
 * @returns {Date} - Next due date
 */
const calculateNextDueDate = (currentDueDate, pattern) => {
  const nextDate = new Date(currentDueDate);
  
  switch (pattern) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + 1);
      break;
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + 7);
      break;
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
    default:
      break;
  }
  
  return nextDate;
};

module.exports = {
  createTask,
  getAllTasks,
  getTaskById,
  updateTask,
  deleteTask,
  createRecurringTasks
};

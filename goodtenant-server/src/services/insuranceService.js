const { Insurance, Property, Account, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new Insurance
 * @param {Object} insuranceData - Insurance data
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createInsurance = async (insuranceData) => {
  const transaction = await sequelize.transaction();
  
  try {
    const insurance = await Insurance.create(insuranceData, { transaction });
    
    // Fetch the created insurance without account information
    const createdInsurance = await Insurance.findByPk(insurance.id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ],
      transaction
    });
    
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        success: true,
        data: createdInsurance
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating insurance:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create insurance',
        error: error.message
      }
    };
  }
};

/**
 * Get all Insurances with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllInsurances = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      propertyId,
      isActive
    } = options;
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { policyNumber: { [Op.like]: `%${search}%` } },
        { provider: { [Op.like]: `%${search}%` } },
        { agentName: { [Op.like]: `%${search}%` } },
        { agentEmail: { [Op.like]: `%${search}%` } },
      ];
    }
    
    if (propertyId) whereClause.propertyId = propertyId;
    if (isActive !== undefined) whereClause.isActive = isActive === 'true';

    const { count, rows } = await Insurance.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: ['accountId'] // Exclude accountId from the response
      },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['effectiveStartDate', 'DESC']],
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching insurances:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch insurances',
        error: error.message
      }
    };
  }
};

/**
 * Get Insurance by ID
 * @param {string} id - Insurance ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getInsuranceById = async (id) => {
  try {
    const insurance = await Insurance.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!insurance) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Insurance not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: insurance
      }
    };
  } catch (error) {
    logger.error(`Error fetching insurance with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch insurance',
        error: error.message
      }
    };
  }
};

/**
 * Update Insurance
 * @param {string} id - Insurance ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user making the update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateInsurance = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const insurance = await Insurance.findByPk(id, { transaction });
    
    if (!insurance) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Insurance not found'
        }
      };
    }

    // Add updatedBy reference
    updateData.updatedBy = userId;

    await insurance.update(updateData, { transaction });
    
    // Fetch the updated insurance without account information
    const updatedInsurance = await Insurance.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state'],
          required: false
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ],
      transaction
    });
    
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        data: updatedInsurance
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating insurance with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update insurance',
        error: error.message
      }
    };
  }
};

/**
 * Delete Insurance (soft delete)
 * @param {string} id - Insurance ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteInsurance = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const insurance = await Insurance.findByPk(id, { transaction });
    
    if (!insurance) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Insurance not found'
        }
      };
    }

    await insurance.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Insurance deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting insurance with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete insurance',
        error: error.message
      }
    };
  }
};

/**
 * Get insurances by property ID
 * @param {string} propertyId - Property ID
 * @param {Object} options - Query options (page, limit, isActive)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPropertyInsurances = async (propertyId, options = {}) => {
  try {
    const { page = 1, limit = 10, isActive } = options;
    const offset = (page - 1) * limit;
    
    const whereClause = { propertyId };
    if (isActive !== undefined) whereClause.isActive = isActive === 'true';

    const { count, rows } = await Insurance.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: ['accountId'] // Exclude accountId from the response
      },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['effectiveStartDate', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error(`Error fetching insurances for property ${propertyId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch property insurances',
        error: error.message
      }
    };
  }
};

module.exports = {
  createInsurance,
  getAllInsurances,
  getInsuranceById,
  updateInsurance,
  deleteInsurance,
  getPropertyInsurances
};

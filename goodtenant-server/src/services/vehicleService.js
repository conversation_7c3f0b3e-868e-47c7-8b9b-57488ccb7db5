const { Vehicle, User } = require('../models');
const { Op, Transaction } = require('sequelize');
const logger = require('../utils/logger');
const sequelize = require('../config/database');

/**
 * Create a new vehicle for a user
 * @param {string} userId - User ID who owns the vehicle
 * @param {Object} vehicleData - Vehicle data
 * @param {Object} options - Options including transaction
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createVehicle = async (userId, vehicleData, options = {}) => {
  let transaction;
  
  try {
    // Only create a transaction if one wasn't provided and we're not explicitly told not to
    if (options.transaction === undefined) {
      transaction = await sequelize.transaction();
    } else if (options.transaction) {
      transaction = options.transaction;
    }
    
    // Check if user exists
    const user = await User.findByPk(userId, { transaction });
    if (!user) {
      if (transaction) await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    const vehicle = await Vehicle.create(
      { 
        ...vehicleData, 
        userId 
      },
      { transaction }
    );
    
    if (transaction && options.transaction === undefined) {
      await transaction.commit();
    }
    
    return {
      status: 201,
      payload: {
        success: true,
        data: vehicle
      }
    };
  } catch (error) {
    if (transaction && options.transaction === undefined) {
      await transaction.rollback();
    }
    logger.error('Error creating vehicle:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create vehicle',
        error: error.message
      }
    };
  }
};

/**
 * Get all vehicles for a user with optional filtering and pagination
 * @param {string} userId - User ID
 * @param {Object} options - Query options (page, limit, search, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUserVehicles = async (userId, options = {}) => {
  try {
    const { page = 1, limit = 10, search = '' } = options;
    const offset = (page - 1) * limit;
    
    const whereClause = { userId };
    
    if (search) {
      whereClause[Op.or] = [
        { make: { [Op.like]: `%${search}%` } },
        { model: { [Op.like]: `%${search}%` } },
        { licensePlate: { [Op.like]: `%${search}%` } },
      ];
    }

    const { count, rows } = await Vehicle.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching vehicles:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch vehicles',
        error: error.message
      }
    };
  }
};

/**
 * Get vehicle by ID
 * @param {string} userId - User ID (for authorization)
 * @param {string} vehicleId - Vehicle ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getVehicleById = async (userId, vehicleId) => {
  try {
    const vehicle = await Vehicle.findOne({
      where: { 
        id: vehicleId,
        userId // Ensure the vehicle belongs to the user
      },
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false
        }
      ]
    });

    if (!vehicle) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Vehicle not found or access denied'
        }
      };
    }


    return {
      status: 200,
      payload: {
        success: true,
        data: vehicle
      }
    };
  } catch (error) {
    logger.error(`Error fetching vehicle with ID ${vehicleId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch vehicle',
        error: error.message
      }
    };
  }
};

/**
 * Update vehicle
 * @param {string} userId - User ID (for authorization)
 * @param {string} vehicleId - Vehicle ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateVehicle = async (userId, vehicleId, updateData) => {
  let transaction;
  
  try {
    transaction = await sequelize.transaction();
    
    const vehicle = await Vehicle.findOne({
      where: { 
        id: vehicleId,
        userId // Ensure the vehicle belongs to the user
      },
      transaction
    });
    
    if (!vehicle) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Vehicle not found or access denied'
        }
      };
    }

    await vehicle.update(updateData, { transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        data: vehicle
      }
    };
  } catch (error) {
    if (transaction) await transaction.rollback();
    logger.error(`Error updating vehicle with ID ${vehicleId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update vehicle',
        error: error.message
      }
    };
  }
};

/**
 * Delete vehicle
 * @param {string} userId - User ID (for authorization)
 * @param {string} vehicleId - Vehicle ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteVehicle = async (userId, vehicleId) => {
  let transaction;
  
  try {
    transaction = await sequelize.transaction();
    
    const vehicle = await Vehicle.findOne({
      where: { 
        id: vehicleId,
        userId // Ensure the vehicle belongs to the user
      },
      transaction
    });
    
    if (!vehicle) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Vehicle not found or access denied'
        }
      };
    }

    await vehicle.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Vehicle deleted successfully'
      }
    };
  } catch (error) {
    if (transaction) await transaction.rollback();
    logger.error(`Error deleting vehicle with ID ${vehicleId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete vehicle',
        error: error.message
      }
    };
  }
};



module.exports = {
  createVehicle,
  getUserVehicles,
  getVehicleById,
  updateVehicle,
  deleteVehicle
};

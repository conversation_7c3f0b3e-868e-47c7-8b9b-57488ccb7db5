// services/userFileService.js
const { sequelize, User } = require('../models');
const { 
  uploadModelFile, 
  deleteModelFile, 
  getSignedFileUrl 
} = require('./fileService');
const logger = require('../utils/logger');

/**
 * Upload a signature for a user
 * @param {Object} file - File object containing buffer, originalname, mimetype, and size
 * @param {string} userId - ID of the user
 * @returns {Promise<Object>} - Returns status and payload with file details
 */
const uploadUserSignature = async (file, userId) => {
  const transaction = await sequelize.transaction();
  let user;
  
  try {
    // Get user with transaction
    user = await User.findByPk(userId, { transaction });
    if (!user) {
      await transaction.rollback();
      return { status: 404, payload: { message: 'User not found' } };
    }

    // Delete existing signature if it exists
    if (user.signature) {
      const deleteResult = await deleteUserSignature(userId, transaction);
      if (deleteResult.status !== 200) {
        await transaction.rollback();
        return deleteResult;
      }
    }

    // Generate a unique file key
    const fileExtension = file.originalname.substring(file.originalname.lastIndexOf('.'));
    const fileKey = `users/${userId}/signatures/signature-${Date.now()}${fileExtension}`;
    
    // Upload new signature
    logger.debug('Calling uploadModelFile with:', {
      fileSize: file.size,
      fileType: file.mimetype,
      fileKey,
      modelName: 'User',
      modelId: userId,
      userId: userId,
      description: 'User signature'
    });

    const result = await uploadModelFile(
      {
        ...file,
        originalname: `signature${fileExtension}`,
        key: fileKey // Explicitly set the S3 key
      },
      'User',
      userId,
      userId, // Pass userId as both modelId and userId
      'User signature'
    );
    
    logger.debug('uploadModelFile result:', JSON.stringify(result, null, 2));

    if (!result || !result.payload || !result.payload.fileKey) {
      throw new Error('Failed to upload file: Invalid response from uploadModelFile');
    }

    // Update user with new signature URL
    await user.update({ 
      signature: result.payload.fileKey 
    }, { transaction });

    // Get signed URL before committing the transaction
    const signedUrl = await getSignedFileUrl(result.payload.fileKey);
    
    await transaction.commit();
    
    return {
      status: 201,
      payload: {
        ...result.payload,
        signedUrl
      }
    };
  } catch (error) {
    if (transaction && !transaction.finished) {
      await transaction.rollback();
    }
    logger.error('Error uploading user signature:', error);
    throw error;
  }
};

/**
 * Delete a user's signature
 * @param {string} userId - ID of the user
 * @param {Object} [transaction] - Optional transaction object
 * @returns {Promise<Object>} - Returns status and payload
 */
const deleteUserSignature = async (userId, transaction = null) => {
  const options = transaction ? { transaction } : {};
  
  try {
    const user = await User.findByPk(userId, options);
    if (!user) {
      if (transaction) await transaction.rollback();
      return { status: 404, payload: { message: 'User not found' } };
    }
    
    if (!user.signature) {
      return { status: 404, payload: { message: 'Signature not found' } };
    }

    const fileKey = user.signature;
    
    // Clear the signature first to prevent issues if delete fails
    await user.update({ signature: null }, options);
    
    // Delete the file from storage
    await deleteModelFile('UserFile', fileKey, userId);

    // Only commit if we created the transaction
    if (options.transaction && !transaction) {
      await options.transaction.commit();
    }

    return { 
      status: 200, 
      payload: { message: 'Signature deleted successfully' } 
    };
  } catch (error) {
    // Rollback only if we created the transaction and it's not already finished
    if (options.transaction && !transaction && !options.transaction.finished) {
      await options.transaction.rollback();
    }
    logger.error('Error deleting user signature:', error);
    throw error;
  }
};

/**
 * Get a user's signature URL
 * @param {string} userId - ID of the user
 * @returns {Promise<Object>} - Returns status and payload with signed URL
 */
const getUserSignature = async (userId) => {
  try {
    const user = await User.findByPk(userId);
    if (!user || !user.signature) {
      return { status: 404, payload: { message: 'Signature not found' } };
    }

    const signedUrl = await getSignedFileUrl(user.signature, 3600); // 1 hour expiry
    return {
      status: 200,
      payload: {
        signedUrl,
        expiresAt: new Date(Date.now() + 3600 * 1000)
      }
    };
  } catch (error) {
    logger.error('Error getting user signature:', error);
    throw error;
  }
};

module.exports = {
  uploadUserSignature,
  deleteUserSignature,
  getUserSignature
};
const { Payment, PaymentReceipt, User, Account, AccountUser, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const { getStripeInstanceForAccount } = require('./stripeService');
const { v4: uuidv4 } = require('uuid');

/**
 * Create a Stripe Checkout session for a payment
 * @param {Object} paymentData - Payment data
 * @param {string} userId - ID of the user creating the payment
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with checkout session
 */
/**
 * Create a Stripe Checkout session for a payment
 * @param {Object} paymentData - Payment data
 * @param {string} userId - ID of the user creating the payment
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with checkout session
 */
const createCheckoutSession = async (paymentData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { items = [], ...paymentDetails } = paymentData;
    
    // If receiver ID is not provided, try to get it from the account
    if (!paymentDetails.receiverId && paymentDetails.accountId) {
      const accountUser = await AccountUser.findOne({
        where: { 
          accountId: paymentDetails.accountId,
          primaryRole: 'account_owner'
        },
        include: [{
          model: User,
          as: 'user',
          required: true
        }],
        transaction
      });
      
      if (accountUser?.user) {
        paymentDetails.receiverId = accountUser.user.id;
      }
    }
    
    // Throw error if receiverId is still not set
    if (!paymentDetails.receiverId) {
      throw new Error('Receiver ID is required. Either provide receiverId or ensure the account has an owner.');
    }
    
    // Get account-specific Stripe instance
    const stripeInstance = await getStripeInstanceForAccount(paymentDetails.accountId);
    
    // Rest of the function remains the same...
    const clientReferenceId = `payment_${uuidv4()}`;
    
    // Store payment details in session metadata instead of creating a payment record
    // We'll create the payment record in the webhook when the checkout is completed

    let lineItems;
    if (items.length > 0) {
      // Prepare line items for Stripe Checkout
      lineItems = items.map(item => ({
        price_data: {
          currency: paymentDetails.currency || 'usd',
          product_data: {
            name: item.description || `Payment Item ${item.id}`,
            ...(item.notes && { description: item.notes }),
          },
          unit_amount: Math.round((item.amount / (item.quantity || 1)) * 100), // Convert to cents per unit
        },
        quantity: item.quantity || 1,
      }));
    } else {
      // Prepare line item for Stripe Checkout (single payment)
      lineItems = [{
        price_data: {
          currency: paymentDetails.currency || 'usd',
          product_data: {
            name: paymentDetails.description || 'Payment',
            ...(paymentDetails.metadata?.period && { description: `Period: ${paymentDetails.metadata.period}` }),
          },
          unit_amount: Math.round(Number(paymentDetails.amount) * 100), // Convert to cents
        },
        quantity: 1,
      }];
    }

    // Include all necessary payment details in metadata
    // Ensure all metadata values are strings as required by Stripe
    const metadata = {
      // Core payment information
      userId: String(userId),
      accountId: paymentDetails.accountId ? String(paymentDetails.accountId) : '',
      clientReferenceId: String(clientReferenceId),
      amount: String(paymentDetails.amount || 0),
      currency: paymentDetails.currency || 'usd',
      description: (paymentDetails.description || 'Payment').substring(0, 500),
      // Include any additional metadata
      ...(paymentDetails.metadata || {})
    };
    
    // Clean up metadata to ensure all values are strings and not too long for Stripe
    Object.keys(metadata).forEach(key => {
      if (metadata[key] === null || metadata[key] === undefined) {
        delete metadata[key];
      } else if (typeof metadata[key] !== 'string') {
        try {
          metadata[key] = JSON.stringify(metadata[key]).substring(0, 500);
        } catch (e) {
          metadata[key] = String(metadata[key]).substring(0, 500);
        }
      } else {
        metadata[key] = metadata[key].substring(0, 500);
      }
    });

    // Combine all metadata into a single object
    const sessionMetadata = {
      // Core payment information
      userId: String(userId),
      accountId: String(paymentDetails.accountId || ''),
      receiverId: String(paymentDetails.receiverId || ''),
      clientReferenceId: String(clientReferenceId),
      amount: String(paymentDetails.amount || 0),
      currency: paymentDetails.currency || 'usd',
      description: (paymentDetails.description || 'Payment').substring(0, 500),
      timestamp: new Date().toISOString(),
      // Include any additional metadata from the request
      ...(paymentDetails.metadata || {})
    };

    // Clean up metadata to ensure all values are strings and not too long for Stripe
    Object.keys(sessionMetadata).forEach(key => {
      if (sessionMetadata[key] === null || sessionMetadata[key] === undefined) {
        delete sessionMetadata[key];
      } else if (typeof sessionMetadata[key] !== 'string') {
        try {
          sessionMetadata[key] = JSON.stringify(sessionMetadata[key]).substring(0, 500);
        } catch (e) {
          sessionMetadata[key] = String(sessionMetadata[key]).substring(0, 500);
        }
      } else {
        sessionMetadata[key] = sessionMetadata[key].substring(0, 500);
      }
    });

    // Create Stripe Checkout session with account-specific Stripe instance
    const session = await stripeInstance.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: paymentDetails.currency || 'usd',
          product_data: {
            name: paymentDetails.description || 'Payment',
          },
          unit_amount: paymentDetails.amount,
        },
        quantity: 1,
      }],
      mode: 'payment',
      // Use the cancel_url from client or fallback to default
      cancel_url: paymentDetails.cancelUrl || `${process.env.CLIENT_URL}/en/payments`,
      success_url: paymentDetails.successUrl || `${process.env.CLIENT_URL}/en/payments/success?session_id={CHECKOUT_SESSION_ID}`,
      client_reference_id: `account_${paymentDetails.accountId}`,
      metadata: sessionMetadata,
      payment_intent_data: {
        metadata: {
          ...sessionMetadata,
          webhook_url: `${process.env.API_URL || 'http://localhost:4000'}/api/stripe/webhook`
        }
      },
    });

    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        sessionId: session.id,
        url: session.url,
        // No paymentId is returned here since we create it in the webhook
      },
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating checkout session:', error);
    throw new Error(`Failed to create checkout session: ${error.message}`);
  }
};

/**
 * Get all payments with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, filters, etc.)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllPayments = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status,
      paymentMethod,
      startDate,
      endDate,
      accountId,
      payerId,
      receiverId,
    } = options;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Apply filters
    if (search) {
      whereClause[Op.or] = [
        { referenceNumber: { [Op.like]: `%${search}%` } },
        { notes: { [Op.like]: `%${search}%` } },
        { '$payer.firstName$': { [Op.like]: `%${search}%` } },
        { '$payer.lastName$': { [Op.like]: `%${search}%` } },
        { '$receiver.firstName$': { [Op.like]: `%${search}%` } },
        { '$receiver.lastName$': { [Op.like]: `%${search}%` } },
      ];
    }

    if (status) whereClause.status = status;
    if (paymentMethod) whereClause.paymentMethod = paymentMethod;
    if (accountId) whereClause.accountId = accountId;
    if (payerId) whereClause.payerId = payerId;
    if (receiverId) whereClause.receiverId = receiverId;

    if (startDate || endDate) {
      whereClause.paymentDate = {};
      if (startDate) whereClause.paymentDate[Op.gte] = new Date(startDate);
      if (endDate) whereClause.paymentDate[Op.lte] = new Date(endDate);
    }

    const { count, rows } = await Payment.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['paymentDate', 'DESC']],
      include: [
        { model: User, as: 'payer', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: User, as: 'receiver', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { 
          model: Account, 
          as: 'account',
          attributes: ['id', 'status', 'plan', 'stripeCustomerId'] 
        },
      ],
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
        },
      },
    };
  } catch (error) {
    logger.error('Error fetching payments:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch payments',
        error: error.message,
      },
    };
  }
};

/**
 * Get payment by ID
 * @param {string} id - Payment ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPaymentById = async (id) => {
  try {
    const payment = await Payment.findByPk(id, {
      include: [
        { model: PaymentReceipt, as: 'receipts' },
        { model: User, as: 'payer', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: User, as: 'receiver', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: Account, as: 'account', attributes: ['id', 'name'] },
      ],
    });

    if (!payment) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Payment not found',
        },
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: payment,
      },
    };
  } catch (error) {
    logger.error(`Error fetching payment ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch payment',
        error: error.message,
      },
    };
  }
};

/**
 * Update payment
 * @param {string} id - Payment ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user making the update
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updatePayment = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const payment = await Payment.findByPk(id, { transaction });
    
    if (!payment) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Payment not found',
        },
      };
    }

    // Prevent updating certain fields
    const { id: _, paymentMethod, amount, currency, ...safeUpdateData } = updateData;
    
    // Update payment
    await payment.update(safeUpdateData, { transaction });

    // Log the update
    await payment.createAuditLog({
      action: 'update',
      userId,
      changes: safeUpdateData,
    }, { transaction });

    await transaction.commit();
    
    // Fetch the updated payment with all associations
    const updatedPayment = await Payment.findByPk(id, {
      include: [
        { model: PaymentReceipt, as: 'receipts' },
        { model: User, as: 'payer', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: User, as: 'receiver', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: Account, as: 'account', attributes: ['id', 'name'] },
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: updatedPayment,
      },
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating payment ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update payment',
        error: error.message,
      },
    };
  }
};

/**
 * Delete payment (soft delete)
 * @param {string} id - Payment ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deletePayment = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const payment = await Payment.findByPk(id, { transaction });
    
    if (!payment) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Payment not found',
        },
      };
    }

    // Soft delete the payment
    await payment.destroy({ transaction });
    
    await transaction.commit();
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Payment deleted successfully',
      },
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting payment ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete payment',
        error: error.message,
      },
    };
  }
};

/**
 * Process Stripe webhook event for checkout.session.completed
 * @param {Object} event - Stripe event
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const handleStripeWebhook = async (event) => {
  const transaction = await sequelize.transaction();
  
  try {
    const session = event.data.object;
    
    // Log the complete session object for debugging
    logger.info('Processing webhook event type:', event.type);
    logger.debug('Complete session object:', JSON.stringify(session, null, 2));
    
    if (!session || !session.id) {
      logger.error('Invalid session object or missing session ID:', session);
      throw new Error('Invalid session data: missing session ID');
    }
    
    // Extract accountId from metadata
    const metadata = session.metadata || {};
    const accountId = metadata.accountId;
    
    // Get account-specific Stripe instance for verification if needed
    // For webhook handlers, we'll mainly use this for verification purposes
    const stripeInstance = await getStripeInstanceForAccount(accountId);
    
    // Handle different event types
    switch (event.type) {
      case 'payment_intent.created':
      case 'payment_intent.succeeded':
      case 'charge.succeeded':
      case 'charge.updated':
        // These events are handled by the checkout.session.completed event
        // We don't need to process them separately
        logger.info(`Skipping ${event.type} event - will be handled by checkout.session.completed`);
        return {
          status: 200,
          payload: { received: true, handled: false, reason: 'Handled by checkout.session.completed' }
        };
      case 'checkout.session.completed':
        // Parse metadata
        const metadata = session.metadata || {};
        
        // Get amount from session or metadata
        const calculatedAmount = session.amount_total ? session.amount_total / 100 : 0;
        
        // Extract and organize metadata
        const {
          // Core payment fields we want to keep at the top level
          userId,
          accountId: metadataAccountId,
          receiverId: metadataReceiverId,
          description,
          isSimplifiedFlow,
          clientReferenceId,
          timestamp,
          // Fields we'll move to stripe object
          email,
          amount: metadataAmount,
          currency: metadataCurrency,
          success_url,
          // Remove these from top level since they'll be in stripe object
          ...otherMetadata
        } = metadata || {};

        // Create the payment record with all required fields
        const paymentData = {
          // Required fields with defaults
          referenceNumber: session.client_reference_id || `stripe_${session.id}`,
          amount: session.amount_total ? session.amount_total / 100 : 0,
          currency: (session.currency || 'usd').toUpperCase(),
          status: 'completed',
          paymentMethod: 'stripe',
          paymentDate: new Date(),
          notes: `Payment completed via Stripe Checkout\n` +
                 `Session: ${session.id}\n` +
                 `Intent: ${session.payment_intent || 'N/A'}\n` +
                 `Customer: ${session.customer || 'N/A'}`,
          
          // Required fields that will be set by ensureRequiredFields
          payerId: null,
          receiverId: null,
          accountId: null,
          
          // Store additional data in metadata JSON
          metadata: {
            // Core payment metadata
            userId,
            accountId: metadataAccountId,
            receiverId: metadataReceiverId,
            description,
            isSimplifiedFlow,
            clientReferenceId,
            timestamp,
            
            // Stripe-specific data
            stripe: {
              // Session data
              checkoutSessionId: session.id,
              paymentIntent: session.payment_intent,
              customerId: session.customer,
              paymentMethod: session.payment_method_types?.[0],
              paymentStatus: session.payment_status,
              subscription: session.subscription,
              mode: session.mode,
              email: session.customer_email || email,
              amount: session.amount_total?.toString(),
              currency: session.currency || metadataCurrency,
              successUrl: success_url,
              
              // Any additional session metadata
              ...(session.metadata || {})
            },
            
            // Any remaining metadata that doesn't fit above
            ...otherMetadata
          },
          
          // Add any additional fields from metadata that should be top-level
          ...(otherMetadata.period && { period: otherMetadata.period }),
          ...(otherMetadata.propertyId && { propertyId: otherMetadata.propertyId })
        };
        
        // Ensure amount is a valid number
        if (isNaN(paymentData.amount) || paymentData.amount <= 0) {
          throw new Error(`Invalid payment amount: ${paymentData.amount}`);
        }
        
        // Ensure all required fields are set
        async function ensureRequiredFields() {
          if (!paymentData.accountId) {
            // Try to get account from the authenticated user if available
            if (metadata?.userId) {
              logger.info(`Looking up account for user ${metadata.userId}`);
              const user = await User.findByPk(metadata.userId, {
                include: [{
                  model: AccountUser,
                  as: 'accountUsers',
                  attributes: ['accountId'],
                  required: false
                }],
                transaction
              });
              
              if (user?.accountUsers?.length > 0) {
                paymentData.accountId = user.accountUsers[0].accountId;
                logger.info(`Found account ${paymentData.accountId} for user ${metadata.userId}`);
              } else {
                logger.warn(`No accounts found for user ${metadata.userId}`);
              }
            }
            
            // If still no accountId, try to get from session metadata
            if (!paymentData.accountId && session?.metadata?.accountId) {
              paymentData.accountId = session.metadata.accountId;
              logger.info(`Using account ID from session metadata: ${paymentData.accountId}`);
            }
            
            // If still no accountId, try to get from client_reference_id
            if (!paymentData.accountId && session?.client_reference_id) {
              // Check if client_reference_id is in format 'account_123'
              const match = session.client_reference_id.match(/^account_(.+)$/);
              if (match) {
                paymentData.accountId = match[1];
                logger.info(`Using account ID from client_reference_id: ${paymentData.accountId}`);
              }
            }
            
            if (!paymentData.accountId) {
              const errorMsg = 'No accountId found in metadata, session, or user accounts';
              logger.error(errorMsg, { 
                userId: metadata?.userId,
                sessionMetadata: session?.metadata,
                clientReferenceId: session?.client_reference_id
              });
              throw new Error(errorMsg);
            }
          }
          
          // 2. Set receiverId to the account owner
          const accountOwner = await AccountUser.findOne({
            where: { 
              accountId: paymentData.accountId,
              primaryRole: 'account_owner'
            },
            include: [{
              model: User,
              as: 'user',
              required: true,
              attributes: ['id']
            }],
            transaction
          });
          
          if (!accountOwner?.user?.id) {
            throw new Error(`No account owner found for account ${paymentData.accountId}`);
          }
          
          // Always set receiver to the account owner
          paymentData.receiverId = accountOwner.user.id;
          
          // 3. Set payerId (use provided payerId, then metadata.userId, then fallback to receiverId)
          if (!paymentData.payerId) {
            paymentData.payerId = (metadata && metadata.userId) || paymentData.receiverId;
          }
          
          // Log the final values for debugging
          logger.debug('Payment field resolution:', {
            accountId: paymentData.accountId,
            receiverId: paymentData.receiverId,
            payerId: paymentData.payerId,
            metadataUserId: metadata?.userId
          });
        }
        
        await ensureRequiredFields();
        
        // Log the payment data for debugging
        logger.info('Creating payment from webhook with data:', JSON.stringify(paymentData, null, 2));
        
        // Use Stripe Payment Intent ID as the reference number
        // This provides a direct link to the payment in Stripe's system
        if (!session.payment_intent) {
          throw new Error('Payment intent ID not found in session');
        }
        paymentData.referenceNumber = session.payment_intent;
        
        // Log the final payment data that will be saved
        logger.debug('Final payment data to be saved:', {
          ...paymentData,
          // Don't log potentially sensitive data
          metadata: '***REDACTED***'
        });
        
        let payment;
        try {
          // First, validate the payment data against the model
          const paymentInstance = Payment.build(paymentData);
          
          // Validate the instance
          try {
            await paymentInstance.validate({ transaction });
          } catch (validationError) {
            logger.error('Payment validation failed:', {
              name: validationError.name,
              message: validationError.message,
              errors: validationError.errors ? validationError.errors.map(e => ({
                path: e.path,
                message: e.message,
                value: e.value,
                type: e.type,
                validatorKey: e.validatorKey,
                validatorArgs: e.validatorArgs
              })) : 'No validation errors'
            });
            throw validationError;
          }
          
          // Create new payment record
          logger.info('Creating new payment with data:', JSON.stringify(paymentData, null, 2));
          payment = await Payment.create(paymentData, { transaction });
          logger.info(`Created new payment ${payment.id} from session ${session.id}`);
          
        } catch (error) {
          logger.error('Error during payment processing:', {
            name: error.name,
            message: error.message,
            stack: error.stack,
            paymentData: {
              ...paymentData,
              // Don't log potentially sensitive data
              metadata: '***REDACTED***',
              notes: '***REDACTED***'
            }
          });
          throw error;
        }
        
        // Log successful creation
        logger.info(`Successfully created payment ${payment.id} from session ${session.id}`);
        
        let receiverId = payment.receiverId;
        
        // If receiver ID is not set, try to get account owner
        if (!receiverId && payment.accountId) {
          const accountUser = await AccountUser.findOne({
            where: { 
              accountId: payment.accountId,
              primaryRole: 'owner'
            },
            include: [{
              model: User,
              as: 'user',
              required: true
            }],
            transaction
          });
          
          if (accountUser?.user) {
            receiverId = accountUser.user.id;
          }
        }
        
        // Update payment status and store Stripe session details
        await payment.update({
          status: 'completed',
          receiverId: receiverId || null,
          stripePaymentIntentId: session.payment_intent,
          stripeCustomerId: session.customer,
          stripePaymentMethod: session.payment_method_types?.[0],
          paidAt: new Date(),
          notes: receiverId 
            ? 'Payment completed via Stripe Checkout. Receiver set to account owner.'
            : 'Payment completed via Stripe Checkout. No receiver specified.'
        }, { transaction });
        
        await transaction.commit();
        logger.info(`Payment ${payment.id} marked as completed`);
        break;
        
      case 'checkout.session.expired':
        await Payment.update(
          { 
            status: 'expired', 
            notes: 'Checkout session expired' 
          },
          { 
            where: { stripeCheckoutSessionId: session.id },
            transaction 
          }
        );
        await transaction.commit();
        break;
        
      case 'checkout.session.cancelled':
        await Payment.update(
          { 
            status: 'cancelled',
            notes: 'Payment was cancelled by user',
            cancelledAt: new Date()
          },
          { 
            where: { stripeCheckoutSessionId: session.id },
            transaction 
          }
        );
        await transaction.commit();
        logger.info(`Payment for session ${session.id} was cancelled`);
        break;
        
      default:
        await transaction.rollback();
        logger.warn(`Unhandled event type: ${event.type}`);
        return { 
          status: 400, 
          payload: { 
            success: false, 
            error: 'Unhandled event type' 
          } 
        };
    }
    
    return { status: 200, payload: { success: true } };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error processing webhook:', error);
    throw error;
  }
};

/**
 * Get payment by checkout session ID
 * @param {string} sessionId - Stripe Checkout session ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getPaymentBySessionId = async (sessionId) => {
  try {
    const payment = await Payment.findOne({
      where: { stripeCheckoutSessionId: sessionId },
      include: [
        { model: User, as: 'payer', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: Account, as: 'account', attributes: ['id', 'name'] },
      ],
    });

    if (!payment) {
      return { status: 404, payload: { success: false, error: 'Payment not found' } };
    }

    return { status: 200, payload: { success: true, data: payment } };
  } catch (error) {
    logger.error('Error fetching payment by session ID:', error);
    throw error;
  }
};

/**
 * Create a manual payment
 * @param {Object} paymentData - Payment data
 * @param {string} userId - ID of the user creating the payment
 * @returns {Promise<Object>} Created payment
 */
const createPayment = async (paymentData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Validate required fields
    if (!paymentData.payerId || !paymentData.amount) {
      await transaction.rollback();
      return { 
        status: 400, 
        payload: { 
          success: false, 
          message: 'Missing required fields: payerId and amount are required' 
        } 
      };
    }

    // First, verify the payer exists - only fetch the id field to avoid accountId lookup
    const payer = await User.findByPk(paymentData.payerId, { 
      attributes: ['id'],
      transaction,
      raw: true // Get plain object
    });
    
    if (!payer) {
      await transaction.rollback();
      return { 
        status: 404, 
        payload: { 
          success: false, 
          message: 'Payer not found' 
        } 
      };
    }

    // Get the account ID from the request or find the payer's account
    let accountId = paymentData.accountId;
    
    if (!accountId) {
      // Find the payer's account through AccountUser without including the User model
      const accountUser = await AccountUser.findOne({
        where: { userId: paymentData.payerId },
        attributes: ['accountId'],
        transaction,
        raw: true,
        // Don't include the User model to avoid the accountId lookup
        include: []
      });
      
      if (accountUser && accountUser.accountId) {
        accountId = accountUser.accountId;
      } else {
        await transaction.rollback();
        return { 
          status: 400, 
          payload: { 
            success: false, 
            message: 'Payer is not associated with any account' 
          } 
        };
      }
    }
    
    logger.debug('Using account ID:', accountId);

    // Prepare payment data with all required fields
    const paymentToCreate = {
      amount: paymentData.amount,
      currency: paymentData.currency || 'USD',
      status: 'completed', // Manual payments are typically marked as completed immediately
      paymentMethod: paymentData.paymentMethod || 'cash',
      paymentDate: paymentData.paymentDate || new Date(),
      notes: paymentData.notes,
      payerId: paymentData.payerId,
      receiverId: userId, // The user creating the payment is the receiver
      accountId: accountId,
      referenceNumber: `manual_${Date.now()}`,
      paidAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
      metadata: {
        ...(paymentData.metadata || {}),
        isManual: true,
        processedAt: new Date().toISOString(),
      },
    };

    logger.debug('Creating payment with payload:', JSON.stringify(paymentToCreate, null, 2));
    
    // Create the payment directly
    const payment = await Payment.create(paymentToCreate, { 
      transaction,
      // Explicitly set the fields to avoid any automatic includes
      fields: [
        'amount', 'currency', 'status', 'paymentMethod', 'paymentDate',
        'notes', 'payerId', 'receiverId', 'accountId', 'metadata',
        'createdBy', 'updatedBy', 'referenceNumber', 'paidAt'
      ]
    });

    await transaction.commit();
    
    // Fetch the full payment with associations
    const fullPayment = await Payment.findByPk(payment.id, {
      include: [
        { 
          model: User, 
          as: 'payer', 
          attributes: ['id', 'firstName', 'lastName', 'email'] 
        },
        { 
          model: User, 
          as: 'receiver', 
          attributes: ['id', 'firstName', 'lastName', 'email'] 
        },
      ],
    });

    logger.info(`Payment ${payment.id} created successfully for payer ${payment.payerId}`);
    return { 
      status: 201, 
      payload: { 
        success: true, 
        data: fullPayment 
      } 
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating manual payment:', {
      error: error.message,
      stack: error.stack,
      paymentData,
      userId
    });
    
    // Handle specific error types
    if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeUniqueConstraintError') {
      return { 
        status: 400, 
        payload: { 
          success: false, 
          message: 'Validation error',
          errors: error.errors?.map(e => e.message) || [error.message]
        } 
      };
    }
    
    return { 
      status: 500, 
      payload: { 
        success: false, 
        message: 'Failed to create payment',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      } 
    };
  }
};

module.exports = {
  createCheckoutSession,
  getPaymentBySessionId,
  getAllPayments,
  getPaymentById,
  updatePayment,
  deletePayment,
  handleStripeWebhook,
  createPayment,
};

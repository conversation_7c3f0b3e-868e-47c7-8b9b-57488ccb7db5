// services/user.service.js
const { User, Account, AccountUser, RefreshToken, sequelize } = require('../models');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');
const { verifyToken } = require('../utils/token');
const { sendEmail } = require('./emailService');
const templateService = require('./templateService');
const { generateRandomPassword } = require('../utils/helpers');

/**
 * Register a new landlord user with account and default role
 * @param {Object} userData - User registration data
 * @param {Object} [options] - Optional parameters
 * @param {Object} [options.transaction] - Optional transaction to use
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const registerLandlord = async (userData, options = {}) => {
  const shouldCommit = !options.transaction;
  const transaction = options.transaction || await sequelize.transaction();
  
  try {
    // Extract password before hashing to avoid modifying the original object
    const { password, ...userDataWithoutPassword } = userData;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user
    const user = await User.create(
      {
        ...userDataWithoutPassword,
        password: hashedPassword,
      },
      { transaction }
    );

    // Create account for the user
    const account = await Account.create(
      {
        status: 'active',
        plan: 'free',
      },
      { transaction }
    );

    // Create the account-user relationship with account_owner role
    await AccountUser.create(
      {
        accountId: account.id,
        userId: user.id,
        primaryRole: 'account_owner',
        additionalRoles: [],
        isDefault: true
      },
      { transaction }
    );

    // Create default templates for the new account
    await templateService.createDefaultTemplates(
      account.id,
      user.id,
      { transaction }
    );

    // Only commit if we created the transaction
    if (shouldCommit) {
      await transaction.commit();
    }
    
    // Fetch the complete user with associations
    const newUser = await User.findByPk(user.id, {
      include: [
        {
          model: Account,
          as: 'accounts',
          through: {
            model: AccountUser,
            as: 'accountUser',
            where: { isDefault: true },
            attributes: ['primaryRole', 'additionalRoles', 'isDefault']
          }
        }
      ],
      attributes: { exclude: ['password'] },
      transaction: shouldCommit ? undefined : transaction
    });

    return {
      status: 201,
      payload: {
        success: true,
        message: 'User created successfully',
        data: newUser
      }
    };
  } catch (error) {
    // Only rollback if we created the transaction
    if (shouldCommit && transaction) {
      await transaction.rollback().catch(rollbackError => {
        logger.error('Error during transaction rollback:', rollbackError);
      });
    }
    
    logger.error('Error registering landlord:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create user',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Find a user by email
 * @param {string} email - User email
 * @param {Object} options - Additional options
 * @param {boolean} [options.includePassword=false] - Whether to include the password field
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const findByEmail = async (email, { includePassword = false } = {}) => {
  try {
    const attributes = { exclude: ['password'] };
    
    // If we need the password for authentication, include it
    if (includePassword) {
      attributes.exclude = [];
      attributes.attributes = ['id', 'email', 'password', 'firstName', 'lastName', 'isEmailVerified', 'createdAt', 'updatedAt'];
    }

    logger.debug('Finding user by email', { 
      email, 
      includePassword,
      attributes 
    });

    const user = await User.findOne({
      where: { email: email.toLowerCase().trim() },
      include: [
        {
          model: Account,
          as: 'accounts',
          through: {
            model: AccountUser,
            as: 'accountUser',
            attributes: ['accountId', 'primaryRole', 'additionalRoles', 'isDefault'] // Added accountId here
          }
        }
      ],
      ...(includePassword && { attributes: attributes.attributes })
    });
    logger.debug('User found:', { 
      userId: user?.id,
      email: user?.email,
      hasPassword: !!user?.password,
      hasAccounts: !!user?.accounts?.length
    });

    if (!user) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: user
      }
    };
  } catch (error) {
    logger.error('Error finding user by email:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to find user',
        error: error.message
      }
    };
  }
};

/**
 * Find a user by ID
 * @param {string} id - User ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const findById = async (id) => {
  try {
    const user = await User.findByPk(id, {
      include: [
        {
          model: Account,
          as: 'accounts',
          through: {
            model: AccountUser,
            as: 'accountUser',
            attributes: ['primaryRole', 'additionalRoles', 'isDefault']
          }
        }
      ],
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: user
      }
    };
  } catch (error) {
    logger.error(`Error finding user with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to find user',
        error: error.message
      }
    };
  }
};

/**
 * Update user by ID
 * @param {string} id - User ID to update
 * @param {Object} userData - User data to update
 * @param {string} accountId - The account ID from the logged-in user's context
 * @param {Object} [roleInfo] - Optional role information
 * @param {string} [roleInfo.primaryRole] - Primary role to assign
 * @param {string[]} [roleInfo.additionalRoles] - Additional roles to assign
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateUser = async (id, userData, accountId, roleInfo = {}) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Find the user with account user information if accountId is provided
    const include = [];
    if (accountId) {
      include.push({
        model: AccountUser,
        as: 'accountUsers',
        where: { accountId },
        required: true
      });
    }

    const user = await User.findByPk(id, { 
      include,
      transaction 
    });
    
    if (!user) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // If accountId is provided, check if the user belongs to that account
    if (accountId && (!user.accountUsers || user.accountUsers.length === 0)) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Access denied. User does not belong to this account.'
        }
      };
    }

    // Handle role updates if roleInfo is provided
    if (accountId && (roleInfo.primaryRole || roleInfo.additionalRoles)) {
      // Update the account user record with new roles
      const accountUser = user.accountUsers && user.accountUsers[0];
      if (accountUser) {
        await accountUser.update({
          primaryRole: roleInfo.primaryRole || accountUser.primaryRole,
          additionalRoles: roleInfo.additionalRoles !== undefined ? 
            roleInfo.additionalRoles : accountUser.additionalRoles
        }, { transaction });
      } else if (roleInfo.primaryRole) {
        // If no accountUser exists but we're trying to set roles, create the relationship
        await AccountUser.create({
          userId: id,
          accountId,
          primaryRole: roleInfo.primaryRole,
          additionalRoles: roleInfo.additionalRoles || [],
          isDefault: true // Assuming this is the only account for this user
        }, { transaction });
      }
    }

    // If password is being updated, hash it
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
      userData.passwordResetRequired = false; // Reset the password reset flag
    }

    // Update user data if there's anything to update
    if (Object.keys(userData).length > 0) {
      await user.update(userData, { transaction });
    }

    await transaction.commit();

    // Fetch the updated user with account user information
    const updatedUser = await User.findByPk(id, {
      include: [
        {
          model: AccountUser,
          as: 'accountUsers',
          attributes: ['id', 'primaryRole', 'additionalRoles', 'isDefault', 'accountId']
        }
      ],
      attributes: { exclude: ['password'] },
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: updatedUser,
        message: 'User updated successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating user with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update user',
        error: error.message
      }
    };
  }
};

/**
 * Delete user by ID (soft delete)
 * @param {string} id - User ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteUser = async (id) => {
  const transaction = await sequelize.transaction();
  
  try {
    const user = await User.findByPk(id, {
      include: [
        {
          model: Account,
          as: 'accounts',
          through: {
            model: AccountUser,
            as: 'accountUser',
            attributes: ['primaryRole', 'additionalRoles', 'isDefault']
          }
        }
      ],
      transaction
    });
    
    if (!user) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // Check if user is the last admin of any account
    for (const account of user.accounts) {
      const accountUser = account.accountUser;
      
      if (['account_owner', 'system_admin'].includes(accountUser.primaryRole)) {
        // Check if there are other admins for this account
        // Cast UUID to string to ensure proper comparison
        const adminCount = await AccountUser.count({
          where: {
            accountId: account.id,
            userId: { 
              [Op.ne]: sequelize.cast(id, 'char') // Cast UUID to string for safe comparison
            },
            primaryRole: { [Op.in]: ['account_owner', 'system_admin'] },
            deletedAt: null // Only count non-deleted account users
          },
          transaction
        });
        
        if (adminCount === 0) {
          await transaction.rollback();
          return {
            status: 400,
            payload: {
              success: false,
              message: 'Cannot delete the last admin of an account. Please assign another admin first.'
            }
          };
        }
      }
    }

    // Soft delete the user (this will set deletedAt timestamp)
    await user.destroy({ transaction });
    
    // Revoke all refresh tokens
    await RefreshToken.update(
      { isRevoked: true },
      { 
        where: { 
          userId: sequelize.cast(id, 'char'), // Cast UUID to string for safe comparison
          isRevoked: false
        },
        transaction
      }
    );

    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'User deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting user with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete user',
        error: error.message
      }
    };
  }
};

/**
 * Get all users with pagination and filtering
 * @param {Object} options - Query options (page, limit, search, accountId)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllUsers = async (options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      filters = {},
      accountId
    } = options;
    
    const { role } = filters || {};
    
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Add search condition if provided
    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.like]: `%${search}%` } },
        { lastName: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }

    // Build the base include clause with account filtering
    const includeClause = [
      {
        model: Account,
        as: 'accounts',
        required: true,
        where: accountId ? { id: accountId } : {},
        through: {
          model: AccountUser,
          as: 'accountUser',
          attributes: ['primaryRole', 'additionalRoles', 'isDefault'],
          // Exclude account owners
          where: {
            [Op.and]: [
              { primaryRole: { [Op.ne]: 'account_owner' } }
            ]
          }
        }
      }
    ];

    // Add role filter if provided
    if (role) {
      // Initialize Op.and array if it doesn't exist
      if (!includeClause[0].through.where[Op.and]) {
        includeClause[0].through.where[Op.and] = [];
      }
      
      // For MySQL, we need to use JSON_CONTAINS to check if the role exists in the additionalRoles JSON array
      includeClause[0].through.where[Op.and].push({
        [Op.or]: [
          { primaryRole: role },
          // Using JSON_CONTAINS for MySQL JSON array search
          sequelize.literal(`JSON_CONTAINS(additionalRoles, '\"${role}\"')`)
        ]
      });
    }

    const { count, rows } = await User.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: includeClause,
      attributes: { exclude: ['password'] },
      order: [['createdAt', 'DESC']],
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching users:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch users',
        error: error.message
      }
    };
  }
};

/**
 * Store a refresh token for a user
 * @param {string} userId - User ID
 * @param {string} token - Refresh token
 * @param {Object} options - Additional options
 * @param {string} [options.ipAddress] - IP address of the client
 * @param {string} [options.userAgent] - User agent of the client
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const storeRefreshToken = async (userId, token, { ipAddress, userAgent } = {}) => {
  const decoded = verifyToken(token, true);
  if (!decoded) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'Invalid refresh token'
      }
    };
  }

  try {
    // Log the userId type for debugging
    logger.debug('Revoking refresh tokens for user', { 
      userId, 
      userIdType: typeof userId, 
      isBuffer: Buffer.isBuffer(userId)
    });
    
    // Let Sequelize handle the UUID type conversion - don't use raw SQL functions
    await RefreshToken.update(
      { isRevoked: true },
      { 
        where: { 
          userId: userId, // Use the UUID string directly
          isRevoked: false,
          expiresAt: { [Op.gt]: new Date() }
        } 
      }
    );
  } catch (error) {
    logger.error('Error revoking previous refresh tokens:', error);
    // Continue with token creation even if revocation fails
  }

  // Create new refresh token
  const refreshToken = await RefreshToken.create({
    token,
    userId,
    expiresAt: new Date(decoded.exp * 1000), // Convert to milliseconds
    ipAddress,
    userAgent,
  });

  return {
    status: 201,
    payload: {
      success: true,
      message: 'Refresh token created successfully',
      data: refreshToken
    }
  };
};

/**
 * Verify if a refresh token is valid
 * @param {string} userId - User ID
 * @param {string} token - Refresh token
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const verifyRefreshToken = async (userId, token) => {
  try {
    // Use direct UUID comparison instead of raw SQL
    const refreshToken = await RefreshToken.findOne({
      where: {
        token,
        userId: userId, // Let Sequelize handle the UUID conversion
        isRevoked: false,
        expiresAt: { [Op.gt]: new Date() },
      },
    });

    if (!refreshToken) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Refresh token not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: refreshToken
      }
    };
  } catch (error) {
    logger.error('Error verifying refresh token:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to verify refresh token',
        error: error.message
      }
    };
  }
};

/**
 * Update a refresh token (token rotation)
 * @param {string} userId - User ID
 * @param {string} oldToken - Old refresh token
 * @param {string} newToken - New refresh token
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateRefreshToken = async (userId, oldToken, newToken) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Mark old token as revoked and set replacedByToken
    await RefreshToken.update(
      { 
        isRevoked: true,
        replacedByToken: newToken,
      },
      { 
        where: { 
          token: oldToken,
          userId: userId, // Let Sequelize handle the UUID conversion
          isRevoked: false,
        },
        transaction,
      }
    );

    // Store new refresh token
    const decoded = verifyToken(newToken, true);
    if (!decoded) {
      throw new Error('Invalid new refresh token');
    }

    await RefreshToken.create(
      {
        token: newToken,
        userId,
        expiresAt: new Date(decoded.exp * 1000),
      },
      { transaction }
    );

    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Refresh token updated successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating refresh token:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update refresh token',
        error: error.message
      }
    };
  }
};

/**
 * Remove a refresh token (logout)
 * @param {string} userId - User ID
 * @param {string} token - Refresh token to remove
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const removeRefreshToken = async (userId, token) => {
  try {
    const result = await RefreshToken.destroy({
      where: {
        userId: userId, // Let Sequelize handle the UUID conversion
        token,
      },
    });

    if (result === 0) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Refresh token not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Refresh token removed successfully'
      }
    };
  } catch (error) {
    logger.error('Error removing refresh token:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to remove refresh token',
        error: error.message
      }
    };
  }
};

/**
 * Revoke all refresh tokens for a user
 * @param {string} userId - User ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const revokeAllRefreshTokens = async (userId) => {
  try {
    const result = await RefreshToken.update(
      { isRevoked: true },
      { 
        where: { 
          userId: userId, // Let Sequelize handle the UUID conversion
          isRevoked: false,
        } 
      }
    );

    if (result[0] === 0) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'No refresh tokens found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Refresh tokens revoked successfully'
      }
    };
  } catch (error) {
    logger.error('Error revoking refresh tokens:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to revoke refresh tokens',
        error: error.message
      }
    };
  }
};

/**
 * Get user by ID with account and roles
 * @param {string} userId - User ID
 * @param {string} accountId - Account ID for security check
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUserById = async (userId, accountId) => {
  try {
    logger.debug('Getting user by ID with account check', { userId, accountId });
    
    // First, find the user without account filtering to ensure it exists
    const user = await User.findByPk(userId, {
      include: [
        { 
          model: AccountUser,
          as: 'accountUsers',
          attributes: ['id', 'primaryRole', 'additionalRoles', 'isDefault', 'accountId'],
          required: false // Don't filter by accountUsers yet
        }
      ],
      attributes: { exclude: ['password'] }
    });
    
    logger.debug('User query result', {
      userId, 
      found: !!user, 
      accountUsers: user?.accountUsers?.length || 0
    });

    if (!user) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // If accountId was provided, explicitly check if user belongs to that account
    if (accountId) {
      // Check if any of the user's accountUsers records match the requested accountId
      const hasAccountAccess = user.accountUsers?.some(au => {
        // Use string comparison to avoid binary/string UUID issues
        const auAccountId = au.accountId?.toString();
        const requestedAccountId = accountId?.toString();
        
        logger.debug('Comparing account IDs', {
          auAccountId,
          requestedAccountId,
          matches: auAccountId === requestedAccountId
        });
        
        return auAccountId === requestedAccountId;
      });
      
      if (!hasAccountAccess) {
        logger.warn('Access denied - user does not belong to account', { 
          userId, 
          accountId, 
          userAccountCount: user.accountUsers?.length || 0
        });
        
        return {
          status: 403,
          payload: {
            success: false,
            message: 'Access denied. User does not belong to this account.'
          }
        };
      }
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: user
      }
    };
  } catch (error) {
    logger.error('Error fetching user by ID:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch user',
        error: error.message
      }
    };
  }
};

/**
 * Create a new user
 * @param {Object} userData - User data
 * @param {Object} roleInfo - Role information
 * @param {string} accountId - Account ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createUser = async (userData, roleInfo = {}, accountId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Check if email already exists
    const existingUser = await User.findOne({
      where: { email: userData.email },
      transaction
    });

    if (existingUser) {
      await transaction.rollback();
      return {
        status: 409,
        payload: {
          success: false,
          message: 'Email already in use'
        }
      };
    }

    // Generate a random password
    const generatedPassword = generateRandomPassword(12);
    
    // Hash the generated password
    const hashedPassword = await bcrypt.hash(generatedPassword, 10);
    
    // Create user with the hashed password
    const user = await User.create(
      {
        ...userData,
        password: hashedPassword,
        // Set a flag to indicate the user needs to change their password on first login
        passwordChangedAt: new Date(0) // This will force password change on first login
      },
      { transaction }
    );

    // Create AccountUser record with roles if accountId is provided
    if (accountId) {
      // Validate primary role
      const validPrimaryRoles = ['account_owner', 'property_manager', 'leasing_agent', 'maintenance_staff', 'tenant'];
      const primaryRole = roleInfo.primaryRole || 'tenant';
      
      if (!validPrimaryRoles.includes(primaryRole)) {
        await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Invalid primary role specified'
          }
        };
      }

      // Validate additional roles
      const validAdditionalRoles = ['property_manager', 'leasing_agent', 'maintenance_staff', 'tenant'];
      const additionalRoles = Array.isArray(roleInfo.additionalRoles) 
        ? roleInfo.additionalRoles.filter(role => validAdditionalRoles.includes(role))
        : [];

      // Remove primary role from additional roles if it exists there
      const filteredAdditionalRoles = additionalRoles.filter(role => role !== primaryRole);

      await AccountUser.create({
        accountId,
        userId: user.id,
        primaryRole,
        additionalRoles: filteredAdditionalRoles,
        isDefault: false
      }, { transaction });
    }

    await transaction.commit();

    // Send welcome email with the generated password
    try {
      await sendEmail({
        to: userData.email,
        subject: 'Welcome to GoodTenant - Your Account Details',
        text: `Welcome to GoodTenant!\n\n` +
              `Your account has been created. Here are your login details:\n` +
              `Email: ${userData.email}\n` +
              `Temporary Password: ${generatedPassword}\n\n` +
              `For security reasons, you will be asked to change your password on first login.\n\n` +
              `Best regards,\nThe GoodTenant Team`
      });
    } catch (emailError) {
      // Log the email error but don't fail the user creation
      logger.error('Failed to send welcome email:', emailError);
    }

    // Get the created user with associations
    const createdUser = await getUserById(user.id, accountId);
    return {
      status: 201,
      payload: {
        success: true,
        message: 'User created successfully. A welcome email with login details has been sent.',
        data: createdUser.payload.data
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating user:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create user',
        error: error.message
      }
    };
  }
};

/**
 * Update user details
 * @param {string} userId - User ID to update
 * @param {Object} updates - Fields to update
 * @param {Object} roleInfo - Role information
 * @param {string} accountId - Account ID for security check
 * @param {string} currentUserId - ID of the current user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateUserDetails = async (userId, updates, roleInfo, accountId, currentUserId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Prevent users from updating other users unless they're admins
    const isAdmin = await isUserAdmin(currentUserId, accountId);
    if (userId !== currentUserId && !isAdmin) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Not authorized to update this user'
        }
      };
    }

    const user = await User.findOne({
      where: { id: userId },
      include: [
        {
          model: Account,
          as: 'accounts',
          through: {
            model: AccountUser,
            as: 'accountUser',
            where: { accountId },
            attributes: ['primaryRole', 'additionalRoles', 'isDefault']
          }
        }
      ],
      transaction
    });

    if (!user) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // Prevent updating system admin accounts
    if (user.isSystemAdmin && userId !== currentUserId) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Cannot update system admin accounts'
        }
      };
    }

    // Update user fields
    const userUpdates = { ...updates };
    
    // Hash new password if provided
    if (userUpdates.password) {
      userUpdates.password = await bcrypt.hash(userUpdates.password, 10);
    }
    
    await user.update(userUpdates, { transaction });

    // Update roles if provided
    if (roleInfo) {
      // Validate primary role
      const validPrimaryRoles = ['account_owner', 'property_manager', 'leasing_agent', 'maintenance_staff', 'tenant'];
      const primaryRole = roleInfo.primaryRole || 'tenant';
      
      if (!validPrimaryRoles.includes(primaryRole)) {
        await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Invalid primary role specified'
          }
        };
      }

      // Validate additional roles
      const validAdditionalRoles = ['property_manager', 'leasing_agent', 'maintenance_staff', 'tenant'];
      const additionalRoles = Array.isArray(roleInfo.additionalRoles) 
        ? roleInfo.additionalRoles.filter(role => validAdditionalRoles.includes(role))
        : [];

      // Remove primary role from additional roles if it exists there
      const filteredAdditionalRoles = additionalRoles.filter(role => role !== primaryRole);

      await AccountUser.update({
        primaryRole,
        additionalRoles: filteredAdditionalRoles
      }, {
        where: {
          accountId,
          userId
        },
        transaction
      });
    }

    await transaction.commit();

    // Get the updated user with associations
    const updatedUser = await getUserById(userId, accountId);
    return {
      status: 200,
      payload: {
        success: true,
        data: updatedUser.payload.data,
        message: 'User updated successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating user:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update user',
        error: error.message
      }
    };
  }
};

/**
 * Update current user's profile
 * @param {string} userId - User ID
 * @param {Object} updates - Profile updates
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateProfile = async (userId, updates) => {
  const transaction = await sequelize.transaction();
  const allowedFields = [
    'firstName', 
    'lastName', 
    'phone',
    'addressLine1',
    'addressLine2',
    'city',
    'state',
    'postalCode',
    'country'
  ];
  
  // Filter allowed fields
  const profileUpdates = {};
  Object.keys(updates).forEach(key => {
    if (allowedFields.includes(key)) {
      profileUpdates[key] = updates[key];
    }
  });

  if (Object.keys(profileUpdates).length === 0) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'No valid fields to update'
      }
    };
  }

  try {
    const user = await User.findByPk(userId, { transaction });
    
    if (!user) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // Update the user's profile
    await user.update(profileUpdates, { transaction });
    await transaction.commit();

    // Fetch the updated user with account information
    const updatedUser = await User.findByPk(userId, {
      include: [
        {
          model: AccountUser,
          as: 'accountUsers',
          attributes: ['id', 'primaryRole', 'additionalRoles', 'isDefault', 'accountId']
        }
      ],
      attributes: { exclude: ['password'] },
    });

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Profile updated successfully',
        data: updatedUser
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating profile:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update profile',
        error: error.message
      }
    };
  }
};

/**
 * Change user password
 * @param {string} userId - User ID
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @param {string} accountId - Account ID for security check
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const changePassword = async (userId, currentPassword, newPassword, accountId) => {
  try {
    // First find the user with their password
    const user = await User.findByPk(userId, {
      attributes: ['id', 'password']
    });

    if (!user) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // Verify the user belongs to the specified account
    const accountUser = await AccountUser.findOne({
      where: { userId, accountId },
      attributes: ['id']
    });

    if (!accountUser) {
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Access denied. User does not belong to this account.'
        }
      };
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Current password is incorrect'
        }
      };
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await user.update({ password: hashedPassword });

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Password changed successfully'
      }
    };
  } catch (error) {
    logger.error('Error changing password:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to change password',
        error: error.message
      }
    };
  }
};

/**
 * Check if user is an admin
 * @private
 */
const isUserAdmin = async (userId, accountId) => {
  try {
    logger.debug('Checking if user is admin', { userId, accountId });

    // Find the account user relationship directly using the UUID strings
    // Let Sequelize handle the type conversion to match the DB type
    const accountUser = await AccountUser.findOne({
      where: {
        userId: userId,
        accountId: accountId,
      },
    });

    if (!accountUser) {
      logger.debug('Account user relationship not found', { userId, accountId });
      return false;
    }

    // Check if user is an admin (either primary or additional role)
    const isAdmin = accountUser.primaryRole === 'account_owner' || 
                  (accountUser.additionalRoles && 
                   accountUser.additionalRoles.includes('account_owner'));

    logger.debug('Admin check result', { 
      userId, 
      accountId,
      primaryRole: accountUser.primaryRole,
      additionalRoles: accountUser.additionalRoles,
      isAdmin
    });

    return isAdmin;
  } catch (error) {
    logger.error('Error in isUserAdmin:', { 
      error: error.message, 
      stack: error.stack,
      userId,
      accountId
    });
    throw error;
  }
};

module.exports = {
  registerLandlord,
  findByEmail,
  findById,
  updateUser,
  deleteUser,
  getAllUsers,
  storeRefreshToken,
  verifyRefreshToken,
  updateRefreshToken,
  removeRefreshToken,
  revokeAllRefreshTokens,
  getUserById,
  createUser,
  updateUserDetails,
  updateProfile,
  changePassword,
  isUserAdmin
};
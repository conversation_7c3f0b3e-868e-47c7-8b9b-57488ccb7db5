// services/maintenanceService.js
const { MaintenanceTicket, MaintenanceFile, User, Property, Account, sequelize } = require('../models');
const fileService = require('./fileService');
const logger = require('../utils/logger');

const createTicket = async (ticketData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { propertyId } = ticketData;
    
    if (!propertyId) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Property ID is required for creating a maintenance ticket'
        }
      };
    }

    // Verify the property exists and belongs to the user's account
    const property = await Property.findOne({
      where: { 
        id: propertyId,
        accountId: ticketData.accountId
      },
      transaction
    });

    if (!property) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Property not found or you do not have permission to create a ticket for this property'
        }
      };
    }

    const ticket = await MaintenanceTicket.create({
      ...ticketData,
      reportedBy: userId,
      status: 'open',
      propertyId: propertyId
    }, { transaction });

    // Fetch the created ticket with its associations
    const createdTicket = await MaintenanceTicket.findByPk(ticket.id, {
      include: [
        {
          model: User,
          as: 'reporter',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'city', 'state', 'postalCode']
        }
      ],
      transaction
    });

    await transaction.commit();
    return {
      status: 201,
      payload: {
        success: true,
        data: createdTicket
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating maintenance ticket:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create maintenance ticket',
        error: error.message
      }
    };
  }
};



const getTicketById = async (ticketId, userId) => {
  try {
    const ticket = await MaintenanceTicket.findByPk(ticketId, {
      include: [
        {
          model: User,
          as: 'reporter',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country']
        },
        {
          model: MaintenanceFile,
          as: 'files',
          include: [{
            model: User,
            as: 'uploader',
            attributes: ['id', 'firstName', 'lastName']
          }]
        }
      ]
    });

    if (!ticket) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Ticket not found'
        }
      };
    }


    return {
      status: 200,
      payload: {
        success: true,
        data: ticket
      }
    };
  } catch (error) {
    logger.error('Error fetching maintenance ticket:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch maintenance ticket',
        error: error.message
      }
    };
  }
};

const updateTicket = async (ticketId, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const ticket = await MaintenanceTicket.findByPk(ticketId);
    
    if (!ticket) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Ticket not found'
        }
      };
    }

    // Check if user has permission to update this ticket
    const hasAccess = await checkTicketAccess(ticket, userId);
    if (!hasAccess) {
      return {
        status: 403,
        payload: {
          success: false,
          message: 'You do not have permission to update this ticket'
        }
      };
    }

    // Update ticket fields
    const allowedUpdates = ['title', 'description', 'priority'];
    const updates = {};
    
    Object.keys(updateData).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = updateData[key];
      }
    });

    await ticket.update(updates, { transaction });
    await transaction.commit();
    
    // Get updated ticket with associations
    const updatedTicket = await getTicketById(ticketId, userId);
    
    // If getTicketById was successful, return the ticket data directly
    if (updatedTicket.payload.success) {
      return {
        status: 200,
        payload: {
          success: true,
          data: updatedTicket.payload.data
        }
      };
    }
    
    // If getTicketById failed, return the error
    return updatedTicket;
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating maintenance ticket:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update maintenance ticket',
        error: error.message
      }
    };
  }
};

const assignTicket = async (ticketId, assigneeId, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const [ticket, assignee] = await Promise.all([
      MaintenanceTicket.findByPk(ticketId),
      User.findByPk(assigneeId, {
        attributes: ['id', 'firstName', 'lastName', 'email']
      })
    ]);
    
    if (!ticket) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Ticket not found'
        }
      };
    }
    
    if (!assignee) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Assignee not found'
        }
      };
    }

    // Check if user has permission to assign this ticket
    const hasAccess = await checkTicketAccess(ticket, userId);
    if (!hasAccess) {
      return {
        status: 403,
        payload: {
          success: false,
          message: 'You do not have permission to assign this ticket'
        }
      };
    }

    // Update assignee
    await ticket.update({ assignedTo: assigneeId }, { transaction });
    await transaction.commit();
    
    // Get updated ticket with associations
    const updatedTicket = await getTicketById(ticketId, userId);
    
    return {
      status: 200,
      payload: {
        success: true,
        message: `Ticket assigned to ${assignee.firstName} ${assignee.lastName}`,
        data: updatedTicket.payload.data
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error assigning ticket:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to assign ticket',
        error: error.message
      }
    };
  }
};

const updateTicketStatus = async (ticketId, status, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const validStatuses = ['open', 'in_progress', 'on_hold', 'completed', 'cancelled'];
    
    if (!validStatuses.includes(status)) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
        }
      };
    }
    
    const ticket = await MaintenanceTicket.findByPk(ticketId);
    
    if (!ticket) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Ticket not found'
        }
      };
    }

    // Check if user has permission to update this ticket's status
    const hasAccess = await checkTicketAccess(ticket, userId);
    if (!hasAccess) {
      return {
        status: 403,
        payload: {
          success: false,
          message: 'You do not have permission to update this ticket status'
        }
      };
    }

    // Special handling for completed status
    const updateData = { status };
    if (status === 'completed') {
      updateData.completedAt = new Date();
    } else if (status !== 'completed' && ticket.status === 'completed') {
      // If changing from completed to another status, clear completedAt
      updateData.completedAt = null;
    }

    await ticket.update(updateData, { transaction });
    await transaction.commit();
    
    // Get updated ticket with associations
    const updatedTicket = await getTicketById(ticketId, userId);
    
    return {
      status: 200,
      payload: {
        success: true,
        message: `Ticket status updated to ${status.replace('_', ' ')}`,
        data: updatedTicket.payload.data
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating ticket status:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update ticket status',
        error: error.message
      }
    };
  }
};

// Helper function to check if user has access to a ticket
const checkTicketAccess = async (ticket, userId) => {
  // Check if user is admin, property owner, or the reporter
  const [isAdmin, isPropertyOwner, isReporter] = await Promise.all([
    // Implement your admin check logic
    // isUserAdmin(userId),
    // Check if user is property owner
    // isPropertyOwner(ticket.propertyId, userId),
    // Check if user is the reporter
    ticket.reportedBy === userId
  ]);

  return isAdmin || isPropertyOwner || isReporter;
};

const getTickets = async (filters = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      status,
      priority,
      propertyId,
      reportedBy,
      assignedTo,
      accountId
    } = filters;

    const offset = (page - 1) * limit;

    const whereClause = { accountId };

    // Add filters if provided
    if (status) whereClause.status = status;
    if (priority) whereClause.priority = priority;
    if (propertyId) whereClause.propertyId = propertyId;
    if (reportedBy) whereClause.reportedBy = reportedBy;
    if (assignedTo) whereClause.assignedTo = assignedTo;

    const { count, rows } = await MaintenanceTicket.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'reporter',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'assignee',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country'],
        },
      ],
      order: [[sortBy, sortOrder]],
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
        },
      },
    };
  } catch (error) {
    logger.error('Error fetching maintenance tickets:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch maintenance tickets',
        error: error.message,
      },
    };
  }
};

module.exports = {
  createTicket,
  getTicketById,
  updateTicket,
  assignTicket,
  updateTicketStatus,
  getTickets
};
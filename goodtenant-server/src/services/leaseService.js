const fileService = require('./fileService');
const { Lease, Property, User, LeaseTenant, } = require('../models');
const { sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const templateService = require('./templateService');
const pdfService = require('./pdfService');

/**
 * Create a new lease
 * @param {Object} leaseData - Lease data
 * @param {string} userId - ID of the user creating the lease
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createLease = async (leaseData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Add landlord ID if not provided (default to current user)
    if (!leaseData.landlordId) {
      leaseData.landlordId = userId;
    }

    // Create the lease without requiring tenantIds
    const lease = await Lease.create(leaseData, { transaction });

    // Add tenants to lease if provided (can be added later during tenant onboarding)
    if (leaseData.tenantIds && Array.isArray(leaseData.tenantIds) && leaseData.tenantIds.length > 0) {
      await lease.setTenants(leaseData.tenantIds, { transaction });
    }

    await transaction.commit();
    
    // Fetch the complete lease with associations
    const createdLease = await getLeaseById(lease.id, userId);
    
    return {
      status: 201,
      payload: {
        success: true,
        data: createdLease,
        message: leaseData.tenantIds && leaseData.tenantIds.length > 0 
          ? 'Lease created successfully with tenants' 
          : 'Lease created successfully. You can add tenants later during the onboarding process.'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating lease:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to create lease',
        error: error.message
      }
    };
  }
};

/**
 * Get all leases with optional filtering and pagination
 * @param {Object} options - Query options (page, limit, search, status, etc.)
 * @param {string} userId - ID of the user making the request
 * @param {boolean} isAdmin - Whether the user is an admin
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAllLeases = async (options = {}, userId) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      status,
      propertyId,
      landlordId,
      tenantId
    } = options;

    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    // Filter by property ID if provided
    if (propertyId) {
      whereClause.propertyId = propertyId;
    }

    // Filter by landlord ID if provided
    if (landlordId) {
      whereClause.landlordId = landlordId;
    }

    // Filter by tenant ID if provided
    if (tenantId) {
      whereClause['$tenants.id$'] = tenantId;
    }

    // Build search conditions if search term is provided
    if (search) {
      whereClause[Op.or] = [
        { id: { [Op.like]: `%${search}%` } },
        { notes: { [Op.like]: `%${search}%` } },
        sequelize.where(
          sequelize.fn('DATE_FORMAT', sequelize.col('start_date'), '%Y-%m-%d'),
          { [Op.like]: `%${search}%` }
        ),
        sequelize.where(
          sequelize.fn('DATE_FORMAT', sequelize.col('end_date'), '%Y-%m-%d'),
          { [Op.like]: `%${search}%` }
        )
      ];
    }

    const include = [
      {
        model: Property,
        as: 'property',
        attributes: ['id', 'name', 'addressLine1', 'city', 'state', 'postalCode'],
        required: false,
        where: search ? {
          [Op.or]: [
            { name: { [Op.like]: `%${search}%` } },
            { addressLine1: { [Op.like]: `%${search}%` } },
            { city: { [Op.like]: `%${search}%` } },
            { state: { [Op.like]: `%${search}%` } },
            { postalCode: { [Op.like]: `%${search}%` } }
          ]
        } : null
      },
      {
        model: User,
        as: 'landlord',
        attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        required: false,
        where: search ? {
          [Op.or]: [
            { firstName: { [Op.like]: `%${search}%` } },
            { lastName: { [Op.like]: `%${search}%` } }
          ]
        } : null
      },
      {
        model: User,
        as: 'tenants',
        attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        through: { attributes: [] },
        required: false,
        where: search ? {
          [Op.or]: [
            { firstName: { [Op.like]: `%${search}%` } },
            { lastName: { [Op.like]: `%${search}%` } }
          ]
        } : null
      }
    ];

    const { count, rows } = await Lease.findAndCountAll({
      where: whereClause,
      include,
      distinct: true,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['startDate', 'DESC']]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching leases:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch leases',
        error: error.message
      }
    };
  }
};

/**
 * Get lease by ID
 * @param {string} id - Lease ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getLeaseById = async (id, userId) => {
  try {
    const lease = await Lease.findByPk(id, {
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode']
        },
        {
          model: User,
          as: 'landlord',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        },
        {
          model: User,
          as: 'tenants',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
          through: { attributes: [] }
        }
      ]
    });

    if (!lease) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found'
        }
      };
    }

    return {
      status: 200,
      payload: {
        success: true,
        data: lease
      }
    };
  } catch (error) {
    logger.error(`Error fetching lease ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch lease',
        error: error.message
      }
    };
  }
};

/**
 * Update lease
 * @param {string} id - Lease ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateLease = async (id, updateData, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const lease = await Lease.findByPk(id, { 
      transaction,
      include: [{
        model: User,
        as: 'tenants',
        attributes: ['id'],
        through: { attributes: [] }
      }]
    });

    if (!lease) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found'
        }
      };
    }


    // Update lease fields
    const { tenantIds, ...leaseData } = updateData;
    await lease.update(leaseData, { transaction });

    // Update tenants if provided
    if (tenantIds && Array.isArray(tenantIds)) {
      await lease.setTenants(tenantIds, { transaction });
    }

    await transaction.commit();

    // Fetch the updated lease with associations
    const updatedLease = await getLeaseById(id, userId);
    
    return {
      status: 200,
      payload: {
        success: true,
        data: updatedLease
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating lease ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update lease',
        error: error.message
      }
    };
  }
};

/**
 * Delete lease
 * @param {string} id - Lease ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteLease = async (id, userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    const lease = await Lease.findByPk(id, { transaction });

    if (!lease) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found'
        }
      };
    }


    // Soft delete the lease
    await lease.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Lease deleted successfully'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting lease ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete lease',
        error: error.message
      }
    };
  }
};

/**
 * Generate lease document content for frontend rendering
 * @param {string} leaseId - ID of the lease
 * @param {string} [token] - Optional invitation token for public access
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with document content
 */
const generateLeaseDocumentContent = async (leaseId, token = null) => {
  try {
    const templateService = require('./templateService');
    let propertyId = null;
    
    // If using token validation, verify it first
    if (token) {
      const { TenantInvitation } = require('../models');
      const { Op } = require('sequelize');
      
      const invitation = await TenantInvitation.findOne({
        where: {
          token,
          status: 'pending',
          expiresAt: { [Op.gt]: new Date() }
        },
        include: [
          {
            model: Property,
            as: 'property',
            attributes: ['id']
          }
        ]
      });

      if (!invitation || !invitation.property) {
        return {
          status: 401,
          payload: {
            success: false,
            message: 'Invalid or expired invitation token'
          }
        };
      }
      
      // Store the property ID from the invitation
      propertyId = invitation.property.id;
    }
    
    // Build the where clause for the lease query
    const whereClause = { id: leaseId };
    if (propertyId) {
      whereClause.propertyId = propertyId;
    }
    
    // Get the lease with all needed information
    const completeLeaseData = await Lease.findOne({
      where: whereClause,
      include: [
        {
          model: Property,
          as: 'property',
          attributes: ['id', 'accountId', 'name', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country']
        },
        {
          model: User,
          as: 'landlord',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    });
    
    if (!completeLeaseData) {
      logger.warn(`Lease ${leaseId} not found when generating document content`);
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found'
        }
      };
    }
    
    // Get tenants separately
    const leaseTenants = await sequelize.models.LeaseTenant.findAll({
      where: { leaseId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    }) || [];
    
    // Add tenants to lease object
    completeLeaseData.tenants = leaseTenants
      .filter(lt => lt && lt.user)
      .map(tenantRel => tenantRel.user);
      
    // Find active lease template for the property's account
    const templatesResponse = await templateService.getAllTemplates({
      accountId: completeLeaseData.property.accountId,
      type: 'LEASE',
      isActive: true,
      limit: 1
    });
    
    if (templatesResponse.status !== 200 || 
        !templatesResponse.payload?.success || 
        !templatesResponse.payload?.data?.length) {
      logger.warn(`No active lease template found for account ${completeLeaseData.property.accountId}`);
      return {
        status: 404,
        payload: {
          success: false,
          message: 'No active lease template found'
        }
      };
    }
    
    const leaseTemplate = templatesResponse.payload.data[0];
    
    // Generate standard variables for template replacement using the centralized function
    const variables = templateService.generateStandardVariables({
      lease: completeLeaseData,
      property: completeLeaseData.property,
      tenants: completeLeaseData.tenants,
      landlord: completeLeaseData.landlord
    });
    
    // Process the template content by replacing variables
    const documentContent = templateService.replaceVariablesInTemplate(leaseTemplate.content, variables);
    
    logger.info(`Successfully generated lease document content for lease ${leaseId}`);
    
    return {
      status: 200,
      payload: {
        success: true,
        message: 'Lease document content generated successfully',
        data: {
          documentContent,
          documentName: `Lease Agreement - ${completeLeaseData.property.name}`,
          templateId: leaseTemplate.id,
          leaseId: leaseId
        }
      }
    };
  } catch (error) {
    logger.error('Error generating lease document content:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to generate lease document content',
        error: error.message
      }
    };
  }
};

module.exports = {
  createLease,
  getAllLeases,
  getLeaseById,
  updateLease,
  deleteLease,
  generateLeaseDocumentContent
};
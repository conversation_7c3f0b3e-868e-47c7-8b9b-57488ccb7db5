const { Sequelize } = require('sequelize');
const { TokenBlacklist } = require('../models');
const logger = require('../utils/logger');

/**
 * Add token to blacklist
 * @param {string} token - The JWT token to blacklist
 * @param {Date} expiresAt - When the token expires (to auto-clean from blacklist)
 */
const addToBlacklist = async (token, expiresAt) => {
  try {
    const tokenHash = TokenBlacklist.hashToken(token);
    await TokenBlacklist.create({
      tokenHash,
      expiresAt: expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000), // Default 24h
    });
  } catch (error) {
    logger.error('Error adding token to blacklist:', error);
    throw error;
  }
};

/**
 * Check if token is blacklisted
 * @param {string} token - The JWT token to check
 * @returns {Promise<boolean>} - True if token is blacklisted
 */
const isBlacklisted = async (token) => {
  try {
    const tokenHash = TokenBlacklist.hashToken(token);
    const blacklisted = await TokenBlacklist.findOne({
      where: { tokenHash },
    });
    return !!blacklisted;
  } catch (error) {
    logger.error('Error checking token blacklist:', error);
    return true; // Fail safe - if there's an error, assume token is blacklisted
  }
};

/**
 * Clean up expired tokens from blacklist
 */
const cleanupExpiredTokens = async () => {
  try {
    await TokenBlacklist.destroy({
      where: {
        expiresAt: {
          [Sequelize.Op.lt]: new Date(),
        },
      },
    });
  } catch (error) {
    logger.error('Error cleaning up expired tokens:', error);
  }
};

// Run cleanup every hour
setInterval(cleanupExpiredTokens, 60 * 60 * 1000);

module.exports = {
  addToBlacklist,
  isBlacklisted,
  cleanupExpiredTokens,
};

const { sequelize, AccountUser, User, Account } = require('../models');
const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Add a user to an account with specified role(s)
 * @param {Object} params - Parameters
 * @param {string} params.accountId - The account ID
 * @param {string} params.userId - The user ID
 * @param {string} params.primaryRole - The primary role for this user (account_owner, property_manager, etc.)
 * @param {Array} [params.additionalRoles] - Additional roles for this user (optional)
 * @param {boolean} [params.isDefault=false] - Whether this is the user's default account
 * @param {Object} [transaction] - Optional transaction object
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const addUserToAccount = async (params, transaction = null) => {
  const { accountId, userId, primaryRole, additionalRoles = [], isDefault = false } = params;

  if (!accountId || !userId || !primaryRole) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'Account ID, user ID, and primary role are required'
      }
    };
  }

  const shouldCommit = !transaction;
  const t = transaction || await sequelize.transaction({
    isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
  });

  try {
    // Check if account exists
    const account = await Account.findByPk(accountId, { transaction: t });
    if (!account) {
      if (shouldCommit) await t.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Account not found'
        }
      };
    }

    // Check if user exists
    const user = await User.findByPk(userId, { transaction: t });
    if (!user) {
      if (shouldCommit) await t.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found'
        }
      };
    }

    // Check if user is already in this account
    const existingAccountUser = await AccountUser.findOne({
      where: { accountId, userId },
      transaction: t
    });

    if (existingAccountUser) {
      if (shouldCommit) await t.rollback();
      return {
        status: 400,
        payload: {
          success: false,
          message: 'User is already a member of this account'
        }
      };
    }

    // If this is set as default, unset any existing default account for this user
    if (isDefault) {
      await AccountUser.update(
        { isDefault: false },
        {
          where: { userId, isDefault: true },
          transaction: t
        }
      );
    }

    // Create the account-user relationship
    const accountUser = await AccountUser.create(
      {
        accountId,
        userId,
        primaryRole,
        additionalRoles,
        isDefault
      },
      { transaction: t }
    );

    if (shouldCommit) await t.commit();

    return {
      status: 201,
      payload: {
        success: true,
        message: 'User added to account',
        data: accountUser
      }
    };
  } catch (error) {
    if (shouldCommit) await t.rollback();
    logger.error('Error adding user to account:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to add user to account',
        error: error.message
      }
    };
  }
};

/**
 * Get all users for an account
 * @param {string} accountId - The account ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getAccountUsers = async (accountId) => {
  if (!accountId) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'Account ID is required'
      }
    };
  }

  try {
    const accountUsers = await AccountUser.findAll({
      where: { accountId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: accountUsers
      }
    };
  } catch (error) {
    logger.error(`Error getting users for account ${accountId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to get account users',
        error: error.message
      }
    };
  }
};

/**
 * Update a user's roles within an account
 * @param {Object} params - Parameters
 * @param {string} params.accountId - The account ID
 * @param {string} params.userId - The user ID
 * @param {string} [params.primaryRole] - The primary role to update (optional)
 * @param {Array} [params.additionalRoles] - Additional roles to update (optional)
 * @param {boolean} [params.isDefault] - Whether this is the user's default account (optional)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateAccountUserRoles = async (params) => {
  const { accountId, userId, primaryRole, additionalRoles, isDefault } = params;

  if (!accountId || !userId) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'Account ID and user ID are required'
      }
    };
  }

  // Create an update object with only the fields that are provided
  const updateData = {};
  if (primaryRole !== undefined) updateData.primaryRole = primaryRole;
  if (additionalRoles !== undefined) updateData.additionalRoles = additionalRoles;
  if (isDefault !== undefined) updateData.isDefault = isDefault;

  // If no fields to update, return error
  if (Object.keys(updateData).length === 0) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'No fields to update provided'
      }
    };
  }

  const transaction = await sequelize.transaction({
    isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
  });

  try {
    // Find the account-user relationship
    const accountUser = await AccountUser.findOne({
      where: { accountId, userId },
      transaction
    });

    if (!accountUser) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User is not a member of this account'
        }
      };
    }

    // If setting as default, unset any existing default account
    if (isDefault === true) {
      await AccountUser.update(
        { isDefault: false },
        {
          where: { userId, isDefault: true, id: { [Sequelize.Op.ne]: accountUser.id } },
          transaction
        }
      );
    }

    // Update the account-user relationship
    const [updatedRows] = await AccountUser.update(
      updateData,
      {
        where: { accountId, userId },
        returning: true,
        transaction
      }
    );

    if (updatedRows === 0) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Account user not found or no changes made'
        }
      };
    }

    // Get the updated record
    const updatedAccountUser = await AccountUser.findOne({
      where: { accountId, userId },
      transaction
    });

    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Account user roles updated',
        data: updatedAccountUser
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error updating account user roles:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to update account user roles',
        error: error.message
      }
    };
  }
};

/**
 * Remove a user from an account
 * @param {string} accountId - The account ID
 * @param {string} userId - The user ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const removeUserFromAccount = async (accountId, userId) => {
  if (!accountId || !userId) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'Account ID and user ID are required'
      }
    };
  }

  const transaction = await sequelize.transaction();

  try {
    // Check if user is the owner
    const accountUser = await AccountUser.findOne({
      where: { accountId, userId },
      transaction
    });

    if (!accountUser) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User is not a member of this account'
        }
      };
    }

    // Don't allow removing the account owner if they're the only one
    if (accountUser.primaryRole === 'account_owner') {
      const ownerCount = await AccountUser.count({
        where: { 
          accountId, 
          primaryRole: 'account_owner' 
        },
        transaction
      });

      if (ownerCount <= 1) {
        await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Cannot remove the only account owner. Transfer ownership first.'
          }
        };
      }
    }

    // Delete the account-user relationship
    await AccountUser.destroy({
      where: { accountId, userId },
      transaction
    });

    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'User removed from account'
      }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error removing user ${userId} from account ${accountId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to remove user from account',
        error: error.message
      }
    };
  }
};

/**
 * Get all accounts for a user
 * @param {string} userId - The user ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getUserAccounts = async (userId) => {
  if (!userId) {
    return {
      status: 400,
      payload: {
        success: false,
        message: 'User ID is required'
      }
    };
  }

  try {
    const userAccounts = await AccountUser.findAll({
      where: { userId },
      include: [
        {
          model: Account,
          as: 'account'
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: userAccounts
      }
    };
  } catch (error) {
    logger.error(`Error getting accounts for user ${userId}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to get user accounts',
        error: error.message
      }
    };
  }
};

module.exports = {
  addUserToAccount,
  getAccountUsers,
  updateAccountUserRoles,
  removeUserFromAccount,
  getUserAccounts
};

const UserService = require('./userService');
const { generateTokens, verifyToken, generateResetToken, verifyResetToken, generateEmailVerificationToken, verifyEmailVerificationToken } = require('../utils/token');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');
const { sendTemplateEmail } = require('./emailService');
const config = require('../config');
const { User, sequelize } = require('../models');
const { Op } = require('sequelize');

/**
 * Register a new landlord
 * @param {Object} userData - User data including firstName, lastName, email, password
 * @returns {Promise<Object>} - Returns status and payload
 */
const register = async (userData) => {
  const { email } = userData;

  // Check if user already exists
  const existingUserResponse = await UserService.findByEmail(email);
  if (existingUserResponse.status === 200 && existingUserResponse.payload.data) {
    return {
      status: 409,
      payload: {
        success: false,
        message: 'Email already in use',
      },
    };
  }

  let user;
  let verificationToken;
  const transaction = await sequelize.transaction();
  
  try {
    // Create new landlord user with account and role within the transaction
    const userResponse = await UserService.registerLandlord(userData, { transaction });
    
    // Check if user creation was successful
    if (!userResponse.payload.success) {
      await transaction.rollback();
      return userResponse; // Return the error response
    }
    
    user = userResponse.payload.data;
    
    // Generate email verification token
    verificationToken = generateEmailVerificationToken({ userId: user.id });
    logger.info(`Generated verification token for user ${user.id}: ${verificationToken.substring(0, 10)}...`);
    
    // Update user with verification token
    const [updatedCount] = await User.update(
      { emailVerificationToken: verificationToken },
      { 
        where: { id: user.id },
        transaction
      }
    );
    
    if (updatedCount === 0) {
      throw new Error('Failed to update user with verification token');
    }
    
    logger.info(`Verification token saved for user ${user.id}`);
    
    // Commit the transaction
    await transaction.commit();
    
    // Send verification email outside of transaction
    try {
      const verificationUrl = `${config.clientUrl}/en/auth/verify-email?token=${verificationToken}`;
      await sendTemplateEmail({
        to: user.email,
        subject: `Verify Your ${config.appName || 'Account'} Email`,
        templateName: config.mailgun.templates.emailVerification,
        templateVars: {
          verificationUrl,
          appName: config.appName || 'GoodTenant',
          clientUrl: config.clientUrl,
          supportEmail: config.supportEmail || '<EMAIL>',
          user: {
            firstName: user.firstName || 'User',
            email: user.email
          },
          currentYear: new Date().getFullYear()
        }
      });
    } catch (emailError) {
      // Log email error but don't fail the registration
      logger.error('Failed to send verification email:', emailError);
    }
    
  } catch (error) {
    await transaction.rollback().catch(rollbackError => {
      logger.error('Error during transaction rollback:', rollbackError);
    });
    
    logger.error('Error in registration:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to complete registration. Please try again later.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }

  // Generate tokens for immediate login if needed
  const { accessToken, refreshToken } = generateTokens({
    id: user.id,
    email: user.email,
    accountId: user.accountId,
    roles: user.roles?.map(role => role.name) || [],
    isEmailVerified: false // Indicate that email is not yet verified
  });
  
  // Store refresh token in database
  await UserService.storeRefreshToken(user.id, refreshToken);

  // Prepare response data (exclude sensitive info)
  const userDataResponse = user.get({ plain: true });
  delete userDataResponse.password;

  return {
    status: 201,
    payload: {
      success: true,
      message: 'Registration successful',
      data: {
        user: userDataResponse,
        tokens: {
          accessToken,
          refreshToken
        }
      },
    },
  };
};

/**
 * User login
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @returns {Promise<Object>} - Returns status and payload
 */
const login = async (email, password) => {
  try {
    logger.debug('Login attempt started', { email: email ? 'provided' : 'missing' });
    
    // Validate input
    if (!email || !password) {
      logger.warn('Login failed: Missing email or password', { 
        emailProvided: !!email, 
        passwordProvided: !!password 
      });
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Email and password are required',
        },
      };
    }

    // Find user by email with password included
    logger.debug('Looking up user by email with password', { email });
    const userResponse = await UserService.findByEmail(email, { includePassword: true });
    
    // Check if user was found
    if (!userResponse?.payload?.success || !userResponse?.payload?.data) {
      logger.warn('Login failed: User not found', { 
        email,
        status: userResponse?.status,
        error: userResponse?.payload?.message
      });
      return {
        status: 401,
        payload: {
          success: false,
          message: 'Invalid email or password',
        },
      };
    }
    
    const user = userResponse.payload.data;
    
    // Verify password
    if (!user.password) {
      logger.error('Login error: User has no password set', { 
        userId: user.id, 
        email,
        userData: {
          id: user.id,
          email: user.email,
          roles: user.roles?.map(r => r.name) || []
        }
      });
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Authentication error: No password set for user',
        },
      };
    }

    const isPasswordValid = await bcrypt.compare(String(password), user.password);
    logger.debug('Password verification result:', { isPasswordValid });
    
    if (!isPasswordValid) {
      logger.warn('Login failed: Invalid password', { email });
      return {
        status: 401,
        payload: {
          success: false,
          message: 'Invalid email or password',
        },
      };
    }

    // Debug log to check user object structure and verification status
    logger.debug('User login verification check:', {
      userId: user.id,
      email: user.email,
      isEmailVerified: user.isEmailVerified,
      userDataKeys: Object.keys(user.dataValues || {}),
      hasIsEmailVerified: 'isEmailVerified' in (user.dataValues || {})
    });

    // Check if email is verified
    if (user.isEmailVerified !== true) {
      logger.warn('Login failed: Email not verified', { 
        userId: user.id, 
        email,
        isEmailVerified: user.isEmailVerified,
        userData: user.get ? user.get({ plain: true }) : user
      });
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Please verify your email address before logging in. Check your email for the verification link.',
          code: 'EMAIL_NOT_VERIFIED',
          requiresVerification: true
        },
      };
    }

    // Get detailed debug info about accounts structure
    logger.debug('User accounts debug info:', {
      accountsCount: user.accounts?.length || 0,
      accountsData: user.accounts?.map(acc => ({
        id: acc.id,
        status: acc.status,
        hasAccountUser: !!acc.accountUser,
        accountUserId: acc.accountUser?.id,
        accountUserAccountId: acc.accountUser?.accountId,
        isDefault: acc.accountUser?.isDefault,
        primaryRole: acc.accountUser?.primaryRole,
        additionalRoles: acc.accountUser?.additionalRoles
      }))
    });
    
    // Get default account and roles from AccountUser
    const defaultAccount = user.accounts?.find(acc => acc.accountUser?.isDefault) || 
      (user.accounts?.length > 0 ? user.accounts[0] : null);
    
    // IMPORTANT: Use the accountId from the junction table (accountUser.accountId),
    // NOT from the account object directly (defaultAccount.id) which might be in binary form
    const accountId = defaultAccount?.accountUser?.accountId || defaultAccount?.id || null;
    const primaryRole = defaultAccount?.accountUser?.primaryRole;
    const additionalRoles = defaultAccount?.accountUser?.additionalRoles || [];
    const roles = primaryRole ? [primaryRole, ...additionalRoles] : additionalRoles;
    
    // Add debug logging to help diagnose the issue
    logger.debug('Account information extracted:', {
      hasDefaultAccount: !!defaultAccount,
      accountId,
      accountIdType: typeof accountId,
      accountIdIsBuffer: Buffer.isBuffer(accountId),
      primaryRole,
      additionalRoles
    });
    
    // Generate tokens
    const { accessToken, refreshToken } = generateTokens({
      id: user.id,
      email: user.email,
      accountId,
      roles
    });

    // Store refresh token in database
    await UserService.storeRefreshToken(user.id, refreshToken);

    // Prepare response data (exclude sensitive info)
    const userData = user.get({ plain: true });
    delete userData.password;

    // Extract roles from AccountUser
    const userRoles = [];
    
    if (primaryRole) {
      userRoles.push({
        name: primaryRole,
        isPrimary: true
      });
    }
    
    if (additionalRoles && additionalRoles.length > 0) {
      additionalRoles.forEach(role => {
        userRoles.push({
          name: role,
          isPrimary: false
        });
      });
    }

    // Prepare account information with roles
    const accounts = userData.accounts?.map(account => ({
      id: account.id,
      status: account.status,
      plan: account.plan,
      isDefault: account.accountUser?.isDefault || false,
      roles: {
        primaryRole: account.accountUser?.primaryRole || null,
        additionalRoles: account.accountUser?.additionalRoles || []
      }
    })) || [];

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Login successful',
        data: {
          user: {
            ...userData,
            accounts, // Include properly formatted accounts with roles
            roles: userRoles  // Keep the flat roles list for backward compatibility
          },
          tokens: {
            accessToken,
            refreshToken
          }
        },
      },
    };
  } catch (error) {
    logger.error('Login error:', {
      message: error.message,
      stack: error.stack,
      email: email,
      error: error
    });
    return {
      status: 500,
      payload: {
        success: false,
        message: 'An error occurred during login',
        // Include error details in development
        ...(process.env.NODE_ENV === 'development' && { error: error.message })
      },
    };
  }
};

/**
 * Refresh access token
 * @param {string} refreshToken - The refresh token
 * @returns {Promise<Object>} - Returns status and payload with new tokens
 */
const refreshToken = async (refreshToken) => {
  try {
    // Verify the refresh token
    const decoded = verifyToken(refreshToken, true);
    if (!decoded) {
      return {
        status: 401,
        payload: {
          success: false,
          message: 'Invalid or expired refresh token',
        },
      };
    }

    // Check if the refresh token exists in the database
    const isValid = await UserService.verifyRefreshToken(decoded.id, refreshToken);
    if (!isValid) {
      return {
        status: 401,
        payload: {
          success: false,
          message: 'Invalid refresh token',
        },
      };
    }

    // Get user data
    const userResponse = await UserService.findById(decoded.id);
    const user = userResponse.payload.data;
    if (!user) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found',
        },
      };
    }

    // Get default account and roles from AccountUser
    const defaultAccountUser = user.accounts?.find(acc => acc.accountUser?.isDefault) || 
      (user.accounts?.length > 0 ? user.accounts[0] : null);
    
    const accountId = defaultAccountUser?.id;
    const primaryRole = defaultAccountUser?.accountUser?.primaryRole;
    const additionalRoles = defaultAccountUser?.accountUser?.additionalRoles || [];
    const userRoles = primaryRole ? [primaryRole, ...additionalRoles] : additionalRoles;
    
    // Generate new tokens
    const tokens = generateTokens({
      id: user.id,
      email: user.email,
      accountId,
      roles: userRoles
    });

    // Update refresh token in database (token rotation)
    await UserService.updateRefreshToken(decoded.id, refreshToken, tokens.refreshToken);

    return {
      status: 200,
      payload: {
        success: true,
        data: {
          tokens
        },
      },
    };
  } catch (error) {
    logger.error('Refresh token error:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to refresh token',
      },
    };
  }
};

/**
 * Request password reset
 * @param {string} email - User's email
 * @returns {Promise<Object>} - Returns status and payload
 */
const requestPasswordReset = async (email) => {
  try {
    if (!email) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Email is required',
        },
      };
    }

    // Check if user exists
    const userResponse = await UserService.findByEmail(email);
    if (!userResponse?.payload?.success || !userResponse?.payload?.data) {
      // For security, don't reveal if email exists or not
      return {
        status: 200,
        payload: {
          success: true,
          message: 'If your email is registered, you will receive a password reset link',
        },
      };
    }

    const user = userResponse.payload.data;
    
    // Generate reset token (expires in 1 hour)
    const resetToken = generateResetToken({ userId: user.id });
    
    // Store reset token in database using the local function
    const storeResult = await storeResetToken(user.id, resetToken);
    if (!storeResult.payload.success) {
      logger.error('Failed to store reset token:', storeResult.payload.message);
      return storeResult;
    }
    
    // Send password reset email using template
    const resetUrl = `${config.clientUrl}/en/auth/reset-password?token=${resetToken}`;
    const expiryTime = '1 hour';
    const templateName = config.mailgun.templates?.passwordReset || 'password-reset';
    
    try {
      await sendTemplateEmail({
        to: user.email,
        subject: `Reset Your ${config.appName || 'Account'} Password`,
        templateName: templateName,
        templateVars: {
          resetUrl,
          expiryTime,
          appName: config.appName || 'GoodTenant',
          supportEmail: config.supportEmail || '<EMAIL>',
          user: {
            firstName: user.firstName || 'User',
            email: user.email
          }
        }
      });
    } catch (emailError) {
      logger.error('Error sending password reset email:', {
        error: emailError.message,
        stack: emailError.stack,
        templateName,
        email: user.email
      });
      
      // Continue execution even if email fails
      logger.warn('Password reset email failed to send, but reset token was still generated');
    }

    return {
      status: 200,
      payload: {
        success: true,
        message: 'If your email is registered, you will receive a password reset link',
      },
    };
  } catch (error) {
    logger.error('Error in requestPasswordReset:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to process password reset request',
      },
    };
  }
};

/**
 * Reset password
 * @param {string} token - Password reset token
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} - Returns status and payload
 */
const resetPassword = async (token, newPassword) => {
  try {
    if (!token || !newPassword) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Token and new password are required',
        },
      };
    }

    // First verify the reset token is valid in the database
    const tokenResponse = await getUserByResetToken(token);
    if (!tokenResponse?.payload?.success || !tokenResponse?.payload?.data) {
      logger.warn(`Invalid or expired reset token: ${token}`);
      return {
        status: tokenResponse?.status || 400,
        payload: {
          success: false,
          message: tokenResponse?.payload?.message || 'Invalid or expired token',
          code: tokenResponse?.payload?.code
        },
      };
    }
    
    // Then verify the JWT token is valid
    const decoded = verifyResetToken(token);
    if (!decoded) {
      logger.warn(`JWT verification failed for token: ${token}`);
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Invalid or expired token',
        },
      };
    }

    const user = tokenResponse.payload.data;
    
    try {
      // Update password using the User model directly to ensure proper hashing
      const userToUpdate = await User.scope('withPassword').findByPk(user.id);
      if (!userToUpdate) {
        return {
          status: 404,
          payload: { success: false, message: 'User not found' }
        };
      }
      
      // Hash the new password and update the user
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      await userToUpdate.update({ 
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null
      });
      
      logger.info(`Password successfully updated for user ${user.id}`);

      // Send confirmation email using template
      const userResponse = await UserService.findById(user.id);
      if (userResponse?.payload?.success && userResponse?.payload?.data) {
        const user = userResponse.payload.data;
        await sendTemplateEmail({
          to: user.email,
          subject: 'Password Changed Successfully',
          templateName: 'password-changed',
          templateVars: {
            appName: config.appName || 'GoodTenant',
            supportEmail: config.supportEmail || '<EMAIL>',
            user: {
              firstName: user.firstName || 'User',
              email: user.email
            },
            changeDate: new Date().toLocaleString(),
            ipAddress: 'N/A' // You might want to pass this from the request
          }
        });
      }
      
      return {
        status: 200,
        payload: {
          success: true,
          message: 'Password reset successful',
        },
      };
    } catch (error) {
      logger.error('Error updating password:', error);
      return {
        status: 500,
        payload: {
          success: false,
          message: 'Failed to update password',
          error: error.message
        },
      };
    }
  } catch (error) {
    logger.error('Error in resetPassword:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to reset password',
        error: error.message
      },
    };
  }
};

/**
 * Verify email
 * @param {string} token - Email verification token
 * @returns {Promise<Object>} - Returns status and payload
 */
const verifyEmail = async (token) => {
  try {
    // Verify the email verification token
    const decoded = verifyEmailVerificationToken(token);
    if (!decoded || !decoded.userId) {
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Invalid or expired verification link',
          code: 'INVALID_OR_EXPIRED_TOKEN'
        },
      };
    }

    // Find the user by ID from the token, including the verification token
    const user = await User.findOne({
      where: { id: decoded.userId },
      attributes: ['id', 'email', 'firstName', 'isEmailVerified', 'emailVerificationToken'],
      raw: true
    });

    logger.info(`Verifying email for user ${user?.id}, token exists: ${!!user?.emailVerificationToken}`);

    if (!user) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'User not found',
          code: 'USER_NOT_FOUND'
        }
      };
    }

    // Check if email is already verified
    if (user.isEmailVerified) {
      return {
        status: 200,
        payload: {
          success: true,
          message: 'Email already verified',
          code: 'ALREADY_VERIFIED'
        },
      };
    }

    // Verify the stored token matches
    if (user.emailVerificationToken !== token) {
      logger.warn(`Token mismatch for user ${user.id}. Stored: ${user.emailVerificationToken?.substring(0, 10)}..., Provided: ${token?.substring(0, 10)}...`);
      return {
        status: 400,
        payload: {
          success: false,
          message: 'Invalid verification token',
          code: 'INVALID_TOKEN'
        },
      };
    }

    // Update user's email verification status and clear the token
    await User.update(
      { 
        isEmailVerified: true,
        emailVerificationToken: null
      },
      { where: { id: user.id } }
    );

    // // Send welcome email now that email is verified
    // await sendTemplateEmail({
    //   to: user.email,
    //   subject: `Welcome to ${config.appName || 'Our Service'}!`,
    //   templateName: config.mailgun.templates.welcomeEmail,
    //   templateVars: {
    //     appName: config.appName || 'GoodTenant',
    //     supportEmail: config.supportEmail || '<EMAIL>',
    //     user: {
    //       firstName: user.firstName || 'User',
    //       email: user.email
    //     },
    //     loginUrl: `${config.clientUrl}/login`
    //   }
    // });

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Email verified successfully. Welcome to our service!',
        code: 'VERIFICATION_SUCCESS'
      },
    };
  } catch (error) {
    logger.error('Error in verifyEmail:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to verify email',
      },
    };
  }
};

/**
 * Store a password reset token for a user
 * @param {string} userId - User ID
 * @param {string} token - Reset token
 * @param {number} [expiresInHours=1] - Token expiration time in hours
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const storeResetToken = async (userId, token, expiresInHours = 1) => {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info(`Storing reset token for user ${userId}`);
    
    // Calculate expiration time (default 1 hour from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);
    
    logger.info(`Token will expire at: ${expiresAt}`);
    
    // First, let's check if the user exists
    const user = await User.scope('withResetToken').findByPk(userId);
    if (!user) {
      logger.warn(`User not found with ID: ${userId}`);
      await transaction.rollback();
      return {
        status: 404,
        payload: { success: false, message: 'User not found' }
      };
    }
    
    logger.info(`Found user: ${user.email}, current reset token: ${user.passwordResetToken}`);
    
    // Update user with reset token and expiration
    const [updated] = await User.update(
      {
        passwordResetToken: token,
        passwordResetExpires: expiresAt
      },
      {
        where: { id: userId },
        transaction
      }
    );
    
    if (updated !== 1) {
      logger.error(`Failed to update user ${userId} with reset token. Rows updated: ${updated}`);
      await transaction.rollback();
      return {
        status: 500,
        payload: { success: false, message: 'Failed to store reset token' }
      };
    }
    
    // Verify the token was actually saved
    const updatedUser = await User.scope('withResetToken').findByPk(userId, { transaction });
    if (!updatedUser || updatedUser.passwordResetToken !== token) {
      logger.error(`Token verification failed. Expected: ${token}, Got: ${updatedUser ? updatedUser.passwordResetToken : 'no user found'}`);
      await transaction.rollback();
      return {
        status: 500,
        payload: { success: false, message: 'Failed to verify token storage' }
      };
    }
    
    await transaction.commit();
    logger.info(`Successfully stored reset token for user ${userId}`);
    return {
      status: 200,
      payload: { 
        success: true, 
        message: 'Reset token stored successfully',
        expiresAt
      }
    };
  } catch (error) {
    logger.error('Error storing reset token:', error);
    await transaction.rollback();
    return {
      status: 500,
      payload: { 
        success: false, 
        message: 'Failed to store reset token',
        error: error.message
      }
    };
  }
};

/**
 * Get user by reset token
 * @param {string} token - Reset token
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload with user data if token is valid
 */
const getUserByResetToken = async (token) => {
  try {
    logger.info(`Looking up user by reset token: ${token}`);
    
    // First try with the scope to include the reset token fields
    const user = await User.scope('withResetToken').findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: { [Op.gt]: new Date() }
      }
    });
    
    if (!user) {
      // Try to find out why the token wasn't found
      const expiredUser = await User.scope('withResetToken').findOne({
        where: {
          passwordResetToken: token,
          passwordResetExpires: { [Op.lte]: new Date() }
        }
      });
      
      if (expiredUser) {
        logger.warn(`Found expired reset token for user ${expiredUser.email}, expired at ${expiredUser.passwordResetExpires}`);
        return {
          status: 400,
          payload: { 
            success: false, 
            message: 'Reset token has expired',
            code: 'TOKEN_EXPIRED'
          }
        };
      }
      
      logger.warn(`No user found with reset token: ${token}`);
      return {
        status: 400,
        payload: { 
          success: false, 
          message: 'Invalid reset token',
          code: 'INVALID_TOKEN'
        }
      };
    }
    
    logger.info(`Found valid reset token for user ${user.email}`);
    
    // Only return the necessary user data
    const userData = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName
    };
    
    return {
      status: 200,
      payload: { 
        success: true, 
        data: userData,
        expiresAt: user.passwordResetExpires
      }
    };
  } catch (error) {
    logger.error('Error finding user by reset token:', error);
    return {
      status: 500,
      payload: { 
        success: false, 
        message: 'Failed to validate reset token',
        error: error.message,
        code: 'VALIDATION_ERROR'
      }
    };
  }
};

/**
 * Clear reset token for a user
 * @param {string} userId - User ID
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const clearResetToken = async (userId) => {
  const transaction = await sequelize.transaction();
  
  try {
    await User.update(
      {
        passwordResetToken: null,
        passwordResetExpires: null
      },
      {
        where: { id: userId },
        transaction
      }
    );
    
    await transaction.commit();
    return {
      status: 200,
      payload: { success: true, message: 'Reset token cleared' }
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error clearing reset token:', error);
    return {
      status: 500,
      payload: { 
        success: false, 
        message: 'Failed to clear reset token',
        error: error.message
      }
    };
  }
};

module.exports = {
  register,
  login,
  refreshToken,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  storeResetToken,
  getUserByResetToken,
  clearResetToken
};

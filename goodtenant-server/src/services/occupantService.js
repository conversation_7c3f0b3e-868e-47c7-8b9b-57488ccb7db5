const { Occupant, Lease, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Create a new occupant
 * @param {Object} occupantData - Occupant data
 * @param {string} userId - ID of the user creating the occupant (must be associated with the lease)
 * @param {Object} options - Options for the create operation
 * @param {Transaction} options.transaction - Optional transaction to use
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const createOccupant = async (occupantData, userId, options = {}) => {
  let transaction;
  
  try {
    // Only create a transaction if one wasn't provided
    if (options.transaction === undefined) {
      transaction = await sequelize.transaction();
    } else if (options.transaction) {
      transaction = options.transaction;
    }
    
    // Validate required fields
    if (!occupantData.firstName || !occupantData.lastName || !occupantData.relationship || !occupantData.leaseId) {
      if (transaction && options.transaction === undefined) await transaction.rollback();
      return {
        status: 400,
        payload: {
          success: false,
          message: 'First name, last name, relationship, and lease ID are required'
        }
      };
    }

    // Validate age if provided
    if (occupantData.age !== undefined) {
      const age = parseInt(occupantData.age, 10);
      if (isNaN(age) || age < 0 || age > 120) {
        if (transaction && options.transaction === undefined) await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Age must be a number between 0 and 120'
          }
        };
      }
    }

    // Check if lease exists and user has access to it
    const lease = await Lease.findOne({
      where: { 
        id: occupantData.leaseId,
        [Op.or]: [
          { landlordId: userId },
          { '$tenants.id$': userId }
        ]
      },
      include: [
        {
          model: User,
          as: 'tenants',
          attributes: ['id'],
          through: { attributes: [] }
        }
      ],
      transaction
    });

    if (!lease) {
      if (transaction && options.transaction === undefined) await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found or access denied'
        }
      };
    }

    // Create the occupant
    const occupant = await Occupant.create({
      firstName: occupantData.firstName,
      lastName: occupantData.lastName,
      age: occupantData.age ? parseInt(occupantData.age, 10) : null,
      relationship: occupantData.relationship,
      notes: occupantData.notes || null,
      leaseId: occupantData.leaseId,
      isActive: true
    }, { transaction });

    if (transaction && options.transaction === undefined) {
      await transaction.commit();
    }

    return {
      status: 201,
      payload: {
        success: true,
        data: occupant
      }
    };
  } catch (error) {
    if (transaction && options.transaction === undefined) {
      await transaction.rollback();
    }
    
    logger.error('Error creating occupant:', error);
    
    // Handle specific error types
    let errorMessage = 'Failed to create occupant';
    if (error.name === 'SequelizeValidationError') {
      errorMessage = error.errors.map(e => e.message).join(', ');
    } else if (error.name === 'SequelizeForeignKeyConstraintError') {
      errorMessage = 'Invalid lease ID';
    }
    
    return {
      status: 500,
      payload: {
        success: false,
        message: errorMessage,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Get all occupants for a lease with optional filtering and pagination
 * @param {string} leaseId - ID of the lease
 * @param {string} userId - ID of the user making the request (must be landlord or tenant on lease)
 * @param {Object} options - Query options (page, limit, search, isActive)
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getOccupantsByLease = async (leaseId, userId, options = {}) => {
  try {
    const { page = 1, limit = 10, search = '', isActive } = options;
    const offset = (page - 1) * limit;

    // Verify user has access to this lease
    const lease = await Lease.findOne({
      where: { 
        id: leaseId,
        [Op.or]: [
          { landlordId: userId },
          { '$tenants.id$': userId }
        ]
      },
      include: [
        {
          model: User,
          as: 'tenants',
          attributes: ['id'],
          through: { attributes: [] }
        }
      ]
    });

    if (!lease) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Lease not found or access denied'
        }
      };
    }

    // Build where clause
    const whereClause = { leaseId };
    
    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.like]: `%${search}%` } },
        { lastName: { [Op.like]: `%${search}%` } },
        { notes: { [Op.like]: `%${search}%` } }
      ];
    }

    if (isActive !== undefined) {
      whereClause.isActive = isActive === 'true';
    }

    const { count, rows } = await Occupant.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Lease,
          as: 'lease',
          attributes: ['id', 'startDate', 'endDate']
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          totalPages: Math.ceil(count / limit)
        }
      }
    };
  } catch (error) {
    logger.error('Error fetching occupants:', error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch occupants',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Get occupant by ID
 * @param {string} id - Occupant ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const getOccupantById = async (id, userId) => {
  try {
    const occupant = await Occupant.findOne({
      where: { id },
      include: [
        {
          model: Lease,
          as: 'lease',
          attributes: ['id', 'startDate', 'endDate'],
          include: [
            {
              model: User,
              as: 'landlord',
              attributes: ['id', 'firstName', 'lastName']
            },
            {
              model: User,
              as: 'tenants',
              attributes: ['id', 'firstName', 'lastName'],
              through: { attributes: [] }
            }
          ]
        }
      ]
    });

    if (!occupant) {
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Occupant not found'
        }
      };
    }

    // Check if user has access to this occupant's lease
    const hasAccess = await Lease.findOne({
      where: {
        id: occupant.leaseId,
        [Op.or]: [
          { landlordId: userId },
          { '$tenants.id$': userId }
        ]
      },
      include: [
        {
          model: User,
          as: 'tenants',
          attributes: ['id'],
          through: { attributes: [] }
        }
      ]
    });

    if (!hasAccess) {
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Access denied to this occupant record'
        }
      };
    }


    return {
      status: 200,
      payload: {
        success: true,
        data: occupant
      }
    };
  } catch (error) {
    logger.error(`Error fetching occupant with ID ${id}:`, error);
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to fetch occupant',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Update occupant
 * @param {string} id - Occupant ID
 * @param {Object} updateData - Data to update
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const updateOccupant = async (id, updateData, userId) => {
  let transaction;
  
  try {
    transaction = await sequelize.transaction();
    
    // Find the occupant
    const occupant = await Occupant.findByPk(id, { transaction });
    
    if (!occupant) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Occupant not found'
        }
      };
    }

    // Check if user has access to this occupant's lease
    const hasAccess = await Lease.findOne({
      where: {
        id: occupant.leaseId,
        [Op.or]: [
          { landlordId: userId },
          { '$tenants.id$': userId }
        ]
      },
      include: [
        {
          model: User,
          as: 'tenants',
          attributes: ['id'],
          through: { attributes: [] }
        }
      ],
      transaction
    });

    if (!hasAccess) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Access denied to update this occupant record'
        }
      };
    }

    // Validate age if provided
    if (updateData.age !== undefined) {
      const age = parseInt(updateData.age, 10);
      if (isNaN(age) || age < 0 || age > 120) {
        await transaction.rollback();
        return {
          status: 400,
          payload: {
            success: false,
            message: 'Age must be a number between 0 and 120'
          }
        };
      }
      updateData.age = age;
    }

    // Update the occupant
    await occupant.update(updateData, { transaction });
    await transaction.commit();

    // Get the updated occupant with relationships
    const updatedOccupant = await Occupant.findByPk(id, {
      include: [
        {
          model: Lease,
          as: 'lease',
          attributes: ['id', 'startDate', 'endDate']
        }
      ]
    });

    return {
      status: 200,
      payload: {
        success: true,
        data: updatedOccupant
      }
    };
  } catch (error) {
    if (transaction) await transaction.rollback();
    
    logger.error(`Error updating occupant with ID ${id}:`, error);
    
    let errorMessage = 'Failed to update occupant';
    if (error.name === 'SequelizeValidationError') {
      errorMessage = error.errors.map(e => e.message).join(', ');
    } else if (error.name === 'SequelizeForeignKeyConstraintError') {
      errorMessage = 'Invalid lease ID';
    }
    
    return {
      status: 500,
      payload: {
        success: false,
        message: errorMessage,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

/**
 * Delete occupant
 * @param {string} id - Occupant ID
 * @param {string} userId - ID of the user making the request
 * @returns {Promise<{status: number, payload: Object}>} - Status and response payload
 */
const deleteOccupant = async (id, userId) => {
  let transaction;
  
  try {
    transaction = await sequelize.transaction();
    
    // Find the occupant
    const occupant = await Occupant.findByPk(id, { transaction });
    
    if (!occupant) {
      await transaction.rollback();
      return {
        status: 404,
        payload: {
          success: false,
          message: 'Occupant not found'
        }
      };
    }

    // Check if user has access to this occupant's lease
    const hasAccess = await Lease.findOne({
      where: {
        id: occupant.leaseId,
        [Op.or]: [
          { landlordId: userId },
          { '$tenants.id$': userId }
        ]
      },
      include: [
        {
          model: User,
          as: 'tenants',
          attributes: ['id'],
          through: { attributes: [] }
        }
      ],
      transaction
    });

    if (!hasAccess) {
      await transaction.rollback();
      return {
        status: 403,
        payload: {
          success: false,
          message: 'Access denied to delete this occupant record'
        }
      };
    }

    // Soft delete the occupant
    await occupant.destroy({ transaction });
    await transaction.commit();

    return {
      status: 200,
      payload: {
        success: true,
        message: 'Occupant deleted successfully'
      }
    };
  } catch (error) {
    if (transaction) await transaction.rollback();
    
    logger.error(`Error deleting occupant with ID ${id}:`, error);
    
    return {
      status: 500,
      payload: {
        success: false,
        message: 'Failed to delete occupant',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }
    };
  }
};

module.exports = {
  createOccupant,
  getOccupantsByLease,
  getOccupantById,
  updateOccupant,
  deleteOccupant
};

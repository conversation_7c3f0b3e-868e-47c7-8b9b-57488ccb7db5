const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const logger = require('./utils/logger');
const { notFoundHandler, errorHandler } = require('./middlewares/error');
const setupSwagger = require('./config/swagger');
const { authenticate } = require('./middlewares/auth');
const { handleUploadErrors } = require('./middlewares/upload');

// Import routes
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const propertyRoutes = require('./routes/propertyRoutes');
const fileRoutes = require('./routes/fileRoutes');
const hoaRoutes = require('./routes/hoaRoutes');
const loanRoutes = require('./routes/loanRoutes');
const insuranceRoutes = require('./routes/insuranceRoutes');
const taxRoutes = require('./routes/taxRoutes');
const maintenanceRoutes = require('./routes/maintenanceRoutes');
const utilityRoutes = require('./routes/utilityRoutes');
const contactRoutes = require('./routes/contactRoutes');
const vehicleRoutes = require('./routes/vehicleRoutes');
const petRoutes = require('./routes/petRoutes');
const leaseRoutes = require('./routes/leaseRoutes');
const tenantRoutes = require('./routes/tenantRoutes');
const tenantPublicRoutes = require('./routes/public/tenantPublicRoutes');
const templateRoutes = require('./routes/templateRoutes');
const occupantRoutes = require('./routes/occupantRoutes');
const accountStatsRoutes = require('./routes/accountStatsRoutes');
const taskRoutes = require('./routes/taskRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const tenantPaymentRoutes = require('./routes/tenantPaymentRoutes');
const signatureRoutes = require('./routes/signatureRoutes');
const stripeRoutes = require('./routes/stripeRoutes');
//const webhookRoutes = require('./routes/webhookRoutes');
// Import other routes as needed
// const apiRoutes = require('./routes/api');
require('./events');
require('./jobs');
// Initialize express app
const app = express();

// Security middleware - must come first
app.use(helmet());

// Create and mount webhook routes before any authentication or body parsing
const webhookRouter = express.Router();
require('./routes/webhookRoutes')(webhookRouter);
app.use(webhookRouter);  // Mount without prefix to use the full path defined in webhookRoutes

// Log all incoming requests (for debugging)
app.use((req, res, next) => {
  logger.info(`Incoming request: ${req.method} ${req.path}`);
  next();
});

// Enable CORS with environment variables
const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim())
  : [];

app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // Check if the origin is in the allowed list
    if (allowedOrigins.indexOf(origin) === -1) {
      const msg = `The CORS policy for this site does not allow access from the specified origin: ${origin}`;
      return callback(new Error(msg), false);
    }
    return callback(null, true);
  },
  credentials: true, // Enable credentials if you need to send cookies
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Parse JSON bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging
app.use(morgan('dev'));

// Simple request logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`);
  next();
});

// Setup Swagger documentation
setupSwagger(app);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'UP' });
});

// Public routes (no authentication required)
app.use('/api/auth', authRoutes);

// Mount public tenant routes before authentication
app.use('/api/public', tenantPublicRoutes);

// Apply authentication middleware to all other API routes
app.use('/api', authenticate);

// Protected API routes
app.use('/api/users', userRoutes);
app.use('/api/properties', propertyRoutes);
app.use('/api/hoas', hoaRoutes);
app.use('/api/loans', loanRoutes);
app.use('/api/insurances', insuranceRoutes);
app.use('/api/taxes', taxRoutes);
app.use('/api/maintenance', maintenanceRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/utilities', utilityRoutes);
app.use('/api/contacts', contactRoutes);
app.use('/api/vehicles', vehicleRoutes);
app.use('/api/pets', petRoutes);
app.use('/api/leases', leaseRoutes);
app.use('/api/templates', templateRoutes);
app.use('/api/occupants', occupantRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/tenants', tenantRoutes);
app.use('/api/account', accountStatsRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/tenant-payment', tenantPaymentRoutes);
app.use('/api/signature', signatureRoutes);
app.use('/api/stripe', stripeRoutes);
// app.use('/api', apiRoutes);

// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/build')));
  
  // Handle React routing, return all requests to React app
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/build', 'index.html'));
  });
}

// Error handling middleware - must be last
app.use(notFoundHandler);
app.use(errorHandler);

module.exports = app;

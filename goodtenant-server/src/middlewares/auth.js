const { verifyToken } = require('../utils/token');
const { User, Account } = require('../models');
const { AuthenticationError, AuthorizationError } = require('../utils/errors');
const tokenBlacklist = require('../services/tokenBlacklist');

/**
 * Middleware to authenticate JWT token
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('No token provided');
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      throw new AuthenticationError('No token provided');
    }

    // Check if token is blacklisted
    const isBlacklisted = await tokenBlacklist.isBlacklisted(token);
    if (isBlacklisted) {
      throw new AuthenticationError('Token has been revoked');
    }

    // Verify access token (not a refresh token)
    const decoded = verifyToken(token);
    if (!decoded) {
      throw new AuthenticationError('Invalid token');
    }

    // Check if this is a refresh token (shouldn't be used as access token)
    if (decoded.isRefreshToken) {
      throw new AuthenticationError('Invalid token type');
    }

    // Get user from the token with account info
    const user = await User.findByPk(decoded.id, {
      include: [
        { 
          model: Account,
          as: 'accounts', // Using plural 'accounts' to match the association alias
          required: false, // Make account optional
          attributes: ['id', 'status', 'plan'], // Only select needed fields
          through: { attributes: [] } // Don't include join table attributes
        },
        // Roles are now stored in AccountUser and PropertyUser models
        // and will be loaded as needed by specific services
      ],
      attributes: { 
        exclude: ['password'],
      },
    });

    // Add the first account to req.user for backward compatibility
    if (user && user.accounts && user.accounts.length > 0) {
      user.account = user.accounts[0];
    }

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Structure the user object safely
    req.user = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      // Include account ID from either the token or user object
      accountId: decoded.accountId || user.account?.id || user.accountId,
      // Map roles if they exist
      roles: user.roles?.map(role => role.name) || []
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check user roles
 * @param {...string} allowedRoles - Allowed roles
 */
const authorize = (...allowedRoles) => {
  return async (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    try {
      console.log('Authorizing user with ID:', req.user.id);
      
      // Flatten the allowedRoles array in case it's nested
      const flattenedAllowedRoles = allowedRoles.flat(Infinity);
      console.log('Allowed roles:', flattenedAllowedRoles);
      
      const { AccountUser } = require('../models');
      
      // Get account user with roles
      const accountUser = await AccountUser.findOne({
        where: { userId: req.user.id },
        raw: true
      });
      
      if (!accountUser) {
        console.log('No account user found for user ID:', req.user.id);
        return next(new AuthorizationError('No account access found'));
      }
      
      console.log('Account user from DB:', JSON.stringify(accountUser, null, 2));

      // Get all roles from account user
      const userRoles = [
        accountUser.primaryRole,
        ...(accountUser.additionalRoles || [])
      ].filter(Boolean);

      console.log('All user roles:', userRoles);
      console.log('Checking against allowed roles:', flattenedAllowedRoles);

      // Check if user has any of the allowed roles
      const hasRole = userRoles.some(role => 
        flattenedAllowedRoles.includes(role)
      );

      if (!hasRole) {
        console.log('User does not have any of the required roles');
        return next(new AuthorizationError('Insufficient permissions'));
      }

      next();
    } catch (error) {
      console.error('Error in authorize middleware:', error);
      return next(new AuthorizationError('Error checking permissions'));
    }
  };
};

/**
 * Middleware to check if user is the owner of the resource
 * @param {string} paramName - Name of the URL parameter containing the resource ID
 * @param {string} modelName - Name of the model to check ownership against
 * @param {string} [idField='id'] - Field name of the ID in the model
 */
const isOwner = (paramName, modelName, idField = 'id') => {
  return async (req, res, next) => {
    try {
      const Model = require(`../models/${modelName}`);
      const resourceId = req.params[paramName];
      const userId = req.user?.id;

      if (!userId) {
        return next(new AuthenticationError('Authentication required'));
      }

      const resource = await Model.findOne({
        where: { [idField]: resourceId },
      });

      if (!resource) {
        return next(new Error('Resource not found'));
      }

      // Check if the resource has a userId field that matches the current user
      if (resource.userId !== userId) {
        return next(new AuthorizationError('Not authorized to access this resource'));
      }

      // Attach the resource to the request for use in the route handler
      req[modelName.toLowerCase()] = resource;
      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  authenticate,
  authorize,
  isOwner,
};

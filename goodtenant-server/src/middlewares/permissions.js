const ApiError = require('../utils/errors');
const logger = require('../utils/logger');

/**
 * Middleware to check if user has required permissions
 * @param {string} resource - The resource being accessed (e.g., 'users', 'properties')
 * @param {string} action - The action being performed (e.g., 'create', 'read', 'update', 'delete')
 * @returns {Function} Express middleware function
 */
const checkPermission = (resource, action) => {
  return async (req, res, next) => {
    try {
      // System admins have all permissions
      if (req.user.isAdmin) {
        return next();
      }

      // Get user's roles with permissions
      const user = await req.user.getRoles({
        attributes: ['id', 'permissions'],
        joinTableAttributes: [],
      });

      // Check if any role has the required permission
      const hasPermission = user.some(role => {
        const rolePermissions = role.permissions || {};
        
        // Check for wildcard permission
        if (rolePermissions['*']?.includes('*')) {
          return true;
        }

        // Check for resource wildcard
        if (rolePermissions['*']?.includes(action)) {
          return true;
        }

        // Check specific resource permission
        return rolePermissions[resource]?.includes('*') || 
               rolePermissions[resource]?.includes(action);
      });

      if (!hasPermission) {
        logger.warn(
          `Permission denied: User ${req.user.id} attempted to ${action} ${resource}`
        );
        return next(
          new ApiError(403, 'You do not have permission to perform this action')
        );
      }

      next();
    } catch (error) {
      logger.error('Permission check failed:', error);
      next(new ApiError(500, 'Failed to verify permissions'));
    }
  };
};

module.exports = {
  checkPermission,
};

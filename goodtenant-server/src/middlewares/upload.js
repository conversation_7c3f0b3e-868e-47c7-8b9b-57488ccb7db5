const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

// Configure multer for file uploads
const storage = multer.memoryStorage();

// File filter to allow only specific file types
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, documents, and archives are allowed.'), false);
  }
};

// Configure multer with the storage and file filter
const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter,
});

// Middleware to handle file upload errors
const handleUploadErrors = (err, req, res, next) => {
  // Only handle errors if they are related to file uploads
  if (err instanceof multer.MulterError || 
      (err && (err.code === 'LIMIT_FILE_SIZE' || err.code === 'LIMIT_FILE_COUNT'))) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum file size is 20MB.',
      });
    }
    return res.status(400).json({
      success: false,
      message: 'Error uploading file',
      error: err.message,
    });
  }
  
  // If it's not a file upload error, pass it to the next error handler
  next(err);
};

// Generate a unique filename while preserving the extension
const generateUniqueFileName = (originalname) => {
  const ext = path.extname(originalname);
  const baseName = path.basename(originalname, ext);
  return `${baseName}-${uuidv4()}${ext}`;
};

module.exports = {
  upload,
  handleUploadErrors,
  generateUniqueFileName,
};

// Add this middleware to your routes after the upload middleware
// Example:
// router.post('/upload', upload.single('file'), handleUploadErrors, controller.uploadFile);

version: '3.8'

services:
  # Development environment
  app-development:
    build:
      context: .
      target: development
    container_name: goodtenant-dev
    restart: unless-stopped
    env_file: .env.development
    environment:
      - NODE_ENV=development
      - DB_HOST=db-development
    ports:
      - "${APP_PORT:-4000}:4000"
    depends_on:
      db-development:
        condition: service_healthy
    networks:
      - goodtenant-dev-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles: ["development"]

  # Development Database
  db-development:
    image: mysql:8.0
    container_name: goodtenant-db-dev
    restart: unless-stopped
    environment:
      # Root user configuration
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_ALLOW_EMPTY_PASSWORD: "no"
      # Application database and user
      MYSQL_DATABASE: goodtenant_dev
      MYSQL_USER: devuser
      MYSQL_PASSWORD: devpass
    ports:
      - "3307:3306"  # Different port than production to avoid conflicts
    volumes:
      - mysql-dev-data:/var/lib/mysql
    networks:
      - goodtenant-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-prootpassword"]
      timeout: 5s
      retries: 10
    profiles: ["development"]

  # Development Adminer
  adminer-development:
    image: adminer
    container_name: goodtenant-adminer-dev
    restart: always
    ports:
      - "8081:8080"  # Different port than production
    environment:
      ADMINER_DEFAULT_SERVER: db-development
    depends_on:
      - db-development
    networks:
      - goodtenant-dev-network
    profiles: ["development"]

  # Production environment
  app-production:
    build:
      context: .
      target: production
    container_name: goodtenant-prod
    restart: unless-stopped
    env_file: .env.production
    environment:
      - NODE_ENV=production
      - DB_HOST=db-production
    ports:
      - "4000:4000"
    depends_on:
      db-production:
        condition: service_healthy
    networks:
      - goodtenant-prod-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles: ["production"]

  # Production Database
  db-production:
    image: mysql:8.0
    container_name: goodtenant-db-prod
    restart: unless-stopped
    environment:
      # Root user configuration
      MYSQL_ROOT_PASSWORD: prodrootpassword
      MYSQL_ALLOW_EMPTY_PASSWORD: "no"
      # Application database and user
      MYSQL_DATABASE: goodtenant_prod
      MYSQL_USER: produser
      MYSQL_PASSWORD: prodpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql-prod-data:/var/lib/mysql
    networks:
      - goodtenant-prod-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-pprodrootpassword"]
      timeout: 5s
      retries: 10
    profiles: ["production"]

  # Production Adminer
  adminer-production:
    image: adminer
    container_name: goodtenant-adminer-prod
    restart: always
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db-production
    depends_on:
      - db-production
    networks:
      - goodtenant-prod-network
    profiles: ["production"]

networks:
  goodtenant-dev-network:
    driver: bridge
  goodtenant-prod-network:
    driver: bridge

volumes:
  mysql-dev-data:
    name: goodtenant-mysql-dev-data
  mysql-prod-data:
    name: goodtenant-mysql-prod-data

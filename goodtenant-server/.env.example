# Database Configuration
DB_NAME=goodtenant_prod
DB_USER=produser
DB_PASSWORD=prodpassword
DB_HOST=localhost
DB_PORT=3306
DB_SYNC_ENABLED=false

# Application Environment
NODE_ENV=production
ALLOWED_ORIGINS=https://www.goodtenant.us

# JWT Configuration
JWT_SECRET=generate_a_secure_random_string_here
JWT_EXPIRES_IN=1d

# Server Configuration
PORT=4000

# Client Application URL
CLIENT_URL=http://localhost:3000  # Update this to your frontend URL

# Stripe
STRIPE_SECRET_KEY=your_production_stripe_secret_key
ACCESS_TOKEN_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# Cloudflare R2 Configuration (Production)
CF_ACCOUNT_ID=your_production_account_id
CF_ACCESS_KEY_ID=your_production_access_key
CF_SECRET_ACCESS_KEY=your_production_secret_key
CF_BUCKET_NAME=goodtenant-prod

# File Upload Settings
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Mailgun Email Configuration (Production)
MAILGUN_API_KEY=your_production_mailgun_key
MAILGUN_DOMAIN=your-production-domain.com
MAILGUN_FROM_EMAIL=<EMAIL>
MAILGUN_TENANT_INVITATION_TEMPLATE=tenant-invitation-prod
MAILGUN_TENANT_WELCOME_TEMPLATE=tenant-welcome-prod
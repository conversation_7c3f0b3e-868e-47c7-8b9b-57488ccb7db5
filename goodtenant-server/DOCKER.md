# Docker Setup for GoodTenant Server

This document explains how to use Docker for development and production environments.

## 🚀 Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Environment variables configured (see `.env.example` for reference)

## 📁 Environment Files

1. **Development**: `.env.development`
   - Used for local development
   - Connect to external database at `***********`
   - Includes development API keys

2. **Production**: `.env.production`
   - Used for production deployment
   - Uses internal Docker network for database
   - Requires production API keys and secrets

## 🛠 Available Commands

### Start Development Environment
```bash
# Using external database
NODE_ENV=development docker-compose up --build

# Or with local database (update DB_HOST in .env.development to 'db-development' first)
# NODE_ENV=development docker-compose -f docker-compose.yml up --build
```

### Start Production Environment
```bash
# Build and start in detached mode
NODE_ENV=production docker-compose up --build -d

# View logs
NODE_ENV=production docker-compose logs -f
```

### Stop Environment
```bash
# Stop development
NODE_ENV=development docker-compose down

# Stop production
NODE_ENV=production docker-compose down

# Remove volumes (caution: deletes data)
NODE_ENV=production docker-compose down -v
```

## 🔌 Access Points

### Development
- **API**: http://localhost:4000
- **Database Host**: ***********:3306 (external)
- **Adminer (DB GUI)**: http://localhost:8080

### Production
- **API**: http://your-domain.com:4000
- **Database**: Internal to Docker network
- **Adminer**: http://your-domain.com:8080 (if exposed)
  - Default credentials: root / [DB_ROOT_PASSWORD]

## 🔒 Environment Variables

### Required for Both Environments
```env
# Database
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_HOST=db-host
DB_PORT=3306
DB_SYNC_ENABLED=false

# JWT
JWT_SECRET=your_secure_jwt_secret
JWT_EXPIRES_IN=7d

# Server
PORT=4000
NODE_ENV=development
```

### Development Specific
```env
# External database access
DB_HOST=***********

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4000
```

### Production Specific
```env
# Internal database
DB_HOST=db-production

# CORS - Update with production domains
ALLOWED_ORIGINS=https://your-production-domain.com
```

## 🗄️ Database Management

### Backup Database (Production)
```bash
docker exec goodtenant-db-production sh -c 'exec mysqldump -u$DB_USER -p"$DB_PASSWORD" $DB_NAME' > backup_$(date +%Y%m%d).sql
```

### Restore Database
```bash
cat backup_file.sql | docker exec -i goodtenant-db-production sh -c 'exec mysql -u$DB_USER -p"$DB_PASSWORD" $DB_NAME'
```

## 🛠 Maintenance

### Rebuild Containers
```bash
NODE_ENV=production docker-compose up --build --force-recreate
```

### View Running Containers
```bash
docker ps
docker-compose ps
```

### Clean Up
```bash
# Remove stopped containers, unused networks, and dangling images
docker system prune

# Remove everything (use with caution)
docker system prune -a --volumes

# Remove specific volume
docker volume rm goodtenant-mysql-production-data
```

## 🔄 CI/CD Integration

### Example GitHub Actions
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
        
      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}
          
      - name: Deploy to Production
        run: |
          scp .env.production user@server:/app/.env
          ssh user@server "cd /app && docker-compose pull && docker-compose up -d"
```

## 🔐 Security Notes

1. **Never commit sensitive data** to version control
2. Use **environment variables** for all secrets
3. Rotate your **JWT_SECRET** and **database passwords** regularly
4. Set up proper **firewall rules** for your database
5. Use **HTTPS** in production
6. Regularly update your **dependencies**

## 🆘 Troubleshooting

### Common Issues

1. **Connection refused**
   - Check if the database is running
   - Verify DB_HOST and DB_PORT in .env
   - Check firewall settings

2. **Permission denied**
   - Ensure database user has correct permissions
   - Check file permissions for uploads

3. **Container won't start**
   - Check logs: `docker logs <container_name>`
   - Look for errors in application logs

For additional help, please refer to the project's issue tracker or contact the development team.
